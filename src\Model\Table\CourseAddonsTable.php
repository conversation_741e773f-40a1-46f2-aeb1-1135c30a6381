<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * CourseAddons Model
 *
 * @property \App\Model\Table\CoursesTable&\Cake\ORM\Association\BelongsTo $Courses
 * @property \App\Model\Table\MasterDataTable&\Cake\ORM\Association\BelongsTo $MasterData
 *
 * @method \App\Model\Entity\CourseAddon newEmptyEntity()
 * @method \App\Model\Entity\CourseAddon newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\CourseAddon> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\CourseAddon get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\CourseAddon findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\CourseAddon patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\CourseAddon> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\CourseAddon|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\CourseAddon saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\CourseAddon>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseAddon>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CourseAddon>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseAddon> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CourseAddon>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseAddon>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CourseAddon>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseAddon> deleteManyOrFail(iterable $entities, array $options = [])
 */
class CourseAddonsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('course_addons');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->belongsTo('Courses', [
            'foreignKey' => 'course_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('MasterData', [
            'foreignKey' => 'master_data_id',
            'joinType' => 'INNER',
        ]);

        $this->hasMany('CourseAddonPricing', [
            'foreignKey' => 'addon_id',
            'className' => 'CourseAddonPricing',
            'dependent' => true,
            'cascadeCallbacks' => true
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('course_id')
            ->notEmptyString('course_id');

        $validator
            ->integer('master_data_id')
            ->notEmptyString('master_data_id');

        $validator
            ->scalar('custom_name')
            ->maxLength('custom_name', 255)
            ->allowEmptyString('custom_name');

        $validator
            ->scalar('custom_category')
            ->maxLength('custom_category', 255)
            ->allowEmptyString('custom_category');

        $validator
            ->integer('total_slots')
            ->allowEmptyString('total_slots');

        $validator
            ->integer('booked_slots')
            ->notEmptyString('booked_slots');

        $validator
            ->dateTime('created_at')
            ->notEmptyDateTime('created_at');

        $validator
            ->dateTime('modified_at')
            ->notEmptyDateTime('modified_at');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['course_id'], 'Courses'), ['errorField' => 'course_id']);
        $rules->add($rules->existsIn(['master_data_id'], 'MasterData'), ['errorField' => 'master_data_id']);

        return $rules;
    }
    
}
