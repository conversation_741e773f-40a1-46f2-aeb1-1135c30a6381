 <section class="group-booking grp-booking-body-content">
    <div class="container mx-auto px-4">
      <div class="flex items-center space-x-4 p-6 bg-[] grp-booking-body">
        <!-- Image -->
        <img src="<?= $booking->course->image_url ?>" alt="<?= $booking->course->name ?>"
          class="w-24 h-24 rounded-lg object-cover" />

        <!-- Text Content -->
        <div class="content">
          <!-- Organizer -->
          <p class="text-sm text-[#D87A61] font-medium">@<?= !empty($booking->course->partner) ? $booking->course->partner->name : '' ?></p>

          <!-- Title -->
          <h2 class="text-xl md:text-2xl font-bold text-gray-800">
             <?= !empty($booking->course) ? $booking->course->name : '' ?>
          </h2>

          <!-- Info Row -->
          <div class="flex items-center mt-2 space-x-6 text-sm text-gray-700 time-info hidden sm:block">
            <!-- Time -->
            <div class="flex items-center space-x-1">
              <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" stroke-width="2"
                viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 6v6l4 2"></path>
                <circle cx="12" cy="12" r="10"></circle>
              </svg>
              <span><span class="italic font-semibold"><?= $booking->course->duration_details ?></span></span>
            </div>

            <!-- Language -->
            <div class="flex items-center space-x-1">
              <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" stroke-width="2"
                viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10" />
                <line x1="2" y1="12" x2="22" y2="12" />
                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
              </svg>
              <span class="italic font-semibold"><?=  $booking->course->language ?></span>
            </div>
          </div>
        </div>
      </div>
      <div class="content block sm:hidden ">
        <!-- Info Row -->
        <div class="flex items-center mt-2 space-x-6 text-sm text-gray-700 time-info ">
          <!-- Time -->
          <div class="flex items-center space-x-1">
            <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"
              stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 6v6l4 2"></path>
              <circle cx="12" cy="12" r="10"></circle>
            </svg>
            <span><span class="italic font-semibold"><?= $booking->course->duration_details ?></span>
          </div>

          <!-- Language -->
          <div class="flex items-center space-x-1">
            <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"
              stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10" />
              <line x1="2" y1="12" x2="22" y2="12" />
              <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
            </svg>
            <span class="italic font-semibold"><?=  $booking->course->language ?></span>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section>
    <div class="container mx-auto px-4 py-8 booking-success-content min-h-screen flex items-center justify-center">
      <div class="max-w-sm w-full text-center">
        <!-- Checkmark Badge -->
        <div class="mx-auto w-20 h-20 mb-6 flex items-center justify-center rounded-full ">
          <svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg" fill="none">
            <path fill="#F4BD62"
              d="M60 0c4.2 0 6.4 7.4 10.2 8.2 3.8.9 9-4.5 12.3-2.4 3.3 2 2.6 9.2 5.5 11.6 2.9 2.4 10.3.7 12.1 4.2 1.9 3.6-3.5 8.6-2.4 12.3 1 3.7 8.2 6 8.2 10.2s-7.4 6.4-8.2 10.2c-.9 3.8 4.5 9 2.4 12.3-2 3.3-9.2 2.6-11.6 5.5-2.4 2.9-.7 10.3-4.2 12.1-3.6 1.9-8.6-3.5-12.3-2.4-3.7 1-6 8.2-10.2 8.2s-6.4-7.4-10.2-8.2c-3.8-.9-9 4.5-12.3 2.4-3.3-2-2.6-9.2-5.5-11.6-2.9-2.4-10.3-.7-12.1-4.2-1.9-3.6 3.5-8.6 2.4-12.3-1-3.7-8.2-6-8.2-10.2s7.4-6.4 8.2-10.2c.9-3.8-4.5-9-2.4-12.3 2-3.3 9.2-2.6 11.6-5.5 2.4-2.9.7-10.3 4.2-12.1 3.6-1.9 8.6 3.5 12.3 2.4C53.6 7.4 55.8 0 60 0Z" />
            <path fill="#fff"
              d="M53.5 70.6 81.2 42.8c1.2-1.2 1.2-3.1 0-4.3l-2-2c-1.2-1.2-3.1-1.2-4.3 0L51.4 59.9l-9.3-9.3c-1.2-1.2-3.1-1.2-4.3 0l-2 2c-1.2 1.2-1.2 3.1 0 4.3l13.5 13.5c1.2 1.2 3.1 1.2 4.2.2Z" />
          </svg>

        </div>

        <!-- Text Content -->
        <h1 class="text-xl font-semibold text-black mb-2 title"> <?= $status ?>!</h1>
        <p class="text-gray-700 mb-2 content">Let your practice be a celebration of life.</p>
        <p class="text-gray-700 mb-6 content">
          Booking details sent to
          <span class="font-semibold italic text-black"><?= $booking->billed_to ?></span>
        </p>
        <!-- Buttons -->
        <div class="space-y-3">
            <a class="flex items-center justify-center view-book w-full py-3 bg-[#D87A61] text-white font-semibold rounded-lg" href="<?= $this->Url->build(['controller' => 'Bookings', 'action' => 'index']) ?>">VIEW BOOKING DETAILS</a>
          <button class="flex items-center justify-center download w-full py-3 border border-[#D87A61] text-[#D87A61] font-semibold rounded-lg">
            DOWNLOAD RECEIPT
          </button>
        </div>
      </div>

    </div>
  </section>