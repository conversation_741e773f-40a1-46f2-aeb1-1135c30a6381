<?php
// Quick test to check if callback URL works now
echo "Testing social callback...\n";

// Simulate a callback request
$callback_url = 'http://homevilla.in/login/social/google/callback?code=test123&state=test';

echo "Callback URL: " . $callback_url . "\n";

// Use curl to test the callback
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $callback_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_HEADER, true);

$response = curl_exec($ch);
$httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Status: " . $httpcode . "\n";
echo "Response headers:\n";
echo $response;
?>
