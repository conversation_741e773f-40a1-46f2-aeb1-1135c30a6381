*, *::before, ::after{
    box-sizing: border-box;
}
html,body{
    overflow-x: hidden;
}
#loginWrapper.ios-focused {
  max-width: 90%; /* Adjust as needed */
}
.mobile-view{
    display: block;
}
.desktop-view{
    display: none;
}
.login-container{
    padding-top: 10px;
    padding-bottom: 20px;
}
hr{
    margin: 0.3rem 0 1rem;
}
/* .login-container .left-img-content{
    width: 100%;
} */
.login-container .login-wrapper > img{
    width: 230px;
    margin: 0 auto;
}
.login-container .left-img-content p{
    text-align: center;
    font-size: 22px;
    font-weight: 400;
    font-family: "Open Sans", sans-serif;
}
.login-container .login-wrapper{
    padding-top: 0px;
    padding-bottom: 20px;
}
.login-container .login-wrapper .hr-container {
    position: relative;
}
.login-container .login-wrapper .hr-container .hr {
    border-bottom: 1px solid #000;
    position: absolute;
    top: 11px;
    width: 100%;
}
.login-container .login-wrapper h2{
    /* font-size: 40px;
    line-height: 60px; */
    font-size: 25px;
    line-height: 20px;
    font-weight: 500;
    font-family: "open sans", sans-serif;
    /* margin-bottom: 5px;
    margin-top: 10px; */
    background-color: #fff;
    width: 237px;
    padding: 0 10px;
    position: relative;
    margin: 10px auto 10px;
}
.login-container .login-wrapper img.yoga-logo{
    width: 11%;
    margin: 0 auto;
    height: auto;
}
.login-container .login-wrapper button.btn-noactive{
    color: #293148;
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 4px;
    font-size: 15px;
    font-weight: 400;
    font-family: "Open Sans", sans-serif;
    /* width: 55%; */
}
.login-container .login-wrapper button.btn-noactive:first-child{
    width: 55%;
    padding-left: 0px !important;
    padding-right: 0px !important;
}
.login-container .login-wrapper button.btn-noactive:last-child{
    width: 45%;
}
.login-container .login-wrapper button.active{
    color: #fff;
    background-color: #d87a61;
    border: 1px solid #d87a61;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 600;
    font-family: "Open Sans", sans-serif;
    padding-left: 0px !important;
    padding-right: 0px !important;
    /* width: 45%; */
}
.login-container .login-wrapper .first-tab{
    height: auto;
}
.login-container .login-wrapper .second-tab{
    height: auto;
}
.login-form .form-container .form-field span.mobile{
    display: block;
}
.login-form .form-container .radio-option-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    margin-top: 10px;
    margin-bottom: 10px;
}
.login-form .form-container .radio-option-container .radio-email {
    width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.login-form .form-container .radio-option-container .radio-email > input[type="radio"] {
    width: 20px;
    height: 20px;
    margin-right: 5px;
}
.login-form .form-container .radio-option-container .radio-email > input[type="radio"]:checked {
    accent-color: #C45F44;
}
.login-form .form-container .radio-option-container .radio-phone {
    width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.login-form .form-container .radio-option-container .radio-phone > input[type="radio"] {
    width: 20px;
    height: 20px;
    margin-right: 5px;
}
.login-form .form-container .radio-option-container .radio-phone > input[type="radio"]:checked {
    accent-color: #C45F44;
}
.login-form .form-container .form-field span.mobile select.code {
    position: absolute;
    /* top: 25px; */
    height: 42px;
    border: 1px solid #000;
    width: 25%;
    z-index: 1;
    border-top-left-radius: 7px;
    border-bottom-left-radius: 7px;
    border-color: #6C6C6C;
}
.login-form .form-container .form-field span.mobile > input{
    width: 75%;
    position: relative;
    left: 25%;
    top: 0;
    height: 43px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 7px;
    border-bottom-right-radius: 7px;
    border-left: 0;
    border-color: #6C6C6C;
}
.login-form .form-container .eye-icon{
    width: 35px;
    padding: 0px 7px;
    position: absolute;
    top: 31px;
    height: 29px;
    right: 5px;
    display: flex;
    align-items: center;
    color: rgba(41, 49, 72, .6);
    font-size: 16px;
}
.login-form .form-container .form-field{
    margin-bottom: 10px;
}
.login-form .form-container .form-field label{
    font-size: 16px;
    font-weight: 400;
    font-family: "Open Sans", sans-serif;
    color: #293148;
}
.login-form .form-container .form-field input.form-control{
    font-size: 14px;
    font-weight: 600;
    font-family: "Open Sans", sans-serif;
    color: #293148;
    border-color: #6C6C6C;
    border-radius: 10px;
    height: 43px;
}
/* .login-form .form-container .form-field input.form-control::placeholder{
    color: #c7cee2;
} */
.login-form .form-container .form-field .email{
    font-size: 14px;
    font-weight: 600;
    font-family: "Open Sans", sans-serif;
    color: #293148;
    width: 66%;
    margin-right: 10px;
}
.login-form .form-container .form-field .remember{
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.login-form .form-container .form-field .remember a{
    font-size: 14px;
    font-weight: 600;
    font-family: "Open Sans", sans-serif;
    text-decoration: none;
    color: #293148;
}
.login-form .form-container button.btn-login{
    width: 100%;
    height: 43px;
    border-radius: 7px;
    background-color: #D87A61;
    padding: 10px 15px 10px;
    color: #fff;
    font-size: 15px;
    font-weight: 600;
    font-family: "Open Sans", sans-serif;
    margin-top: 8px;
}
.login-container .login-wrapper .new-account{
    /* font-size: 14px;
    font-weight: 400;
    font-family: "Open Sans", sans-serif;
    color: #293148;
    margin-top: 10px;
    margin-bottom: 10px; */
    border: 1px solid #D87A61;
    border-radius: 7px;
    width: 87%;
    padding-top: 10px;
    padding-bottom: 10px;
    font-size: 16px;
    font-weight: 700;
    line-height: normal;
    font-family: "Open Sans", sans-serif;
}
.login-container .login-wrapper .new-account > a{
    /* font-size: 14px;
    font-weight: 600;
    font-family: "Open Sans", sans-serif;
    /* color: #293148; */
    /*color: #0000ff;
    text-decoration: none; */
    font-size: 16px;
    font-weight: 700;
    font-family: "Open Sans", sans-serif;
    text-decoration: none;
    color: #D87A61;
}
.login-container .login-wrapper p.terms{
    font-size: 12px;
    font-weight: 400;
    font-family: "Open Sans", sans-serif;
    color: #293148;
    margin-bottom: 0;
    padding: 0;
    /* text-align: center; */
    margin-top: 10px;
}
/* .login-container .form-container p.small-text > input{
    margin-right: 5px;
} */
.login-container .login-wrapper p.terms > label {
    display: flex;
    align-items: flex-start;
}
.login-container .login-wrapper p.terms input[type="checkbox"]{
    margin-right: 5px;
    margin-top: 2px;
}
.login-container .login-wrapper p.terms a{
    font-size: 12px;
    line-height: normal;
    font-weight: 700;
    font-family: "Open Sans", sans-serif;
    color: #293148;
    text-decoration: none;
}
/* OTP form Style */
.login-form .form-container .otp-wrapper {
    margin-bottom: 20px;
}
.login-form .form-container .otp-wrapper label {
    margin-bottom: 5px;
}
.login-form .form-container .otp-wrapper button.btn-login {
    width: 100%;
    height: 46px;
    border-radius: 29px;
    background-color: #D87A61;
    padding: 5px 15px 6px;
    color: #fff;
    font-size: 15px;
    font-weight: 600;
    font-family: "Open Sans", sans-serif;
}
.login-container .login-form .otp-wrapper .otp-sent-msg{
    color: #293148;
    font-size: 13px;
    font-weight: 500;
    font-family: "Open Sans", sans-serif;
    margin-bottom: 0;
}
.login-container .login-form .otp-wrapper .otp-msg{
    display: flex;
    align-items: center;
    /* flex-wrap: wrap; */
}
.login-container .login-form .otp-wrapper .otp-msg + p{
    margin-top: 5px;
    margin-bottom: 0px;
}
.login-container .login-form .otp-wrapper .btn-resend{
    /* border: 1px solid #293148; */
    outline: 1px solid #293148;
    border-radius: 29px;
    padding: 3px 4px;
    margin-top: 0px;
    margin-bottom: 0px;
    font-size: 13px;
    font-family: "Open Sans", sans-serif;
}
.login-container .login-form .otp-wrapper .otp-msg .form-otp{
    /* width: 40px;
    margin-right: 15px; */
    width: 22%;
    margin-right: 12px;
    padding: 0px 25px;
}
.login-container .login-form .otp-wrapper .otp-msg .form-otp:last-child{
    margin-right: 0;
}
.login-container .login-form .otp-wrapper .resend-span {
    text-align: center;
    display: block;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 400;
    line-height: normal;
    font-family: "Open Sans", sans-serif;
    margin-top: 9px;
}
.login-container .login-form .otp-wrapper .email-otp-verify{
    margin-top: 20px;
    margin-bottom: 10px;
}
.login-container .login-form .otp-wrapper button.btn-link {
    color: #C45F44;
    font-size: 16px;
    font-weight: 700;
    font-family: "Open Sans", sans-serif;
    text-decoration: none;
}
.login-container .login-form .otp-wrapper .verify-msg {
    display: block;
    text-align: center;
    font-size: 12px;
    line-height: normal;
    font-weight: 600;
    font-family: "Open Sans", sans-serif;
}
.login-container .login-form .otp-wrapper .verify-msg .verify-mail {
    font-weight: 700;
}
.login-form .form-container button.btn-otp {
    width: 100%;
    height: 43px;
    border-radius: 8px;
    background-color: #D87A61;
    padding: 5px 15px 6px;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    font-family: "Open Sans", sans-serif;
}
.login-container .form-container button.btn-send-otp{
    border: 1px solid #D87A61;
    background-color: #D87A61;
    border-radius: 4px;
    /* padding: 5px 15px; */
    margin-top: 20px;
    /* margin-bottom: 10px; */
    color: #fff;
    width: 100%;
    height: 37px;
    /* margin-top: 0;
    margin-bottom: 0; */
    font-size: 13px;
    font-family: "Open Sans", sans-serif;
}
/* Style for divider and social media login */
.login-container .login-wrapper .divider-container{
    position: relative;
}
.login-container .login-wrapper .divider{
    position: absolute;
    bottom: 4px;
    left: 50%;
    margin-left: -23px;
    display: block;
    padding: 2px 15px;
    background: #fff;
    font-size: 14px;
    font-weight: 400;
    font-family: "Open Sans", sans-serif;
}
.social-media{
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 10px !important;
}
.login-container .login-wrapper .social-media .google{
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #d87a61;
    border-radius: 8px;
    padding: 6px;
    margin-right: 16px;
    width: 70px;
    margin-bottom: 0px;
}
.login-container .login-wrapper .social-media .google img{
    width: 18px;
    height: 18px;
    margin-right: 0px;
    margin-left: 0px;
}
.login-container .login-wrapper .social-media .fb{
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #d87a61;
    border-radius: 8px;
    padding: 6px;
    margin-left: 0px;
    width: 70px;
}
.login-container .login-wrapper .social-media .fb img{
    width: 18px;
    height: 19px;
    margin-right: 0px;
    margin-left: 0px;
}
.login-container .login-form .otp-wrapper #otp-timer {
    font-size: 16px;
    font-weight: 600;
    font-family: "Poppins", sans-serif;
    margin-bottom: 6px;
    margin-top: 9px;
    text-align: center;
}
@media screen and (min-width:992px) {
    .mobile-view{
        display: none;
    }
    .desktop-view{
        display: flex;
    }
    .login-container{
        padding-top: 10px;
        padding-bottom: 10px;
    }
    .login-container .left-img-content{
        width: 40%;
        padding-right: 30px;
        padding-left: 30px;
        background: url(../../img/bg-image.png);
        /* align-items: center; */
        flex-direction: column;
    }
    .login-container .left-img-content img{
        width: 100%;
        margin: 0 auto;
        object-fit: cover;
        height: 390px;
        border-radius: 10px;
    }
    .login-container .left-img-content > a img{
        width: 11%;
        margin: 0;
        height: auto;
    }
    .login-container .left-img-content .img-container{
        margin-top: 40px;
    }
    /* .login-container .left-img-content p{
        text-align: center;
        font-size: 22px;
        font-weight: 400;
        font-family: "Open Sans", sans-serif;
    } */
    .login-container .login-wrapper{
        padding-top: 0px;
        position: relative;
        width: 39%;
        padding-bottom: 20px;
        scrollbar-width: none;
    }
    .login-container .login-wrapper img.yoga-logo{
        width: 15%;
        height: auto;
        margin: 0 auto;
    }
    .login-container .login-wrapper .hr-container .hr {
        border-bottom: 1px solid #000;
        position: absolute;
        top: 17px;
        width: 100%;
    }
    .login-container .login-wrapper h2{
        font-size: 30px;
        line-height: 32px;
        font-weight: 500;
        font-family: "open sans", sans-serif;
        /* margin-bottom: 2px;
        margin-top: 0px; */
        background-color: #fff;
        width: 280px;
        padding: 0 10px;
        position: relative;
        margin: 10px auto 10px;
    }
    .login-container .login-wrapper button.btn-noactive{
        color: #293148;
        background-color: transparent;
        border: 1px solid transparent;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 400;
        font-family: "Open Sans", sans-serif;
        width: 50%;
    }
    .login-container .login-wrapper button.active{
        color: #fff;
        background-color: #d87a61;
        border: 1px solid #d87a61;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
    }
    /* .login-container .login-wrapper .first-tab{
        height: 320px;
    }
    .login-container .login-wrapper .second-tab{
        height: 340px;
    } */
    .login-form .form-container .eye-icon{
        width: 35px;
        padding: 0px 7px;
        position: absolute;
        top: 35px;
        height: 18px;
        right: 5px;
        display: flex;
        align-items: center;
        color: rgba(41, 49, 72, .6);
        font-size: 16px;
    }
    .login-form .form-container .form-field{
        margin-bottom: 10px;
    }
    .login-form .form-container .form-field label{
        font-size: 14px;
        font-weight: 400;
        font-family: "Open Sans", sans-serif;
        color: #293148;
    }
    .login-form .form-container .form-field input.form-control{
        font-size: 14px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
        color: #293148;
        height: 39px;
    }
    .login-form .form-container .form-field span.mobile > input {
        width: 75%;
        position: relative;
        left: 25%;
        top: 0;
        height: 39px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-top-right-radius: 7px;
        border-bottom-right-radius: 7px;
        border-left: 0;
        border-color: #6C6C6C;
    }
    .login-form .form-container .form-field span.mobile select.code {
        position: absolute;
        top: 24px;
        height: 39px;
        border: 1px solid #000;
        width: 25%;
        z-index: 1;
        border-top-left-radius: 7px;
        border-bottom-left-radius: 7px;
        border-color: #6C6C6C;
    }
    .login-form .form-container .form-field input.form-control::placeholder{
        color: #c7cee2;
    }
    .login-form .form-container .form-field .email{
        font-size: 14px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
        color: #293148;
        width: 65%;
        margin-right: 10px;
    }
    .login-form .form-container .form-field .remember{
        margin-top: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .login-form .form-container .form-field .remember a{
        font-size: 14px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
        text-decoration: none;
        color: #293148;
    }
    .login-form .form-container button.btn-login{
        width: 100%;
        height: 39px;
        border-radius: 8px;
        background-color: #D87A61;
        padding: 6px 15px 10px;
        color: #fff;
        font-size: 15px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
        margin-top: 0px;
    }
    .login-container .form-container p.new-account{
        font-size: 14px;
        font-weight: 400;
        font-family: "Open Sans", sans-serif;
        color: #293148;
        margin-top: 10px;
        margin-bottom: 10px;
    }
    .login-container .form-container p.new-account > a{
        font-size: 14px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
        /* color: #293148; */
        color: #0000ff;
        text-decoration: none;
    }
    .login-container .form-container p.small-text{
        font-size: 11px;
        font-weight: 400;
        font-family: "Open Sans", sans-serif;
        color: #293148;
        margin-bottom: 0;
    }
    .login-container .form-container p.small-text > label.flex{
        display: flex !important;
        align-items: center;
    }
    .login-container .form-container p.small-text input[type="checkbox"]{
        margin-right: 5px;
    }
    .login-container .form-container p.small-text a{
        font-size: 11px;
        font-weight: 500;
        font-family: "Open Sans", sans-serif;
        color: #293148;
        text-decoration: none;
    }
    /* OTP form Style */
    .login-form .form-container .otp-wrapper {
    margin-bottom: 10px;
    }
    .login-form .form-container .otp-wrapper label {
        margin-bottom: 6px;
    }
    .login-form .form-container .otp-wrapper button.btn-login {
        width: 111px;
        height: 46px;
        border-radius: 29px;
        background-color: #D87A61;
        padding: 5px 15px 6px;
        color: #fff;
        font-size: 15px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
    }
    .login-container .login-form .otp-wrapper .otp-sent-msg{
        color: #293148;
        font-size: 13px;
        font-weight: 500;
        font-family: "Open Sans", sans-serif;
        margin-bottom: 0px;
    }
    .login-container .login-form .otp-wrapper .otp-msg{
        display: flex;
        align-items: center;
    }
    .login-container .login-form .otp-wrapper .otp-msg + p{
        margin-top: 10px;
        margin-bottom: 10px;
    }
    .login-container .login-form .otp-wrapper .otp-msg .btn-resend{
        border: 1px solid #293148;
        border-radius: 29px;
        padding: 5px 15px;
        margin-top: 0;
        margin-bottom: 0;
        font-size: 14px;
        font-weight: 400;
        font-family: "Open Sans", sans-serif;
    }
    .login-container .login-form .otp-wrapper .otp-msg .form-otp{
        width: 22%;
        height: 35px;
        margin-right: 15px;
        padding: 0px 28px;
    }
    .login-form .form-container button.btn-otp {
        width: 100%;
        height: 39px;
        border-radius: 7px;
        background-color: #D87A61;
        padding: 5px 15px 6px;
        color: #fff;
        font-size: 15px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
    }
    .login-container .form-container button.btn-send-otp{
        border: 1px solid #D87A61;
        background-color: #D87A61;
        border-radius: 4px;
        /* padding: 5px 15px; */
        margin-top: 20px;
        margin-bottom: 10px;
        color: #fff;
        font-size: 15px;
        font-weight: 400;
        font-family: "Open Sans", sans-serif;
        width: 35%;
        height: 30px;
        margin-top: 0;
        margin-bottom: 0;
    }
    .login-container .login-form .otp-wrapper .btn-resend + p {
        margin-top: 3px;
        margin-bottom: 0px;
    }

    /* Style for divider and social media login */
    hr:not([size]) {
        height: 1px;
        margin: 0.5rem 0 1rem;
    }
    .login-container .login-wrapper .divider{
        position: absolute;
        bottom: -2px;
        left: 50%;
        margin-left: -17px;
        display: block;
        padding: 0px 5px;
        background: #fff;
        font-size: 14px;
        font-weight: 400;
        font-family: "Open Sans", sans-serif;
    }
    .social-media{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .login-container .login-wrapper .social-media .google{
        font-size: 16px;
        font-weight: 500;
        font-family: "Open Sans", sans-serif;
        display: flex;
        align-items: center;
        border: 1px solid #d87a61;
        border-radius: 4px;
        padding: 0px;
        margin-right: 4px;
        margin-bottom: 0;
        width: 70px;
        height: 34px;
        border-radius: 8px;
    }
    .login-container .login-wrapper .social-media .google img{
        width: 18px;
        height: 18px;
        margin-right: 0px;
        margin-left: 4px;
    }
    .login-container .login-wrapper .social-media .fb{
        display: flex;
        align-items: center;
        border: 1px solid #d87a61;
        border-radius: 4px;
        padding: 0px;
        margin-left: 4px;
        width: 70px;
        height: 34px;
        border-radius: 8px;
    }
    .login-container .login-wrapper .social-media .fb img{
        width: 18px;
        height: 19px;
        margin-right: 0px;
        margin-left: 0px;
    }
    .login-container .login-wrapper .new-account {
        border: 1px solid #D87A61;
        border-radius: 7px;
        width: 87%;
        padding-top: 8px;
        padding-bottom: 8px;
        font-size: 16px;
        font-weight: 700;
        line-height: normal;
        font-family: "Open Sans", sans-serif;
    }
}
@media screen and (min-width: 1536px) {
    .login-container .left-img-content img {
        object-position: 0px;
    }
}