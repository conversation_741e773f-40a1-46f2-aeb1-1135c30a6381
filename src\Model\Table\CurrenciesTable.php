<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * Currencies Model
 *
 * @property \App\Model\Table\ClassPricesTable&\Cake\ORM\Association\HasMany $ClassPrices
 * @property \App\Model\Table\ClassesTable&\Cake\ORM\Association\HasMany $Classes
 * @property \App\Model\Table\CoursesTable&\Cake\ORM\Association\HasMany $Courses
 *
 * @method \App\Model\Entity\Currency newEmptyEntity()
 * @method \App\Model\Entity\Currency newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Currency> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Currency get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Currency findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Currency patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Currency> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Currency|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Currency saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Currency>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Currency>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Currency>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Currency> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Currency>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Currency>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Currency>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Currency> deleteManyOrFail(iterable $entities, array $options = [])
 */
class CurrenciesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('currencies');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->hasMany('ClassPrices', [
            'foreignKey' => 'currency_id',
        ]);
        $this->hasMany('Classes', [
            'foreignKey' => 'currency_id',
        ]);
        $this->hasMany('Courses', [
            'foreignKey' => 'currency_id',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('name')
            ->maxLength('name', 100)
            ->requirePresence('name', 'create')
            ->notEmptyString('name')
            ->add('name', 'unique', ['rule' => 'validateUnique', 'provider' => 'table']);

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        $validator
            ->dateTime('created_at')
            ->notEmptyDateTime('created_at');

        $validator
            ->dateTime('modified_at')
            ->notEmptyDateTime('modified_at');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->isUnique(['name']), ['errorField' => 'name']);

        return $rules;
    }

    public function getAll(){
        return $this->find()->all();
    }

    public function getList(){
        return $this->find('list')
            ->where(['Currencies.status' => 'A'])
            ->toArray(); // Convert to an array explicitly
    }

     public function getForeignCurrencies(){
        return $this->find('list')
            ->where(['Currencies.status' => 'A'])
            ->where(['Currencies.name !=' => 'INR'])
            ->toArray(); // Convert to an array explicitly
    }
}
