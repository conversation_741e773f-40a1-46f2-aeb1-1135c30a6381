<section class="login-container min-h-screen flex items-center justify-center bg-[#FFEFE9] px-4 py-6">
    <div class="w-full max-w-md lg:max-w-[55%]">
        <div class="block md:flex md:items-normal justify-center lg:h-[91vh]" id="loginWrapper">
            <!-- Card 1 -->
            <div class="bg-white rounded-lg rounded-tr-none roundedbr-none left-img-content hidden lg:flex lg:flex-col lg:justify-between lg:strciky lg:top:0 w-[60%] relative">
                <!-- <img src="<?= $this->Url->webroot('img/yoga-register.png') ?>" alt="Login Yoga image" /> -->
                 <a href="/" class="flex items-center justify-center">
                    <img src="<?= $this->Url->webroot('img/yoga-big.png') ?>" alt="Yoga logo" class="yoga-logo h-[78px]">
                </a>
                <div class="img-container absolute top-[30%] w-full px-[20px]">
                    <img src="/img/login-img.png" alt="Login image" class="login-img w-full h-[250px] rounded-[8px] object-cover">
                </div>
            </div>

            <div class="inline-block bg-white rounded-xl rounded-tl-none rounded-bl-none border-l-1 border-[#CFDFE2] shadow-lg lg:overflow-y-auto lg:w-[60%] lg:max-h-[91vh]">
                <div class="px-0 pt-2 pb-1 text-center">
                    <a href="/" class="inline-block mb-2 lg:hidden"><img src="<?= $this->Url->webroot('img/yoga-big.png') ?>" alt="Yoga logo" class="h-[78px]"></a>
                    <div class="flex items-center mb-2">
                        <div class="flex-1 border-t border-[#CFDFE2]"></div>
                        <h2 class="px-2 text-[24px] font-[600] text-[#000] font-[Open_Sans]">Create an account</h2>
                        <div class="flex-1 border-t border-[#CFDFE2]"></div>
                    </div>
                    <div class="flex gap-3 justify-center mb-2">
                        <button id="google-signin-btn" class="flex items-center justify-center rounded-[7px] border border-[#c05e47] px-[25px] py-1">
                            <img src="<?= $this->Url->webroot('img/google-icon.png') ?>" alt="Google Icon" class="w-[28px] h-[28px] rounded-full">
                        </button>
                        <button class="flex items-center justify-center rounded-[7px] border border-[#c05e47] px-[25px] py-1">
                            <img src="<?= $this->Url->webroot('img/facebook-icon.png') ?>" alt="Facebook Icon" class="w-[28px] h-[28px] rounded-full">
                        </button>
                    </div>
                </div>
                <div x-data="{ tab: 'first' }" class="px-6 pb-2">

                    <!-- Tabs -->
                    <div class="flex mb-2 bg-[#FFEFE9] rounded-[8px] px-[6px] py-[4px]">
                        <button @click="tab = 'first'" :class="tab === 'first' ? 'border-b-2 border-[#d87a61] bg-[#D87A61] text-[#fff]' : 'text-[#293148] bg-[transparent]'" class="px-2 py-1 focus:outline-none flex-1 rounded-[7px] font-medium transition-all duration-200 text-sm">With Password</button>
                        <button @click="tab = 'second'" :class="tab === 'second' ? 'border-b-2 border-[#d87a61] bg-[#D87A61] text-[#fff]' : 'text-[#293148] bg-[transparent]'" class="px-2 py-1 focus:outline-none flex-1 rounded-[7px] font-medium transition-all duration-200 text-sm">With OTP</button>
                    </div>

                    <!-- Tab Panels -->
                    <div x-show="tab === 'first'" class="first-tab">
                        <form id="password-signup-form" class="login-form" method="post">
                            <?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')) ?>
                            <div class="form-container">
                                <div class="form-wrapper">
                                    <div class="relative form-field mt-2">
                                        <label for="firstName" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">First Name <span class="text-red-600">*</span></label>
                                        <input type="text" name="first_name" id="firstName" minlength="3" maxlength="50" class="form-control w-full h-[40px] rounded-[7px] border border-[#6C6C6C] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] px-3" id="input" required onkeypress="validString(event);" />
                                    </div>
                                    <div class="relative form-field mt-2">
                                        <label for="lastName" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">Last Name</label>
                                        <input type="text" name="last_name" id="lastName" class="form-control w-full h-[40px] rounded-[7px] border border-[#6C6C6C] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] px-3" id="input" onkeypress="validString(event);" />
                                    </div>
                                    <div class="relative form-field mt-2">
                                        <label for="email_id" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">Email ID</label>
                                        <input type="text" name="email_id" id="email_id" class="form-control w-full h-[40px] rounded-[7px] border border-[#6C6C6C] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] px-3" placeholder="" />
                                    </div>
                                    <div class="relative form-field mt-2">
                                        <label for="mobile" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">Mobile No</label>
                                        <span class="mobile flex items-center">
                                            <select name="country_code" class="code w-[25%] lg:w-[28%] h-[40px] text-[14px] font-[600] font-[Open_Sans] rounded-[7px] rounded-tr-none rounded-br-none border border-[#6C6C6C] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] px-0 text-center">
                                                <option value="1">+1</option>
                                                <option value="7">+7</option>
                                                <option value="43">+43</option>
                                                <option value="44">+44</option>
                                                <option value="60">+60</option>
                                                <option value="65">+65</option>
                                                <option selected value="91">+91</option>
                                                <option value="92">+92</option>
                                                <option value="1 648">******</option>
                                            </select>
                                            <input type="text" name="email_or_mobile" id="mobile" maxlength="10" class="form-control w-[75%] lg:w-[72%] h-[40px] rounded-[7px] rounded-tl-none rounded-bl-none border border-l-0 border-[#6C6C6C] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] px-3" required onkeypress="validNumber(event);" />
                                        </span>
                                    </div>
                                    
                                    <div class="relative form-field mt-2">
                                        <label for="password" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">Create Password <span class="text-red-600">*</span></label>
                                        <input type="password" name="password" id="password" class="form-control password w-full h-[40px] rounded-[7px] border border-[#6C6C6C] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] px-3" required />
                                        <!-- <div class="eye-icon">
                                            <i class="fas fa-eye-slash"></i>
                                        </div> -->
                                        <button type="button" class="eye-toggle absolute inset-y-0 top-[30px] end-0 flex items-center z-20 px-3 cursor-pointer text-gray-400 rounded-e-md focus:outline-hidden focus:text-gray-600 dark:text-neutral-600 dark:focus:text-blue-500 h-[30px]">
                                            <svg id="eyeIcon" class="shrink-0 size-3.5" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-password-active:hidden" d="M9.88 9.88a3 3 0 1 0 4.24 4.24"></path>
                                                <path class="hs-password-active:hidden" d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"></path>
                                                <path class="hs-password-active:hidden" d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"></path>
                                                <line class="hs-password-active:hidden" x1="2" x2="22" y1="2" y2="22"></line>
                                                <path class="hidden hs-password-active:block" d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                                <circle class="hidden hs-password-active:block" cx="12" cy="12" r="3"></circle>
                                            </svg>
                                        </button>
                                        <div class="password-requirements block text-[10px] font-[500] text-[#231F20] font-[Open_Sans] mt-1">
                                            Password must be 8-20 characters and include uppercase, lowercase, number, and special character.
                                        </div>
                                    </div>
                                    <div class="relative form-field mt-2">
                                        <label for="rePassword" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">Re-enter Password <span class="text-red-600">*</span></label>
                                        <input type="password" name="re_password" id="rePassword" class="form-control password w-full h-[40px] rounded-[7px] border border-[#6C6C6C] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] px-3" id="input" required />
                                        <!-- <div class="eye-icon">
                                            <i class="fas fa-eye-slash"></i>
                                        </div> -->
                                        <button type="button" class="eye-confirm-toggle absolute inset-y-0 top-[30px] end-0 flex items-center z-20 px-3 cursor-pointer text-gray-400 rounded-e-md focus:outline-hidden focus:text-gray-600 dark:text-neutral-600 dark:focus:text-blue-500 h-[30px]">
                                            <svg id="eyeConfirmIcon" class="shrink-0 size-3.5" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-password-active:hidden" d="M9.88 9.88a3 3 0 1 0 4.24 4.24"></path>
                                                <path class="hs-password-active:hidden" d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"></path>
                                                <path class="hs-password-active:hidden" d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"></path>
                                                <line class="hs-password-active:hidden" x1="2" x2="22" y1="2" y2="22"></line>
                                                <path class="hidden hs-password-active:block" d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                                <circle class="hidden hs-password-active:block" cx="12" cy="12" r="3"></circle>
                                            </svg>
                                        </button>
                                        <!-- <div class="password-requirements text-xs text-gray-600 mt-1">
                                            Password is not matched.
                                        </div> -->
                                    </div>
                                </div>
                                
                                <button type="button" class="btn-login w-full h-[40px] rounded-[8px] bg-[#D87A61] text-white text-[14px] font-[600] font-[Open_Sans] mt-3" onclick="sendOTP()">Send OTP</button>
                                <div class="relative form-field otp-wrapper">
                                    <form class="otp-form" @submit.prevent="console.log('Form submitted');">
                                        <div class="mobile-otp-verify">
                                            <p class="otp-sent-msg text-success"></p>
                                            <div class="flex items-center justify-between">
                                                <label class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">Mobile Verification Code <span class="text-red-600">*</span></label>
                                                <span class="text-[14px] font-[600] text-[#BE1F2E] font-[Open_Sans]" id="mobile-otp-timer">05:00</span>
                                            </div>
                                            <div class="otp-msg">
                                                <input type="text" class="form-control form-otp w-[22%] h-[38px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                                <input type="text" class="form-control form-otp w-[22%] h-[38px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                                <input type="text" class="form-control form-otp w-[22%] h-[38px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                                <input type="text" class="form-control form-otp w-[22%] h-[38px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                            </div>
                                            <span class="resend-span block text-center mb-2 text-[12px] font-[400] mt-2 font-[Open_Sans]">Didn't receive a code? <button type="button" class="btn-link text-[#C45F44] text-[12px] font-[700] font-[Open_Sans] resend-otp-btn" id="mobile-resend-otp-btn" disabled>Resend OTP</button></span>
                                            <span class="verify-msg block text-center text-[11px] font-[600] font-[Open_Sans]" id="mobile-verify-line">Verification code sent to <span class="verify-mail font-[700]" id="mobile-verify-msg"></span></span>
                                            <!-- <button type="submit" class="btn-resend mobile-view disabled:cursor-not-allowed" disabled>Resend OTP</button> -->
                                        </div>
                                        <div class="email-otp-verify mt-3">
                                            <p class="otp-sent-msg text-success"></p>
                                            <div class="flex items-center justify-between">
                                                <label class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">Email Verification Code <span class="text-red-600">*</span></label>
                                                <span class="text-[14px] font-[600] text-[#BE1F2E] font-[Open_Sans]" id="email-otp-timer">05:00</span>
                                            </div>
                                            <div class="otp-msg">
                                                <input type="text" class="form-control form-otp w-[22%] h-[38px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                                <input type="text" class="form-control form-otp w-[22%] h-[38px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                                <input type="text" class="form-control form-otp w-[22%] h-[38px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                                <input type="text" class="form-control form-otp w-[22%] h-[38px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                            </div>
                                            <span class="resend-span block text-center mb-2 text-[12px] font-[400] mt-2 font-[Open_Sans]">Didn't receive a code? <button type="button" class="btn-link text-[#C45F44] text-[12px] font-[700] font-[Open_Sans] resend-otp-btn" id="email-resend-otp-btn" disabled>Resend OTP</button></span>
                                            <span class="verify-msg block text-center text-[11px] font-[600] font-[Open_Sans]" id="email-verify-line" style="display: none;">Verification code sent to <span class="verify-mail font-[700]" id="email-verify-msg"></span></span>
                                            <!-- <button type="submit" class="btn-resend mobile-view disabled:cursor-not-allowed" disabled>Resend OTP</button> -->
                                            
                                            <!-- Terms and Conditions -->
                                            <div class="terms-section mt-3 mb-2">
                                                <label class="flex items-start gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-[#D87A61] transition-colors cursor-pointer">
                                                    <input type="checkbox" name="terms_accepted" class="mt-1 w-4 h-4 text-[#D87A61] bg-white border-2 border-gray-300 rounded focus:ring-[#D87A61] focus:ring-2 transition-all" required /> 
                                                    <span class="text-[13px] font-[400] text-[#4B5563] leading-relaxed font-[Open_Sans]">
                                                        I agree to the <a href="<?= h($configSettings['WP_TERMS_CONDITIONS']) ?>" target="_blank" class="text-[#D87A61] font-[600] hover:text-[#C45F44] hover:underline transition-colors">Terms & Conditions</a> and <a href="<?= h($configSettings['WP_PRIVACY_POLICY']) ?>" target="_blank" class="text-[#D87A61] font-[600] hover:text-[#C45F44] hover:underline transition-colors">Privacy Policy</a> of yoga.in
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </form>
                                    <button class="btn-otp w-full h-[40px] rounded-[8px] bg-[#D87A61] text-white text-[14px] font-[600] font-[Open_Sans] mt-3">Create Account</button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div x-show="tab === 'second'" class="second-tab">
                        <div class="login-form">
                            <div class="form-container">
                                <!-- <div class="relative form-field">
                                    <label>First Name</label>   
                                    <input type="text" name="first_name" minlength="3" maxlength="50" class="form-control" id="input" required />
                                </div>
                                <div class="relative form-field">
                                    <label>Email ID / Mobile Number</label>
                                    <div class="flex items-center gap-2">
                                        <input type="text" name="email" class="form-control email" id="input" required />
                                        <button id="send-otp-btn" type="button" class="btn-send-otp">Send OTP</button>
                                    </div>
                                </div> -->
                                <div class="relative form-field otp-wrapper">
                                    <div class="radio-option-container flex gap-6 justify-center mb-2">
                                        <div class="radio-email flex items-center gap-2">
                                            <input type="radio" id="email" name="login" value="email" checked  onclick="checkRadio(id)" class="w-4 h-4 text-[#D87A61] border-gray-300 focus:ring-[#D87A61] accent-[#D87A61]">
                                            <label for="email" class="cursor-pointer select-none text-[14px] font-[400] font-[Open_Sans]">Email</span>
                                        </div>
                                        <div class="radio-phone flex items-center">
                                            <input type="radio" id="phone" name="login" value="phone" onclick="checkRadio(id)" class="w-4 h-4 text-[#D87A61] border-gray-300 focus:ring-[#D87A61] accent-[#D87A61] mr-2">
                                            <label for="phone" class="cursor-pointer select-none text-[14px] font-[400] font-[Open_Sans]">Mobile No</label>
                                        </div>
                                    </div>
                                    <form class="otp-form-email" @submit.prevent="console.log('Form submitted');">
                                        <div class="email-otp-form">
                                            <div class="relative form-field">
                                                <label for="firstName" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">First Name <span class="text-red-600">*</span></label>
                                                <input type="text" name="first_name" id="firstName" class="form-control first-name w-full h-[42px] rounded-[7px] border border-[#6C6C6C] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] px-3" required />
                                                <label for="lastName" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">Last Name <span class="text-[12px] text-gray-500">(optional)</span></label>
                                                <input type="text" name="last_name" id="lastName" class="form-control last-name w-full h-[42px] rounded-[7px] border border-[#6C6C6C] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] px-3" />
                                                <label for="email" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">Eamil ID <span class="text-red-600">*</span></label>
                                                <input type="text" name="email" id="email" class="form-control email-id w-full h-[42px] rounded-[7px] border border-[#6C6C6C] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] px-3" required />
                                                <button type="submit" class="btn-login otp-email text-[14px] w-full bg-[#D87A61] hover:bg-[#c05e47] text-white font-[600] py-2 rounded-[7px] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:ring-offset-2 mt-2" onclick="sendEmail()">Send OTP</button>
                                            </div>
                                        </div>
                                        <div class="email-otp-code">
                                            <p class="otp-sent-msg text-success"></p>
                                            <div class="flex items-center justify-between">
                                                <label for="firstName" class="text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">Email Verification Code <span class="text-red-600">*</span></label>
                                                <span class="text-[16px] font-[600] text-[#BE1F2E]" id="otp-timer">05:00</span>
                                            </div>
                                            <div class="otp-msg">
                                                <input type="text" class="form-control form-otp w-[22%] h-[42px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                                <input type="text" class="form-control form-otp w-[22%] h-[42px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                                <input type="text" class="form-control form-otp w-[22%] h-[42px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                                <input type="text" class="form-control form-otp w-[22%] h-[42px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                            </div>
                                            <span class="resend-span block text-center mb-[10px] text-[14px] font-[400] mt-[9px] font-[Open_Sans]">Didn't receive a code? <button type="button" class="btn-link text-[#C45F44] text-[14px] font-[700] font-[Open_Sans] resend-otp-btn" id="tab2-email-resend-otp-btn" disabled>Resend OTP</button></span>
                                            <span class="verify-msg block text-center text-[12px] font-[600] font-[Open_Sans]">Verification code sent to <span class="verify-mail font-[700]"><EMAIL></span></span>
                                            <!-- <button type="submit" class="btn-resend mobile-view disabled:cursor-not-allowed" disabled>Resend OTP</button> -->
                                             <button id="btn-otp" class="btn-otp w-full h-[42px] rounded-[8px] bg-[#D87A61] text-white text-[14px] font-[600] font-[Open_Sans] mt-[10px]">Create Account</button>
                                        </div>
                                        
                                    </form>
                                    
                                    <form class="otp-form-mobile" @submit.prevent="console.log('Form submitted');">
                                        <div class="mobile-otp-form">
                                            <div class="relative form-field">
                                                <label for="firstName" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">First Name <span class="text-red-600">*</span></label>
                                                <input type="text" name="first_name" id="firstName" class="form-control first-name w-full h-[42px] rounded-[7px] border border-[#6C6C6C] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] px-3" required />
                                                <label for="lastName" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">Last Name <span class="text-[12px] text-gray-500">(optional)</span></label>
                                                <input type="text" name="last_name" id="lastName" class="form-control last-name w-full h-[42px] rounded-[7px] border border-[#6C6C6C] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] px-3" />
                                                <label for="countryCode" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">Mobile No <span class="text-red-600">*</span></label>
                                                <span class="mobile flex items-center">
                                                    <select name="country_code" id="countryCode" class="code start-0 top-[30px] w-[25%] h-[42px] rounded-[7px] rounded-tr-none rounded-br-none border border-[#6C6C6C] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] px-3 text-[14px] font-[600] text-[#231F20] font-[Open_Sans] text-center">
                                                        <option value="1">+1</option>
                                                        <option value="7">+7</option>
                                                        <option value="43">+43</option>
                                                        <option value="44">+44</option>
                                                        <option value="60">+60</option>
                                                        <option value="65">+65</option>
                                                        <option value="91">+91</option>
                                                        <option value="92">+92</option>
                                                        <option value="1 648">******</option>
                                                    </select>
                                                    <input type="text" name="email_or_mobile" maxlength="10" class="form-control w-[75%] h-[42px] rounded-[7px] rounded-tl-none rounded-bl-none border border-l-0 border-[#6C6C6C] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] px-3" required onkeypress="validNumber(event);" />
                                                </span>
                                                <button type="submit" class="btn-login otp-email text-[14px] font-[Open_Sans] w-full bg-[#D87A61] hover:bg-[#c05e47] text-white font-[600] py-2 rounded-[7px] transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:ring-offset-2 mt-2" onclick="sendPhone()">Send OTP</button>
                                            </div>
                                        </div>
                                        <div class="mobile-otp-code">
                                            <p class="otp-sent-msg text-success"></p>
                                            <div class="flex items-center justify-between">
                                                <label class="text-[14px] font-[600] text-[#231F20] font-[Open_Sans]">Mobile Verification Code <span class="text-red-600">*</span></label>
                                                <span class="text-[14px] font-[600] text-[#BE1F2E]" id="otp-timer">05:00</span>
                                            </div>
                                            <div class="otp-msg">
                                                <input type="text" class="form-control form-otp w-[22%] h-[42px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                                <input type="text" class="form-control form-otp w-[22%] h-[42px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                                <input type="text" class="form-control form-otp w-[22%] h-[42px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                                <input type="text" class="form-control form-otp w-[22%] h-[42px] rounded-[7px] border border-[#6C6C6C] focus:border-[#D87A61] px-3" id="input" value="" require />
                                            </div>
                                            <span class="resend-span block text-center mb-[10px] text-[14px] text-[#231F20] font-[400] mt-[9px] font-[Open_Sans]">Didn't receive a code? <button type="button" class="btn-link text-[#C45F44] text-[14px] font-[700] font-[Open_Sans] resend-otp-btn" id="tab2-mobile-resend-otp-btn" disabled>Resend OTP</button></span>
                                            <span class="verify-msg block text-center text-[12px] font-[600] text-[#231F20] font-[Open_Sans]">Verification code sent to <span class="verify-mail font-[700]">+91 **********</span></span>
                                            <!-- <button type="submit" class="btn-resend mobile-view disabled:cursor-not-allowed" disabled>Resend OTP</button> -->
                                             <button class="btn-otp w-full h-[42px] rounded-[8px] bg-[#D87A61] text-white text-[14px] font-[600] font-[Open_Sans] mt-[10px]">Create Account</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <hr class="mt-[0.3rem] mb-[1rem]" />
                <div class="text-center mb-[10px]">
                    <a href="<?= $this->Url->build('/login') ?>?redirect=<?= $qryRedirect ?>" class="inline-block bg-[#fff] border-1 border-[#c05e47] hover:bg-[#c05e47] hover:text-white font-semibold px-4 py-2 rounded-lg transition-colors duration-200 text-sm ml-1 group">
                        Already have an account?
                        <span class="text-[14px] font-[600]font-[Open_Sans] text-[#D87A61] group-hover:text-white">Login</span>
                    </a>
                </div>
                
                <!--<div class="divider-container">
                    <span class="divider">Or</span>
                </div> -->
                
            </div>
        </div>
    </div>
</section>
<script>
    // Global helper functions
    function showInlineError(input, message, withOtp=false) {
        // Safety check - return early if input is null
        if (!input) {
            console.error('showInlineError: input is null');
            return;
        }
        
        var errorEl;
        
        // Special handling for mobile number field
        if (input.name === 'email_or_mobile' && input.closest('.mobile')) {
            errorEl = input.closest('.mobile').parentElement.querySelector('.input-error');
            if (!errorEl) {
                errorEl = document.createElement('span');
                errorEl.className = 'input-error text-red-500 text-xs mt-1 block';
                input.closest('.mobile').parentElement.appendChild(errorEl);
            }
        } else if(withOtp){
            errorEl = input.parentElement.parentElement.querySelector('.input-error');
            if (!errorEl) {
                errorEl = document.createElement('span');
                errorEl.className = 'input-error text-red-500 text-xs mt-1 block';
                input.parentElement.parentElement.appendChild(errorEl);
            }
        } else {
            errorEl = input.parentElement.querySelector('.input-error');
            if (!errorEl) {
                errorEl = document.createElement('span');
                errorEl.className = 'input-error text-red-500 text-xs mt-1 block';
                input.parentElement.appendChild(errorEl);
            }
        }
       
        errorEl.textContent = message;
        input.classList.add('is-invalid');
    }

    function clearInlineError(input, withOtp=false) {
        // Safety check - return early if input is null
        if (!input) {
            console.error('clearInlineError: input is null');
            return;
        }
        
        var errorEl;
        
        // Special handling for mobile number field
        if (input.name === 'email_or_mobile' && input.closest('.mobile')) {
            errorEl = input.closest('.mobile').parentElement.querySelector('.input-error');
        } else if(withOtp){
            errorEl = input.parentElement.parentElement.querySelector('.input-error');
        } else {
            errorEl = input.parentElement.querySelector('.input-error');
        }
    
        if (errorEl) errorEl.textContent = '';
        input.classList.remove('is-invalid');
    }

    function validateFirstName(value) {
        const trimmed = value.trim();

        if (!trimmed) return 'This field is required';
        if (!/^[a-zA-Z\s]+$/.test(trimmed)) return 'Only letters and spaces allowed';
        if (trimmed.length < 3) return 'At least 3 characters required';
        if (trimmed.length > 50) return 'Max 50 characters allowed';
        return '';
    }

    function validateEmailOrMobile(value) {
        const trimmed = value.trim();

        if (!trimmed) {
            return 'This field is required';
        }

        if (trimmed.includes('@')) {
            const emailRegex = /^[a-zA-Z0-9](?!.*\.\.)[a-zA-Z0-9._%+-]{0,63}@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            return emailRegex.test(trimmed) ? '' : 'Invalid email format';
        } else {
            return /^\d{10}$/.test(trimmed) ? '' : 'Enter a valid 10-digit mobile number';
        }
    }

    function validatePassword(value) {
        if (value.length < 8) return 'Min 8 characters required';
        if (value.length > 20) return 'Max 20 characters allowed';
        if (!/[A-Z]/.test(value)) return 'Include uppercase letter';
        if (!/[a-z]/.test(value)) return 'Include lowercase letter';
        if (!/[0-9]/.test(value)) return 'Include a number';
        if (!/[^A-Za-z0-9]/.test(value)) return 'Include special character';
        return '';
    }

    document.addEventListener('DOMContentLoaded', function() {
        let createBtnOtp = document.querySelector('.second-tab .btn-otp');
        if (createBtnOtp) {
            createBtnOtp.disabled = true;
            createBtnOtp.classList.add('disabled:cursor-not-allowed');
        }
        
        // Auto-hide scrollbar functionality
        const scrollableContainer = document.querySelector('.lg\\:overflow-y-auto');
        let scrollTimeout;
        
        if (scrollableContainer) {
            scrollableContainer.addEventListener('scroll', function() {
                // Show scrollbar when scrolling
                this.classList.add('scrolling');
                
                // Clear existing timeout
                clearTimeout(scrollTimeout);
                
                // Hide scrollbar after scrolling stops (1.5 seconds delay)
                scrollTimeout = setTimeout(() => {
                    this.classList.remove('scrolling');
                }, 1500);
            });
            
            // Show scrollbar on mouse enter
            scrollableContainer.addEventListener('mouseenter', function() {
                clearTimeout(scrollTimeout);
            });
            
            // Hide scrollbar on mouse leave (with delay)
            scrollableContainer.addEventListener('mouseleave', function() {
                scrollTimeout = setTimeout(() => {
                    this.classList.remove('scrolling');
                }, 500);
            });
        }
        const passwordForm = document.getElementById('password-signup-form');
        const emailOrMobileInput = passwordForm.querySelector('input[name="email_or_mobile"]');
        const firstNameInputs = passwordForm.querySelector('input[name="first_name"]');
        const passwordInput = passwordForm.querySelector('input[name="password"]');
        const termsAccepted = passwordForm.querySelector('input[name="terms_accepted"]');
        const queryRedirect = '<?php echo  $qryRedirect ?>'

        // === Password visibility toggle ===
        passwordForm.querySelectorAll('.eye-icon').forEach(icon => {
            icon.addEventListener('click', function() {
                const passwordField = this.parentElement.querySelector('input');
                const eyeIcon = this.querySelector('i');

                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    eyeIcon.classList.replace('fa-eye-slash', 'fa-eye');
                } else {
                    passwordField.type = 'password';
                    eyeIcon.classList.replace('fa-eye', 'fa-eye-slash');
                }
            });
        });

        // === Form submit ===
        passwordForm.addEventListener('submit', function(e) {
            e.preventDefault();

            let isValid = true;

            // Get values with null safety
            const emailOrMobile = emailOrMobileInput ? emailOrMobileInput.value.trim() : '';
            const firstName = firstNameInputs ? firstNameInputs.value.trim() : '';
            const password = passwordInput ? passwordInput.value : '';

            // Clear previous errors
            if (firstNameInputs) clearInlineError(firstNameInputs);
            if (emailOrMobileInput) clearInlineError(emailOrMobileInput);
            if (passwordInput) clearInlineError(passwordInput);

            // === Validations ===
            const firstNameError = validateFirstName(firstName);
            if (firstNameError) {
                if (firstNameInputs) showInlineError(firstNameInputs, firstNameError);
                isValid = false;
            }

            const emailOrMobileError = validateEmailOrMobile(emailOrMobile);
            if (emailOrMobileError) {
                if (emailOrMobileInput) showInlineError(emailOrMobileInput, emailOrMobileError);
                isValid = false;
            }

            const passwordError = validatePassword(password);
            if (passwordError) {
                if (passwordInput) showInlineError(passwordInput, passwordError);
                isValid = false;
            }

            if (!termsAccepted || !termsAccepted.checked) {
                alert("Please accept the Terms & Conditions and Privacy Policy");
                isValid = false;
            }

            if (!isValid) return;

            // === Submit with fetch ===
            const csrfToken = document.querySelector('meta[name="csrfToken"]').getAttribute('content');
            const formData = new FormData(passwordForm);
            // Show loading state
            let createBtn = document.querySelector('#password-signup-form .btn-login');
            if (createBtn) {
                createBtn.textContent = 'Processing...';
                createBtn.disabled = true;
            }
            formData.append('reqdirect_url', queryRedirect);

            fetch('<?= $this->Url->build(['controller' => 'Signup', 'action' => 'register']) ?>', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-Token': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                  
                    if (createBtn) {
                        // Reset button state
                        createBtn.textContent = 'Create Account';
                        createBtn.disabled = false;
                    }

                    if (data.success && data.redirect) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success!',
                            text: data.message,
                            confirmButtonText: 'OK'
                        }).then(() => {
                            if (data.redirect) {
                                window.location.href = data.redirect;
                            }
                        });

                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: data.message || 'An error occurred. Please try again.',
                            confirmButtonText: 'OK'
                        });

                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'An unexpected error occurred. Please try again.',
                        confirmButtonText: 'OK'
                    });

                });
        });

        // === Helper Functions ===
        // (Functions moved to global scope above)

        const otpEmailInput = document.querySelector('.second-tab input[name="email"]');
        const sendOtpBtn = document.getElementById('send-otp-btn');
        const otpWrapper = document.querySelector('.otp-wrapper');
        const otpSentMsg = document.querySelector('.otp-sent-msg');
        const otpInputs = document.querySelectorAll('.form-otp');
        // Resend buttons are now handled via querySelectorAll('.resend-otp-btn') in the event listener
        const createAccountBtn = document.querySelector('.btn-otp');
        const otpemailOrMobileInput = document.querySelector('input[name="email"]');
        const firstNameInput = document.querySelector('.second-tab input[name="first_name"]');

        // Email/Mobile validation for second tab (OTP)
        if (otpEmailInput) {
            let debounceTimer;
            let withOtp = true;

            const showValidationFeedback = (input, message) => {
             const parent = input.closest('.form-field');
                removeValidationFeedback(input);
                const feedback = document.createElement('div');
                feedback.className = 'validation-feedback text-red-500 text-xs mt-1';
                feedback.textContent = message;
                parent.appendChild(feedback);
            };

            const removeValidationFeedback = (input) => {
                const parent = input.closest('.form-field');
                const existingFeedback = parent.querySelector('.validation-feedback');
                if (existingFeedback) existingFeedback.remove();
               // input.classList.remove('is-invalid', 'is-valid');
            };

            otpEmailInput.addEventListener('input', function() {
                clearTimeout(debounceTimer);
                removeValidationFeedback(this);
                // Remove validation messages while typing
                this.classList.remove('is-invalid', 'is-valid');
                
                clearInlineError(otpEmailInput);
                // Debounce the validation
                debounceTimer = setTimeout(() => {
                    const value = this.value.trim();

                    // Check if the trimmed value is empty
                    if (!value) {
                        this.classList.add('is-invalid');
                        clearInlineError(this, withOtp);
                        showValidationFeedback(this, 'Please enter a valid email address or mobile number');
                        return;
                    } else {
                        removeValidationFeedback(this);
                    }

                    // Check if it looks like an email attempt
                    const looksLikeEmail = /[a-zA-Z@]/.test(value);
                    
                    if (looksLikeEmail) {
                        // Validate email format
                        // const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        const emailRegex = /^[a-zA-Z0-9](?!.*\.\.)[a-zA-Z0-9._%+-]{0,63}@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        const isValidEmail = emailRegex.test(value);
                 
                        // Show validation feedback
                        this.classList.add(isValidEmail ? 'is-valid' : 'is-invalid');

                        if (!isValidEmail) {
                            clearInlineError(this, withOtp);
                            showValidationFeedback(this, 'Please enter a valid email address');
                        }

                    } else {
                        // Validate mobile number (exactly 10 digits)
                        const isValidMobile = /^\d{10}$/.test(value);
                        // Show validation feedback
                        this.classList.add(isValidMobile ? 'is-valid' : 'is-invalid');
                        clearInlineError(this, withOtp);
                        removeValidationFeedback(this);
                    }
                }, 500);
            });
        }
        // Removed old timer functions - using startOTPTimer() instead


        // Initialize OTP inputs to handle one digit per input and auto-focus next input
        if (otpInputs.length) {
            otpInputs.forEach((input, index) => {
                input.addEventListener('input', function(e) {
                    // Allow only one digit
                    this.value = this.value.replace(/\D/g, '').substring(0, 1);

                    // Clear any previous error styling
                    clearInlineError(this);

                    // Auto focus next input
                    if (this.value && index < otpInputs.length - 1) {
                        otpInputs[index + 1].focus();
                    }

                    // Check if all OTP fields are filled
                    const allFilled = Array.from(otpInputs).every(inp => inp.value.length === 1);
                    if (allFilled) {
                        // Add visual feedback that OTP is complete
                        otpInputs.forEach(inp => inp.classList.add('is-valid'));
                    }
                });

                // Handle backspace to go to previous input
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Backspace' && !this.value && index > 0) {
                        otpInputs[index - 1].focus();
                    }
                });

                // Prevent non-numeric input
                input.addEventListener('keypress', function(e) {
                    if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter'].includes(e.key)) {
                        e.preventDefault();
                    }
                });
            });
        }

        // Function to get complete OTP from inputs
        function getCompleteOtp() {
            let otp = '';
            otpInputs.forEach(input => {
                otp += input.value;
            });
            return otp;
        }

        // Handle Send OTP button click
        if (sendOtpBtn) {
            sendOtpBtn.addEventListener('click', function() {
                const firstName = firstNameInput.value.trim();
                const emailOrMobile = otpemailOrMobileInput.value.trim();
                const withOtp = true;

                clearInlineError(firstNameInput);
                clearInlineError(otpemailOrMobileInput, withOtp);

                if (!emailOrMobile) {
                    showInlineError(otpemailOrMobileInput, 'This field is required', withOtp);
                    return;
                }
                
                const firstNameError = validateFirstName(firstName);
          
                if (firstNameError) {
                    showInlineError(firstNameInput, firstNameError);
                    return;
                }

               
                // Validate email or mobile format
                const isEmail = /^[a-zA-Z0-9](?!.*\.\.)[a-zA-Z0-9._%+-]{0,63}@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(emailOrMobile);
                const isMobile = /^\d{10}$/.test(emailOrMobile);

                if (!isEmail && !isMobile) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: emailOrMobile.includes('@') ? 'Please enter a valid email address' : 'Please enter a valid 10-digit mobile number',
                        confirmButtonText: 'OK'
                    });
                    return;
                }


                // Show loading state
                sendOtpBtn.textContent = 'Sending...';
                sendOtpBtn.disabled = true;

                // Send OTP request
                const formData = new FormData();
                formData.append('email', emailOrMobile);
                const csrfToken = document.querySelector('meta[name="csrfToken"]').getAttribute('content');

                fetch('<?= $this->Url->build(['controller' => 'Signup', 'action' => 'sendOtp']) ?>', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-Token': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Reset button state
                        sendOtpBtn.textContent = 'Send OTP';
                        sendOtpBtn.disabled = false;

                        if (data.success) {
                            // Show OTP input fields
                            otpWrapper.style.display = 'block';

                            // Update message based on input type
                            if (data.input_type === 'mobile') {
                                otpSentMsg.textContent = 'The OTP is sent to your mobile number';
                            } else {
                                otpSentMsg.textContent = 'The OTP is sent to your email address';
                            }

                            console.log('Starting timer, timer element:', timerText);
                            // Start countdown timer - using proper 5-minute timer
                            startOTPTimer();

                            // Disable resend button initially (done in startOTPTimer)
                            // resendOtpBtn.disabled = true;
                            // resendOtpBtn.classList.add('disabled:cursor-not-allowed');

                            // Focus first OTP input
                            if (otpInputs.length) {
                                otpInputs[0].focus();
                            }

                            // Show success message
                            Swal.fire({
                                icon: 'success',
                                title: 'OTP Sent',
                                text: data.message,
                                confirmButtonText: 'OK'
                            });

                            // enable create button //
                            if (createBtnOtp) {
                                createBtnOtp.disabled = false;
                                createBtnOtp.classList.remove('disabled:cursor-not-allowed');
                            }

                        } else {
                            // Show error message
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: data.message || 'Failed to send OTP. Please try again.',
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        sendOtpBtn.textContent = 'Send OTP';
                        sendOtpBtn.disabled = false;

                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'An unexpected error occurred. Please try again.',
                            confirmButtonText: 'OK'
                        });
                    });
            });
        }

        // Handle Resend OTP button click - for all resend buttons
        const allResendButtons = document.querySelectorAll('.resend-otp-btn');
        allResendButtons.forEach(resendBtn => {
            if (resendBtn) {
                resendBtn.addEventListener('click', function() {
                    if (this.disabled) return;

                    console.log('Resend OTP button clicked:', this.id);
                    
                    // Get form data again
                    const passwordForm = document.getElementById('password-signup-form');
                    const firstNameInput = passwordForm.querySelector('input[name="first_name"]');
                    const lastNameInput = passwordForm.querySelector('input[name="last_name"]');
                    const emailInput = passwordForm.querySelector('input[name="email_id"]');
                    const mobileInput = passwordForm.querySelector('input[name="email_or_mobile"]');
                    
                    const firstName = firstNameInput.value.trim();
                    const lastName = lastNameInput.value.trim();
                    const email = emailInput.value.trim();
                    const mobile = mobileInput.value.trim();
                    
                    // Show loading state on resend button
                    const originalText = this.textContent;
                    this.textContent = 'Resending...';
                    this.disabled = true;
                    
                    // Prepare data for resend OTP request
                    const formData = new FormData();
                    formData.append('email_or_mobile', mobile);
                    formData.append('first_name', firstName);
                    if (lastName) formData.append('last_name', lastName);
                    if (email) formData.append('email_id', email);
                    formData.append('country_code', passwordForm.querySelector('select[name="country_code"]').value);
                    
                    const csrfToken = document.querySelector('meta[name="csrfToken"]').getAttribute('content');
                    const fetchUrl = '<?= $this->Url->build(['controller' => 'Signup', 'action' => 'sendOtpAllOrOne']) ?>';
                    
                    // Send resend OTP request
                    fetch(fetchUrl, {
                        method: 'POST',
                        headers: {
                            'X-CSRF-Token': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: formData
                    })
                    .then(response => {
                        console.log('Resend response status:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Resend server response:', data);
                        
                        // Reset button state
                        this.textContent = originalText;
                        // Keep button disabled until new timer expires
                        
                        if (data.success) {
                            console.log('OTP resent successfully');
                            
                            // Clear existing OTP inputs
                            const otpInputs = document.querySelectorAll('.form-otp');
                            otpInputs.forEach(input => input.value = '');
                            
                            // Restart timer for new 5-minute period
                            startOTPTimer();
                            
                            // Show success message
                            Swal.fire({
                                icon: 'success',
                                title: 'OTP Resent!',
                                text: data.message,
                                confirmButtonText: 'OK',
                                timer: 3000
                            });
                        } else {
                            console.log('OTP resend failed:', data.message);
                            this.disabled = false; // Re-enable button on error
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: data.message || 'Failed to resend OTP. Please try again.',
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error resending OTP:', error);
                        
                        // Reset button state on error
                        this.textContent = originalText;
                        this.disabled = false;
                        
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: `Failed to resend OTP: ${error.message}`,
                            confirmButtonText: 'OK'
                        });
                    });
                });
            }
        });

        // Handle Create Account button click
        if (createAccountBtn) {
            createAccountBtn.addEventListener('click', function() {
                // Get form data from the original password form
                const passwordForm = document.getElementById('password-signup-form');
                if (!passwordForm) {
                    console.error('Password form not found');
                    return;
                }
                
                const firstNameField = passwordForm.querySelector('input[name="first_name"]');
                const lastNameField = passwordForm.querySelector('input[name="last_name"]');
                const emailField = passwordForm.querySelector('input[name="email_id"]');
                const mobileField = passwordForm.querySelector('input[name="email_or_mobile"]');
                const passwordField = passwordForm.querySelector('input[name="password"]');
                const countryCodeField = passwordForm.querySelector('select[name="country_code"]');
                const termsField = passwordForm.querySelector('input[name="terms_accepted"]');
                
                // Safety checks and get values
                const firstName = firstNameField ? firstNameField.value.trim() : '';
                const lastName = lastNameField ? lastNameField.value.trim() : '';
                const emailId = emailField ? emailField.value.trim() : '';
                const mobileNumber = mobileField ? mobileField.value.trim() : '';
                const password = passwordField ? passwordField.value : '';
                const countryCode = countryCodeField ? countryCodeField.value : '91';
                const termsAccepted = termsField ? termsField.checked : false;
                
                const otp = getCompleteOtp();
                
                // Determine which field to use as email_or_mobile
                let emailOrMobile = '';
                if (mobileNumber) {
                    emailOrMobile = mobileNumber;
                } else if (emailId) {
                    emailOrMobile = emailId;
                }
               
                // Clear inline errors (with null safety checks)
                if (firstNameInput) clearInlineError(firstNameInput);
                if (otpEmailInput) clearInlineError(otpEmailInput);
                if (termsAccepetdInput) clearInlineError(termsAccepetdInput);
                otpInputs.forEach(input => {
                    if (input) clearInlineError(input);
                });

                let hasError = false;
                
                // Validate required fields
                const firstNameError = validateFirstName(firstName);
                if (firstNameError) {
                    if (firstNameInput) showInlineError(firstNameInput, firstNameError);
                    hasError = true;
                }

                if (!emailOrMobile) {
                    if (otpEmailInput) showInlineError(otpEmailInput, 'This field is required');
                    hasError = true;
                }

                // Skip terms validation for Create Account - user already accepted terms when sending OTP
                
                // Validate OTP
                const otpValue = getCompleteOtp();
                var showOTPError = false;
                if (otpValue.length < otpInputs.length) {
                    otpInputs.forEach(input => {
                        if (!input.value) showOTPError = true; 
                    });

                    if(showOTPError == true){
                        // Find first empty OTP input for error display
                        const emptyInput = Array.from(otpInputs).find(input => !input.value);
                        if (emptyInput) showInlineError(emptyInput, 'Required');
                    }
                    hasError = true;
                }

                if (hasError) {
                    console.log('Validation failed, not proceeding with registration');
                    return;
                }

                // Show loading state
                this.textContent = 'Processing...';
                this.disabled = true;

                // Create verification and registration request with all form data
                const formData = new FormData();
                formData.append('first_name', firstName);
                formData.append('otp', otp);
                
                // Add all form data
                if (lastName) formData.append('last_name', lastName);
                if (emailId) formData.append('email_id', emailId);
                if (mobileNumber) formData.append('email_or_mobile', mobileNumber);
                formData.append('password', password);
                formData.append('country_code', countryCode);
                
                const csrfToken = document.querySelector('meta[name="csrfToken"]').getAttribute('content');
                formData.append('reqdirect_url', queryRedirect);
                
                fetch('<?= $this->Url->build(['controller' => 'Signup', 'action' => 'verifyOtpAndRegister']) ?>', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-Token': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Reset button state
                        this.textContent = 'Create Account';
                        this.disabled = false;

                        if (data.success) {
                            // Show success message
                            Swal.fire({
                                icon: 'success',
                                title: 'Success!',
                                text: data.message,
                                confirmButtonText: 'OK'
                            }).then(() => {
                                if (data.redirect) {
                                    window.location.href = data.redirect;
                                }
                            });
                        } else {
                            // Show specific error message from server
                            let errorTitle = 'Registration Failed';
                            let errorMessage = data.message || 'Please check your information and try again.';

                            // Check if it's an OTP-related error
                            if (errorMessage.includes('OTP') || errorMessage.includes('expired')) {
                                errorTitle = 'OTP Verification Failed';
                            } else if (errorMessage.includes('already registered')) {
                                errorTitle = 'Account Already Exists';
                            }

                            Swal.fire({
                                icon: 'error',
                                title: errorTitle,
                                text: errorMessage,
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        this.textContent = 'Create Account';
                        this.disabled = false;

                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'An unexpected error occurred. Please try again.',
                            confirmButtonText: 'OK'
                        });
                    });
            });
        }

        //Toggle Icon script
        // function togglePasswordVisibility() {
        //     const input = document.getElementById("password");
        //     const icon = document.getElementById("eyeIcon");

        //     if (input.type === "password") {
        //         input.type = "text";
        //         // Switch to eye-off icon
        //         icon.innerHTML = `
        //         <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
        //             d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7a9.977
        //             9.977 0 012.348-4.413M9.88 9.88a3 3 0 104.24 4.24M3 3l18 18" />
        //         `;
        //     } 
        //     else {
        //         input.type = "password";
        //         // Switch back to eye icon
        //         icon.innerHTML = `
        //         <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
        //             d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        //         <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
        //             d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274
        //             4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        //         `;
        //     }
        // }

        // Add CSS for validation styling
        document.head.insertAdjacentHTML('beforeend', `
        <style>
        .is-invalid {
            border-color: #dc3545 !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        .otp-msg .is-invalid{
            border-color: #dc3545 !important;
            background-image: none;
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
            padding: 1px 7px;
        }
        .password.is-invalid{
            border-color: #dc3545 !important;
            background-image: none;
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        /* Special styling for mobile number field validation */
        .mobile .form-control.is-invalid {
            border-color: #dc3545 !important;
            background-image: none;
        }
        /* Ensure mobile error messages appear below the mobile field container */
        .mobile + .input-error {
            margin-top: 4px;
        }
        .is-valid {
            border-color: #28a745 !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        
        /* Custom Scrollbar Styling with Auto-Hide */
        .lg\\:overflow-y-auto::-webkit-scrollbar {
            width: 8px;
            transition: opacity 0.3s ease;
        }
        
        .lg\\:overflow-y-auto::-webkit-scrollbar-track {
            background: transparent;
            border-radius: 10px;
            margin: 8px 0;
        }
        
        .lg\\:overflow-y-auto::-webkit-scrollbar-thumb {
            background: rgba(216, 122, 97, 0.6);
            border-radius: 10px;
            transition: background-color 0.3s ease, opacity 0.3s ease;
        }
        
        .lg\\:overflow-y-auto::-webkit-scrollbar-thumb:hover {
            background: rgba(192, 94, 71, 0.8);
        }
        
        .lg\\:overflow-y-auto::-webkit-scrollbar-thumb:active {
            background: rgba(165, 74, 55, 1);
        }
        
        /* Hide scrollbar by default */
        .lg\\:overflow-y-auto {
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE/Edge */
        }
        
        .lg\\:overflow-y-auto::-webkit-scrollbar {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        /* Show scrollbar on hover or when scrolling */
        .lg\\:overflow-y-auto:hover::-webkit-scrollbar,
        .lg\\:overflow-y-auto.scrolling::-webkit-scrollbar {
            opacity: 1;
        }
        
        /* Firefox - show thin scrollbar on hover/scroll */
        .lg\\:overflow-y-auto:hover,
        .lg\\:overflow-y-auto.scrolling {
            scrollbar-width: thin;
            scrollbar-color: rgba(216, 122, 97, 0.6) transparent;
        }
        
        /* Smooth scrolling behavior */
        .lg\\:overflow-y-auto {
            scroll-behavior: smooth;
        }
        
        /* Custom styling for the signup form container when OTP is shown */
        .otp-wrapper {
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Ensure proper padding when scrollbar appears */
        @media (min-width: 1024px) {
            .lg\\:overflow-y-auto {
                padding-right: 4px;
            }
        }
        
        /* Minimize scrolling by reducing form heights and spacings */
        .form-field {
            margin-bottom: 0.5rem !important;
        }
        
        .form-field input,
        .form-field select {
            height: 40px !important;
        }
        
        .otp-wrapper {
            margin-top: 1rem !important;
        }
        
        .password-requirements {
            font-size: 9px !important;
            line-height: 1.2 !important;
            margin-top: 2px !important;
        }
        
        /* Compact OTP section */
        .otp-msg {
            margin: 8px 0 !important;
        }
        
        .verify-msg, .resend-span {
            line-height: 1.3 !important;
        }
        </style>
        `);
    });

    document.addEventListener('DOMContentLoaded', function () {
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        const input = document.getElementById('input');
        const container = document.getElementById('loginWrapper');

        if (isIOS && input && container) {
            input.addEventListener('focus', function () {
            container.classList.add('ios-focused');
            });

            input.addEventListener('blur', function () {
            container.classList.remove('ios-focused');
            });
        }

        const mobregExp = /^[0-9\b]+$/;
        const nameregEx = /^[A-Za-z]+$/;
        document.getElementsByClassName("otp-wrapper")[0].style.display = "none";

        window.validNumber = function(event){
            var mobno = event.target.value + event.key;
            if(!mobregExp.test(mobno)){
                event.preventDefault();
            }
        }

        window.validString = function(event){
            var name = event.target.value + event.key;
            if(!nameregEx.test(name)){
                event.preventDefault();
            }
        }

        window.sendOTP = function(){
            console.log('sendOTP function called');
            // Get form references
            const passwordForm = document.getElementById('password-signup-form');
            const firstNameInput = passwordForm.querySelector('input[name="first_name"]');
            const lastNameInput = passwordForm.querySelector('input[name="last_name"]');
            const emailInput = passwordForm.querySelector('input[name="email_id"]');
            const mobileInput = passwordForm.querySelector('input[name="email_or_mobile"]');
            const passwordInput = passwordForm.querySelector('input[name="password"]');
            const rePasswordInput = passwordForm.querySelector('input[name="re_password"]');

            console.log('Form elements found:', {
                firstName: firstNameInput?.value,
                lastName: lastNameInput?.value,
                email: emailInput?.value,
                mobile: mobileInput?.value,
                password: passwordInput?.value ? 'present' : 'missing',
                rePassword: rePasswordInput?.value ? 'present' : 'missing'
            });

            // Clear previous errors
            clearInlineError(firstNameInput);
            clearInlineError(lastNameInput);
            clearInlineError(emailInput);
            clearInlineError(mobileInput);
            clearInlineError(passwordInput);
            clearInlineError(rePasswordInput);

            let isValid = true;

            // Validate First Name
            const firstName = firstNameInput.value.trim();
            const firstNameError = validateFirstName(firstName);
            if (firstNameError) {
                console.log('First name validation failed:', firstNameError);
                showInlineError(firstNameInput, firstNameError);
                isValid = false;
            }

            // Validate Last Name (optional but if provided should be valid)
            const lastName = lastNameInput.value.trim();
            if (lastName && lastName.length > 0) {
                if (!/^[a-zA-Z\s]+$/.test(lastName)) {
                    console.log('Last name validation failed: invalid characters');
                    showInlineError(lastNameInput, 'Only letters and spaces allowed');
                    isValid = false;
                } else if (lastName.length > 50) {
                    console.log('Last name validation failed: too long');
                    showInlineError(lastNameInput, 'Max 50 characters allowed');
                    isValid = false;
                }
            }

            // Validate Email (optional)
            const email = emailInput.value.trim();
            if (email && email.length > 0) {
                const emailRegex = /^[a-zA-Z0-9](?!.*\.\.)[a-zA-Z0-9._%+-]{0,63}@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                if (!emailRegex.test(email)) {
                    console.log('Email validation failed:', email);
                    showInlineError(emailInput, 'Invalid email format');
                    isValid = false;
                }
            }

            // Validate Mobile Number
            const mobile = mobileInput.value.trim();
            if (!mobile) {
                console.log('Mobile validation failed: empty');
                showInlineError(mobileInput, 'Mobile number is required');
                isValid = false;
            } else if (!/^\d{10}$/.test(mobile)) {
                console.log('Mobile validation failed: invalid format', mobile);
                showInlineError(mobileInput, 'Enter a valid 10-digit mobile number');
                isValid = false;
            }

            // Validate Password
            const password = passwordInput.value;
            const passwordError = validatePassword(password);
            if (passwordError) {
                console.log('Password validation failed:', passwordError);
                showInlineError(passwordInput, passwordError);
                isValid = false;
            }

            // Validate Re-password
            const rePassword = rePasswordInput.value;
            if (!rePassword) {
                console.log('Re-password validation failed: empty');
                showInlineError(rePasswordInput, 'Please re-enter your password');
                isValid = false;
            } else if (password !== rePassword) {
                console.log('Re-password validation failed: not matching');
                showInlineError(rePasswordInput, 'Passwords do not match');
                isValid = false;
            }

            console.log('Validation result:', isValid);

            // If validation fails, don't proceed
            if (!isValid) {
                return false;
            }

            // If all validations pass, send OTP API request
            console.log('Sending OTP request to server');
            
            // Show loading state
            const sendButton = document.querySelector('.btn-login');
            const originalText = sendButton.textContent;
            sendButton.textContent = 'Sending OTP...';
            sendButton.disabled = true;

            // Prepare data for OTP request
            const formData = new FormData();
            formData.append('email_or_mobile', mobile);
            formData.append('first_name', firstName);
            if (lastName) formData.append('last_name', lastName);
            if (email) formData.append('email_id', email);
            formData.append('password', password);
            formData.append('country_code', passwordForm.querySelector('select[name="country_code"]').value);
            
            // Debug: Log the data being sent
            console.log('Data being sent to server:');
            for (let [key, value] of formData.entries()) {
                console.log(`${key}: ${value}`);
            }
            
            const csrfToken = document.querySelector('meta[name="csrfToken"]').getAttribute('content');
            console.log('CSRF Token:', csrfToken);
            
            const fetchUrl = '<?= $this->Url->build(['controller' => 'Signup', 'action' => 'sendOtpAllOrOne']) ?>';
            console.log('Fetch URL:', fetchUrl);

            // Send OTP request to server
            fetch(fetchUrl, {
                method: 'POST',
                headers: {
                    'X-CSRF-Token': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Server response:', data);
                // Reset button state
                sendButton.textContent = originalText;
                sendButton.disabled = false;

                if (data.success) {
                    console.log('OTP sent successfully');
                    
                    // Update verification messages with actual mobile number
                    const countryCode = passwordForm.querySelector('select[name="country_code"]').value;
                    const fullMobile = `+${countryCode} ${mobile}`;
                    
                    // Always update and show mobile verification message
                    const mobileVerifyMsg = document.getElementById('mobile-verify-msg');
                    const mobileVerifyLine = document.getElementById('mobile-verify-line');
                    if (mobileVerifyMsg) {
                        mobileVerifyMsg.textContent = fullMobile;
                    }
                    if (mobileVerifyLine) {
                        mobileVerifyLine.style.display = 'block';
                    }
                    
                    // Handle email verification message based on whether email was provided
                    const emailVerifyLine = document.getElementById('email-verify-line');
                    if (email) {
                        const emailVerifyMsg = document.getElementById('email-verify-msg');
                        if (emailVerifyMsg) {
                            emailVerifyMsg.textContent = email;
                        }
                        if (emailVerifyLine) {
                            emailVerifyLine.style.display = 'block';
                        }
                    } else {
                        // Hide email verification line if no email provided
                        if (emailVerifyLine) {
                            emailVerifyLine.style.display = 'none';
                        }
                    }
                    
                    // Show OTP form and hide main form
                    document.getElementsByClassName("otp-wrapper")[0].style.display = "block";
                    document.getElementsByClassName("form-wrapper")[0].style.display = "none";
                    document.getElementsByClassName("btn-login")[0].style.display = "none";
                    
                    // Start timer
                    startOTPTimer();
                    
                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'OTP Sent!',
                        text: data.message,
                        confirmButtonText: 'OK',
                        timer: 3000
                    });
                    
                } else {
                    console.log('OTP sending failed:', data.message);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message || 'Failed to send OTP. Please try again.',
                        confirmButtonText: 'OK'
                    });
                }
            })
            .catch(error => {
                console.error('Error sending OTP:', error);
                console.error('Error details:', {
                    message: error.message,
                    stack: error.stack,
                    name: error.name
                });
                // Reset button state
                sendButton.textContent = originalText;
                sendButton.disabled = false;
                
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: `An unexpected error occurred: ${error.message}`,
                    confirmButtonText: 'OK'
                });
            });
        }

        // Timer functionality for OTP
        let currentTimerInterval = null; // Store timer interval globally
        
        function startOTPTimer() {
            // Clear any existing timer
            if (currentTimerInterval) {
                clearInterval(currentTimerInterval);
            }
            
            // Get all resend buttons
            const resendButtons = document.querySelectorAll('.resend-otp-btn');
            let timeLeft = 30; // Always 5 minutes (300 seconds)
            
            // Update timer display
            function updateTimer() {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                const formattedTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                // Update all timer elements
                const timerElements = document.querySelectorAll('#mobile-otp-timer, #email-otp-timer, #otp-timer');
                timerElements.forEach(timer => {
                    if (timer) timer.textContent = formattedTime;
                });
                
                if (timeLeft <= 0) {
                    clearInterval(currentTimerInterval);
                    currentTimerInterval = null;
                    
                    // Enable all resend buttons
                    resendButtons.forEach(button => {
                        if (button) {
                            button.disabled = false;
                            button.classList.remove('disabled:cursor-not-allowed');
                        }
                    });
                    
                    // Set timer displays to 00:00
                    timerElements.forEach(timer => {
                        if (timer) timer.textContent = '00:00';
                    });
                } else {
                    timeLeft--;
                }
            }
            
            // Start timer
            updateTimer(); // Update immediately
            currentTimerInterval = setInterval(updateTimer, 1000);
            
            // Disable all resend buttons initially
            resendButtons.forEach(button => {
                if (button) {
                    button.disabled = true;
                    button.classList.add('disabled:cursor-not-allowed');
                }
            });
        }

        //document.getElementsByClassName("email-otp-verify")[0].style.display = "none";
        window.sendEmailOTP = function(){
            document.getElementsByClassName("email-otp-verify")[0].style.display = "block";
            document.getElementsByClassName("email-otp-form")[0].style.display = "none";
            // document.getElementsByClassName("otp-email")[0].style.display = "none";
        }
        //With OTP
        document.getElementsByClassName("email-otp-code")[0].style.display = "none";
        window.sendEmail = function(){
            document.getElementsByClassName("email-otp-code")[0].style.display = "block";
            document.getElementsByClassName("email-otp-form")[0].style.display = "none";
            // document.getElementsByClassName("otp-email")[1].style.display = "none";
        }

        document.getElementsByClassName("mobile-otp-code")[0].style.display = "none";
        window.sendPhone = function(){
            document.getElementsByClassName("mobile-otp-code")[0].style.display = "block";
            document.getElementsByClassName("mobile-otp-form")[0].style.display = "none";
            // document.getElementsByClassName("otp-email")[1].style.display = "none";
        }

        // Handle Create Account buttons for second tab (With OTP)
        // Email OTP Create Account button
        const emailOtpCreateBtn = document.querySelector('.otp-form-email .btn-otp');
        if (emailOtpCreateBtn) {
            emailOtpCreateBtn.addEventListener('click', function() {
                handleSecondTabCreateAccount('email');
            });
        }

        // Mobile OTP Create Account button
        const mobileOtpCreateBtn = document.querySelector('.otp-form-mobile .btn-otp');
        if (mobileOtpCreateBtn) {
            mobileOtpCreateBtn.addEventListener('click', function() {
                handleSecondTabCreateAccount('mobile');
            });
        }

        // Function to handle Create Account for second tab
        function handleSecondTabCreateAccount(type) {
            console.log('Create Account clicked for type:', type);

            let firstName, lastName, emailOrMobile, otpInputs, formContainer;

            if (type === 'email') {
                formContainer = document.querySelector('.otp-form-email');
                firstName = formContainer.querySelector('input[name="first_name"]').value.trim();
                lastName = formContainer.querySelector('input[name="last_name"]').value.trim();
                emailOrMobile = formContainer.querySelector('input[name="email"]').value.trim();
                otpInputs = formContainer.querySelectorAll('.form-otp');
            } else {
                formContainer = document.querySelector('.otp-form-mobile');
                firstName = formContainer.querySelector('input[name="first_name"]').value.trim();
                lastName = formContainer.querySelector('input[name="last_name"]').value.trim();
                emailOrMobile = formContainer.querySelector('input[name="email_or_mobile"]').value.trim();
                otpInputs = formContainer.querySelectorAll('.form-otp');
            }

            // Clear previous errors
            const firstNameInput = formContainer.querySelector('input[name="first_name"]');
            const lastNameInput = formContainer.querySelector('input[name="last_name"]');
            const emailOrMobileInput = type === 'email' ?
                formContainer.querySelector('input[name="email"]') :
                formContainer.querySelector('input[name="email_or_mobile"]');

            clearInlineError(firstNameInput);
            clearInlineError(lastNameInput);
            clearInlineError(emailOrMobileInput);
            otpInputs.forEach(input => clearInlineError(input));

            let hasError = false;

            // Validate first name
            const firstNameError = validateFirstName(firstName);
            if (firstNameError) {
                showInlineError(firstNameInput, firstNameError);
                hasError = true;
            }

            // Validate email or mobile
            if (!emailOrMobile) {
                showInlineError(emailOrMobileInput, 'This field is required');
                hasError = true;
            } else {
                const emailOrMobileError = validateEmailOrMobile(emailOrMobile);
                if (emailOrMobileError) {
                    showInlineError(emailOrMobileInput, emailOrMobileError);
                    hasError = true;
                }
            }

            // Validate OTP
            let otp = '';
            otpInputs.forEach(input => {
                otp += input.value;
            });

            if (otp.length < 4) {
                const emptyInput = Array.from(otpInputs).find(input => !input.value);
                if (emptyInput) {
                    showInlineError(emptyInput, 'Required');
                }
                hasError = true;
            }

            if (hasError) {
                console.log('Validation failed for second tab');
                return;
            }

            // Show loading state
            const createBtn = formContainer.querySelector('.btn-otp');
            createBtn.textContent = 'Processing...';
            createBtn.disabled = true;

            // Prepare form data
            const formData = new FormData();
            formData.append('first_name', firstName);
            if (lastName) formData.append('last_name', lastName);
            formData.append('email', emailOrMobile);
            formData.append('otp', otp);

            const csrfToken = document.querySelector('meta[name="csrfToken"]').getAttribute('content');
            const queryRedirect = '<?php echo $qryRedirect ?>';
            formData.append('reqdirect_url', queryRedirect);

            // Send verification request
            fetch('<?= $this->Url->build(['controller' => 'Signup', 'action' => 'verifyOtp']) ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-Token': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // Reset button state
                createBtn.textContent = 'Create Account';
                createBtn.disabled = false;

                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: data.message,
                        confirmButtonText: 'OK'
                    }).then(() => {
                        if (data.redirect) {
                            window.location.href = data.redirect;
                        }
                    });
                } else {
                    // Show specific error message from server
                    let errorTitle = 'OTP Verification Failed';
                    let errorMessage = data.message || 'Please check your OTP and try again.';

                    // Check if it's an expiry error
                    if (errorMessage.includes('expired')) {
                        errorTitle = 'OTP Expired';
                    } else if (errorMessage.includes('session found')) {
                        errorTitle = 'Session Error';
                    }

                    Swal.fire({
                        icon: 'error',
                        title: errorTitle,
                        text: errorMessage,
                        confirmButtonText: 'OK'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                createBtn.textContent = 'Create Account';
                createBtn.disabled = false;

                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'An unexpected error occurred. Please try again.',
                    confirmButtonText: 'OK'
                });
            });
        }

        document.getElementsByClassName("otp-form-mobile")[0].style.display = "none";
        window.checkRadio = function (id) {
            console.log("The id is:", id);
            if(id == "phone"){
                document.getElementsByClassName("otp-form-mobile")[0].style.display = "block";
                document.getElementsByClassName("otp-form-email")[0].style.display = "none";
            }
            else{
                document.getElementsByClassName("otp-form-mobile")[0].style.display = "none";
                document.getElementsByClassName("otp-form-email")[0].style.display = "block";
            }
        };
        // document.addEventListener("click",".btn-login", function(){
        //     document.getElementsByClassName("otp-wrapper")[0].style.display = "block";
        //     document.getElementsByClassName("login-form")[0].style.display = "none";
        // });

        //Toggle eye icon script
        document.querySelector('.eye-toggle').addEventListener('click', function () {
            const input = document.getElementById("password");
            const icon = document.getElementById("eyeIcon");

            if (input.type === "password") {
                input.type = "text";
                icon.innerHTML = `
                <svg id="eyeIcon" class="shrink-0 size-3.5" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274
                    4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                
                `;
            } else {
                input.type = "password";
                icon.innerHTML = `
                <svg id="eyeIcon" class="shrink-0 size-3.5" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path class="hs-password-active:hidden" d="M9.88 9.88a3 3 0 1 0 4.24 4.24"></path>
                    <path class="hs-password-active:hidden" d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"></path>
                    <path class="hs-password-active:hidden" d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"></path>
                    <line class="hs-password-active:hidden" x1="2" x2="22" y1="2" y2="22"></line>
                    <path class="hidden hs-password-active:block" d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                    <circle class="hidden hs-password-active:block" cx="12" cy="12" r="3"></circle>
                </svg>
                
                `;
            }
        });
        //eye icon toggle for confirm password
        document.querySelector('.eye-confirm-toggle').addEventListener('click', function () {
            const input = document.getElementById("rePassword");
            const icon = document.getElementById("eyeConfirmIcon");

            if (input.type === "password") {
                input.type = "text";
                icon.innerHTML = `
                <svg id="eyeConfirmIcon" class="shrink-0 size-3.5" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274
                    4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                
                `;
            } else {
                input.type = "password";
                icon.innerHTML = `
                <svg id="eyeConfirmIcon" class="shrink-0 size-3.5" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path class="hs-password-active:hidden" d="M9.88 9.88a3 3 0 1 0 4.24 4.24"></path>
                    <path class="hs-password-active:hidden" d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"></path>
                    <path class="hs-password-active:hidden" d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"></path>
                    <line class="hs-password-active:hidden" x1="2" x2="22" y1="2" y2="22"></line>
                    <path class="hidden hs-password-active:block" d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                    <circle class="hidden hs-password-active:block" cx="12" cy="12" r="3"></circle>
                </svg>
                
                `;
            }
        });
    });
</script>

<!-- Add Google Sign-In API -->
<script src="https://accounts.google.com/gsi/client" async defer></script>