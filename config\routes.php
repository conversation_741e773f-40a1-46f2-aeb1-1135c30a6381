<?php

/**
 * Routes configuration.
 *
 * In this file, you set up routes to your controllers and their actions.
 * Routes are very important mechanism that allows you to freely connect
 * different URLs to chosen controllers and their actions (functions).
 *
 * It's loaded within the context of `Application::routes()` method which
 * receives a `RouteBuilder` instance `$routes` as method argument.
 *
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 */

use Cake\Routing\Route\DashedRoute;
use Cake\Routing\RouteBuilder;
use Cake\Routing\Router;
use ADmad\I18n\Middleware\I18nMiddleware;
/*
 * This file is loaded in the context of the `Application` class.
  * So you can use  `$this` to reference the application class instance
  * if required.
 */

return function (RouteBuilder $routes): void {

    // Admin Prefixed Routes
    $routes->prefix('Admin', function (RouteBuilder $builder) {
        $builder->setRouteClass(DashedRoute::class);
        $builder->connect('/robots', ['controller' => 'Robots', 'action' => 'index']);
        $builder->connect('/robots/edit', ['controller' => 'Robots', 'action' => 'edit']);
        // Example: /admin redirects to Admin\DashboardController@index
        $builder->connect('/login', ['controller' => 'Users', 'action' => 'login']);
        $builder->connect('/check-php-config', ['controller' => 'Courses', 'action' => 'checkPhpConfig']);
        $builder->connect('/partners/get-states', ['controller' => 'Partners', 'action' => 'getStates']);
        $builder->connect('/partners/get-cities', ['controller' => 'Partners', 'action' => 'getCities']);
        $builder->connect('/checkDuplicateCourseName', ['controller' => 'Courses', 'action' => 'checkDuplicateCourseName'], ['method' => 'POST']);

        // Coupon routes
        $builder->connect('/coupons', ['controller' => 'Coupons', 'action' => 'index']);
        $builder->connect('/coupons/add', ['controller' => 'Coupons', 'action' => 'add']);
        $builder->connect('/coupons/view/{id}', ['controller' => 'Coupons', 'action' => 'view'])->setPass(['id']);
        $builder->connect('/coupons/edit/{id}', ['controller' => 'Coupons', 'action' => 'edit'])->setPass(['id']);
        $builder->connect('/coupons/delete/{id}', ['controller' => 'Coupons', 'action' => 'delete'])->setPass(['id']);
        $builder->connect('/coupons/toggle-status/{id}', ['controller' => 'Coupons', 'action' => 'toggleStatus'])->setPass(['id']);
        $builder->connect('/coupons/bulk-actions', ['controller' => 'Coupons', 'action' => 'bulkActions']);
        $builder->connect('/coupons/export', ['controller' => 'Coupons', 'action' => 'export']);

        // Review routes
        $builder->connect('/reviews', ['controller' => 'Reviews', 'action' => 'index']);
        $builder->connect('/reviews/add', ['controller' => 'Reviews', 'action' => 'add']);
        $builder->connect('/reviews/view/{id}', ['controller' => 'Reviews', 'action' => 'view'])->setPass(['id']);
        $builder->connect('/reviews/edit/{id}', ['controller' => 'Reviews', 'action' => 'edit'])->setPass(['id']);
        $builder->connect('/reviews/delete/{id}', ['controller' => 'Reviews', 'action' => 'delete'])->setPass(['id']);
        $builder->connect('/reviews/update-status/{id}', ['controller' => 'Reviews', 'action' => 'updateStatus'])->setPass(['id']);
        $builder->connect('/reviews/bulk-action', ['controller' => 'Reviews', 'action' => 'bulkAction']);

        // Fallback for all Admin controllers
        $builder->fallbacks();
    });

    $routes->scope('/{lang}', ['lang' => 'af|sq|alz,am|ar|ban|bal|bn|bew|bho|bs|bg|bua|yue|ca|ca|ny|zh-CN|zh-TW|crh|hr|cs|nl|en|et|tl|fr|fr-CA|ka|de |el|gu|iw|hi|hu|id|ja|kn|lb|ms|mt|mr|mwr|new|pt|pt-PT|pa|pa-Arab|es|ta|te|tr'], function (RouteBuilder $builder): void {
        $builder->setRouteClass(DashedRoute::class);
        $builder->setExtensions(['json']);
        // SEO route for center type
        $builder->connect('/yoga-centers/{country}/type/{type}', [
            'controller' => 'Partners',
            'action' => 'index'
        ])
        ->setPass(['country', 'type']);

        $builder->connect('/yoga-centers/{country}/{region}/type/{type}', [
            'controller' => 'Partners',
            'action' => 'index'
        ])
        ->setPass(['country', 'region', 'type']);

        // SEO-friendly route: /lang/yoga-centers/india
        $builder->connect('/yoga-centers/{country}', [
            'controller' => 'Partners',
            'action' => 'index'
        ])
        ->setPass(['country']);

        $builder->connect('/yoga-centers/{country}/{region}', [
            'controller' => 'Partners',
            'action' => 'index'
        ])
        ->setPass(['country', 'region']);

        $builder->connect('/yoga-centers/{country}/{region}/{state}', [
            'controller' => 'Partners',
            'action' => 'index'
        ])
        ->setPass(['country', 'region', 'state']);

        $builder->connect('/yoga-centers/{country}/{region}/{state}/{city}', [
            'controller' => 'Partners',
            'action' => 'index'
        ])
        ->setPass(['country', 'region', 'state', 'city']);

        $builder->connect('/yoga-centers/{country}/{region}/{state}/{city}/{slug}', [
            'controller' => 'Partners',
            'action' => 'details'
        ])
        ->setPass(['country', 'region', 'state', 'city', 'slug']);

        // SEO route for course type
        $builder->connect('/yoga-courses/{country}/type/{type}', [
            'controller' => 'Courses',
            'action' => 'index'
        ])->setPass(['country', 'type']);

        $builder->connect('/yoga-courses/{country}/{region}/type/{type}', [
            'controller' => 'Courses',
            'action' => 'index'
        ])
        // ->setPatterns(['slug' => '[a-z0-9\-]+'])
        ->setPass(['country', 'region', 'type']);

        // for yoga style //
        $builder->connect('/yoga-courses/{country}/style/{style}', [
            'controller' => 'Courses',
            'action' => 'index'
        ]) ->setPass(['country','style']);

        $builder->connect('/yoga-courses/{country}/{region}/style/{style}', [
            'controller' => 'Courses',
            'action' => 'index'
        ])
        ->setPass(['country', 'region', 'style']);

        // Route for special need
         $builder->connect('/yoga-courses/{country}/special-need/{specialNeeds}', [
            'controller' => 'Courses',
            'action' => 'index'
        ])->setPass(['country', 'specialNeeds']);

        $builder->connect('/yoga-courses/{country}/{region}/special-need/{specialNeeds}', [
            'controller' => 'Courses',
            'action' => 'index'
        ])->setPass(['country', 'region', 'specialNeeds']);

        // Modality-based routes for course lists (must come before general country routes)
        $builder->connect('/yoga-courses/video', [
            'controller' => 'Courses',
            'action' => 'index',
            'modality' => 'video'
        ]);

        $builder->connect('/yoga-courses/online', [
            'controller' => 'Courses',
            'action' => 'index',
            'modality' => 'online'
        ]);

        // Modality-based routes for course details
        $builder->connect('/yoga-courses/video/{centerSlug}', [
            'controller' => 'Courses',
            'action' => 'details',
            'modality' => 'video'
        ])
        ->setPatterns(['id' => '\d+', 'centerSlug' => '[^/]+'])
        ->setPass(['id', 'centerSlug']);

        $builder->connect('/yoga-courses/online/{centerSlug}', [
            'controller' => 'Courses',
            'action' => 'details',
            'modality' => 'online'
        ])
        ->setPatterns(['id' => '\d+', 'centerSlug' => '[^/]+'])
        ->setPass(['id', 'centerSlug']);

        // SEO-friendly route: /lang/yoga-courses/india
        $builder->connect('/yoga-courses/{country}', [
            'controller' => 'Courses',
            'action' => 'index'
        ])
         ->setPass(['country']);

        $builder->connect('/yoga-courses/{country}/{region}', [
            'controller' => 'Courses',
            'action' => 'index'
        ])
        ->setPass(['country', 'region']);
        
        $builder->connect('/yoga-courses/{country}/{region}/{state}', [
            'controller' => 'Courses',
            'action' => 'index'
        ])
        ->setPass(['country', 'region', 'state']);

        $builder->connect('/yoga-courses/{country}/{region}/{state}/{city}', [
            'controller' => 'Courses',
            'action' => 'index'
        ])
         ->setPass(['country', 'region', 'state', 'city']);

         $builder->connect('/yoga-courses/{country}/{region}/{state}/{city}/{locality}/{slug}', [
            'controller' => 'Courses',
            'action' => 'details'
        ])
        ->setPatterns(['slug' => '[^/]+'])
        ->setPass(['country', 'region', 'state', 'city', 'locality', 'slug']);


        $builder->connect('/yoga-courses/{country}/{region}/{state}/{city}/{slug}', [
            'controller' => 'Courses',
            'action' => 'details'
        ])
        ->setPatterns(['slug' => '[^/]+'])
        ->setPass(['country', 'region', 'state', 'city', 'slug']);
        // END SEO //

        $builder->connect('/', ['controller' => 'Home', 'action' => 'index']);
        $builder->connect('/data-protection-policy', ['controller' => 'Policies', 'action' => 'dataProtection']);
        $builder->connect('/pages/*', 'Pages::display');
        $builder->connect('/partners/submit', ['controller' => 'Partners', 'action' => 'submit']);
        // course details - slug
        $builder->connect('/courses/details/:slug', ['controller' => 'Courses', 'action' => 'details'])
        ->setPass(['slug']);

        // partner details - slug 
        $builder->connect('/partners/detail/:slug', ['controller' => 'Partners', 'action' => 'details'])
        ->setPass(['slug']);

        // ImportController route for direct access
        $builder->connect('/import/localities', ['controller' => 'Import', 'action' => 'importLocalities']);
            
        $builder->connect('/partners/checkPartnerDuplicateEmail', ['controller' => 'Partners', 'action' => 'checkPartnerDuplicateEmail']);
        $builder->connect('/partners/checkPartnerDuplicatePhone', ['controller' => 'Partners', 'action' => 'checkPartnerDuplicatePhone']);
        $builder->connect('/partners/get-states', ['controller' => 'Partners', 'action' => 'getStates']);
        $builder->connect('/partners/get-cities', ['controller' => 'Partners', 'action' => 'getCities']);

        $builder->connect('/city-detail/{name}', ['controller' => 'Home', 'action' => 'cityDetails'])
            ->setPass(['name']);

        // Vanity URLs for yoga destinations
        $builder->connect('/yoga/{country}/{region}/{state}', ['controller' => 'Home', 'action' => 'cityDetails'])
            ->setPass(['state'])
            ->setPatterns([
                'country' => '[a-zA-Z0-9\-]+',
                'region' => '[a-zA-Z0-9\-]+', 
                'state' => '[a-zA-Z0-9\-]+'
            ]);
            
        $builder->connect('/yoga/{country}/{region}/{state}/{city}', ['controller' => 'Home', 'action' => 'cityDetails'])
            ->setPass(['city'])
            ->setPatterns([
                'country' => '[a-zA-Z0-9\-]+',
                'region' => '[a-zA-Z0-9\-]+',
                'state' => '[a-zA-Z0-9\-]+',
                'city' => '[a-zA-Z0-9\-]+'
            ]);

        // booking //
        // $builder->connect('/bookings/add', ['controller' => 'Bookings', 'action' => 'add', ['_method' => 'POST']]);
        // $builder->connect('/booking/confirmation/{booking_id}', ['controller' => 'Bookings', 'action' => 'confirmation'])->setPass(['booking_id']);
        // $builder->connect('/booking/enrollment/{course_slug}', ['controller' => 'Bookings', 'action' => 'enrollment'])
        // ->setPass(['slug']);

        $builder->connect('/destination-list', ['controller' => 'Home', 'action' => 'destinationListPage']);

        // Profile routes
        $builder->connect('/profile', ['controller' => 'Profile', 'action' => 'index']);
        // $builder->post('/profile/update-profile', ['controller' => 'Profile', 'action' => 'updateProfile']);
        $builder->connect('/profile/update-profile', [
            'controller' => 'Profile',
            'action' => 'updateProfile',
            '_method' => ['POST']
        ]);
        $builder->connect('/profile/orders', ['controller' => 'Profile', 'action' => 'orders']);
        $builder->connect('/profile/bookmarks', ['controller' => 'Profile', 'action' => 'wishlist']);
        $builder->connect('/profile/change-password', ['controller' => 'Profile', 'action' => 'changePassword']);

        $builder->connect('/profile/toggle', ['controller' => 'Profile', 'action' => 'toggle']);
        $builder->connect('/profile/check', ['controller' => 'Profile', 'action' => 'check']);
        $builder->connect('/profile/deleteBookmark', ['controller' => 'Profile', 'action' => 'deleteBookmark']);
        $builder->fallbacks();
    });

    $routes->scope('/', function (RouteBuilder $builder): void {
        $builder->setRouteClass(DashedRoute::class);
        $builder->setExtensions(['json']);
       $builder->connect('/profile/update-profile', [
            'controller' => 'Profile',
            'action' => 'updateProfile',
            '_method' => ['POST']
        ]);
         $builder->connect('/profile/bookmarks', ['controller' => 'Profile', 'action' => 'wishlist']);
        $builder->connect('/profile/toggle', ['controller' => 'Profile', 'action' => 'toggle']);
        $builder->connect('/profile/check', ['controller' => 'Profile', 'action' => 'check']);
         $builder->connect('/profile/deleteBookmark', ['controller' => 'Profile', 'action' => 'deleteBookmark']);
        $builder->connect('/forgot-password', ['controller' => 'Login', 'action' => 'forgotPassword']);
        $builder->connect('/reset-password/{token}', ['controller' => 'Login', 'action' => 'resetPassword'])->setPass(['token']);
        $builder->connect('/verify-otp', ['controller' => 'Login', 'action' => 'verifyOtpForgot']);
        $builder->connect('/logout', ['controller' => 'Login', 'action' => 'logout']);

        // Sitemap routes - using explicit patterns to avoid extension parsing issues
        $builder->connect('/sitemap.xml', ['controller' => 'Sitemap', 'action' => 'index'], ['pass' => []]);
        $builder->connect('/sitemap-index.xml', ['controller' => 'Sitemap', 'action' => 'sitemapIndex'], ['pass' => []]);
        $builder->connect('/sitemap/detail-courses.xml', ['controller' => 'Sitemap', 'action' => 'detailCourses'], ['pass' => []]);
        $builder->connect('/sitemap/list-courses.xml', ['controller' => 'Sitemap', 'action' => 'listCourses'], ['pass' => []]);
        $builder->connect('/sitemap/detail-partners.xml', ['controller' => 'Sitemap', 'action' => 'detailPartners'], ['pass' => []]);
        $builder->connect('/sitemap/list-partners.xml', ['controller' => 'Sitemap', 'action' => 'listPartners'], ['pass' => []]);

        $builder->connect('/', ['controller' => 'Home', 'action' => 'index']);
        // Add the data protection policy route here
        $builder->connect('/data-protection-policy', ['controller' => 'Policies', 'action' => 'dataProtection']);

        $builder->connect('/', ['controller' => 'Home', 'action' => 'index']);
        $builder->connect('/pages/*', 'Pages::display');
        $builder->connect('/partners/submit', ['controller' => 'Partners', 'action' => 'submit']);

        // Add signup route
        $builder->connect('/signup', ['controller' => 'Signup', 'action' => 'index']);
        $builder->connect('/login', ['controller' => 'Login', 'action' => 'index']);

        // course details - slug
        $builder->connect('/courses/details/:slug', ['controller' => 'Courses', 'action' => 'courseDetails'])
            ->setPass(['slug']);

        // partner details - slug 
        $builder->connect('/partners/detail/:slug', ['controller' => 'Partners', 'action' => 'details'])
            ->setPass(['slug']);

        // Make sure this route exists and is correctly defined
        $builder->connect('/signup/register', ['controller' => 'Signup', 'action' => 'register']);
        $builder->connect('/signup/sendOtp', ['controller' => 'Signup', 'action' => 'sendOtp']);
        $builder->connect('/signup/verifyOtp', ['controller' => 'Signup', 'action' => 'verifyOtp']);
        $builder->connect('/login/sendOtp', ['controller' => 'Login', 'action' => 'sendOtp']);
        $builder->connect('/login/verifyOtp', ['controller' => 'Login', 'action' => 'verifyOtp']);
        $builder->connect('/login/customer-login', ['controller' => 'Login', 'action' => 'customerLogin']);
        // Social login routes - very specific callback route first
        $builder->connect('/login/social/google/callback', ['controller' => 'Login', 'action' => 'socialLoginCallback', 'provider' => 'google']);
        $builder->connect('/login/social/facebook/callback', ['controller' => 'Login', 'action' => 'socialLoginCallback', 'provider' => 'facebook']);
        $builder->connect('/login/social/:provider/callback', ['controller' => 'Login', 'action' => 'socialLoginCallback'])->setPass(['provider']);
        $builder->connect('/login/social', ['controller' => 'Login', 'action' => 'socialLogin']);
        $builder->connect('/test-auth', ['controller' => 'Test', 'action' => 'index']);
         // booking //
        $builder->connect('/bookings/add', ['controller' => 'Bookings', 'action' => 'add', ['_method' => 'POST']]);
        // $builder->connect('/booking/confirmation', ['controller' => 'Bookings', 'action' => 'confirmation']);
        
        // $builder->connect('/booking/enroll/{course_slug}', ['controller' => 'Bookings', 'action' => 'enrollment'])
        // ->setPass(['slug']);

        $builder->fallbacks();
    });

    // Partner routes using $routes instead of $builder
    $routes->connect('/partners/get-states', ['controller' => 'Partners', 'action' => 'getStates']);
    $routes->connect('/partners/get-cities', ['controller' => 'Partners', 'action' => 'getCities']);
    $routes->connect('/partners/checkPartnerDuplicateEmail', ['controller' => 'Partners', 'action' => 'checkPartnerDuplicateEmail']);
    $routes->connect('/partners/checkPartnerDuplicatePhone', ['controller' => 'Partners', 'action' => 'checkPartnerDuplicatePhone']);
    $routes->connect('/partners/generate-captcha', ['controller' => 'Partners', 'action' => 'generateCaptcha']);
    $routes->connect('/partners/verify-captcha', ['controller' => 'Partners', 'action' => 'verifyCaptcha']);

    // NewsLetter Subscription //
    $routes->connect('/subscribe-newsletter', ['controller' => 'Home', 'action' => 'subscribeNewsletter'], ['_method' => 'POST']);

    $routes->connect('/unsubscribe-newsletter/{id}', ['controller' => 'Home', 'action' => 'unsubscribeNewsletter'])
        ->setPass(['id']);
};
