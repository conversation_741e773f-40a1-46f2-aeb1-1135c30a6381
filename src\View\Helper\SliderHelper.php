<?php
// src/View/Helper/SliderHelper.php
namespace App\View\Helper;

use Cake\View\Helper;

class SliderHelper extends Helper
{
    protected array $helpers = ['Html', 'Card'];

    /**
     * Render a Swiper slider.
     * Each slide array/entity may contain:
     *  - image (string URL)
     *  - title (string)
     *  - center (string) center/organization name
     *  - rating (float) course rating
     *  - description (string) course description
     *  - date (string) course date
     *  - mode (string) delivery mode (Hybrid, Online, etc.)
     *  - language (string) course language
     *  - link (string) optional
     *  - alt (string) optional
     *
     * @param array $slides Slides data.
     * @param array $options Options: id, outerClass, containerClass, swiperClass, navigation, pagination, init (bool), swiperOptions (array), title (string), description (string), navStyle (string: 'nav-style-1' or 'nav-style-2')
     * @return string HTML markup for the slider section.
     */
    public function render(array $slides, array $options = []): string
    {
        $config = $this->normalizeOptions($options);
        $slidesHtml = $this->buildSlidesHtml($slides, $config);
        $navResult = $this->buildNavigationHtml($config['navStyle']);
        $navHtml = $config['navigation'] ? $navResult['html'] : '';
        $navClasses = $config['navigation'] ? $navResult['classes'] : ['prev' => '', 'next' => ''];
        $paginationHtml = $config['pagination'] ? '<div class="swiper-pagination"></div>' : '';
        $headerHtml = $this->buildHeaderHtml($config);

        $html = '<section class="' . h($config['outerClass']) . '">'
            . '<div class="' . h($config['containerClass']) . '">'
            . $headerHtml
            . '<div id="' . h($config['id']) . '" class="' . h($config['swiperClass']) . '">'
            . '<div class="swiper-wrapper">' . $slidesHtml . '</div>'
            . $paginationHtml
            . '</div>'
            . $navHtml
            . '</div>'
            . '</section>';

        if ($config['init']) {
            // Update navigation selectors with unique classes
            $config['swiperOptions']['navigation']['nextEl'] = '.' . $navClasses['next'];
            $config['swiperOptions']['navigation']['prevEl'] = '.' . $navClasses['prev'];
            $html .= $this->buildInitScript($config['id'], $config['swiperOptions']);
        }

        // Add description toggle script once (delegated to CardHelper)
        $html .= '<script>
            function toggleDescription(button, event) {
                // Prevent event bubbling to parent link
                if (event) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                
                const descText = button.previousElementSibling;
                const span = descText.querySelector("span");
                const fullText = descText.getAttribute("data-full");
                
                if (span.classList.contains("line-clamp-2")) {
                    span.classList.remove("line-clamp-2");
                    span.innerHTML = fullText;
                    button.textContent = "View less";
                    descText.style.maxHeight = "none";
                    descText.style.overflow = "visible";
                } else {
                    span.classList.add("line-clamp-2");
                    span.innerHTML = fullText;
                    button.textContent = "View more";
                    descText.style.maxHeight = "";
                    descText.style.overflow = "";
                }
            }
        </script>';

        return $html;
    }

    private function normalizeOptions(array $options): array
    {
        $defaults = [
            'id' => 'swiper_' . uniqid(),
            'outerClass' => 'w-full py-8',
            'containerClass' => 'max-w-7xl mx-auto px-4 md:px-8 lg:px-12 relative',
            'swiperClass' => 'swiper relative overflow-hidden',
            'imageClass' => 'w-full h-48 object-cover',
            'cardClass' => 'group h-full flex flex-col bg-white rounded-2xl shadow-sm ring-1 ring-slate-200 hover:shadow-lg transition duration-200 overflow-hidden',
            'title' => '',
            'description' => '',
            'navigation' => true,
            'pagination' => true,
            'navStyle' => 'nav-style-1', // nav-style-1 (default: side), nav-style-2 (top right)
            'init' => true,
            'swiperOptions' => [
                'loop' => false,
                'slidesPerView' => 3.5,
                'spaceBetween' => 24,
                'navigation' => [ 'nextEl' => '.swiper-button-next', 'prevEl' => '.swiper-button-prev' ],
                'pagination' => [ 'el' => '.swiper-pagination', 'clickable' => true ],
                'breakpoints' => [
                    0 => ['slidesPerView' => 1.1, 'spaceBetween' => 16],
                    640 => ['slidesPerView' => 2.1, 'spaceBetween' => 20],
                    1024 => ['slidesPerView' => 3.2, 'spaceBetween' => 24],
                    1280 => ['slidesPerView' => 3.5, 'spaceBetween' => 24],
                ]
            ]
        ];
        return $options + $defaults;
    }

    private function buildSlidesHtml(array $slides, array $config): string
    {
        $cardWrapperCls = $config['cardClass'];
        $imageClass = $config['imageClass'];
        
        if (empty($slides)) {
            return '<div class="swiper-slide px-2 pb-4"><div class="' . h($cardWrapperCls) . ' flex items-center justify-center h-40 text-sm text-gray-500">No items</div></div>';
        }

        $html = '';
        foreach ($slides as $slide) {
            $slideData = $this->normalizeSlideData($slide);
            $html .= $this->buildSingleSlide($slideData, $cardWrapperCls, $imageClass);
        }
        
        return $html;
    }

    private function normalizeSlideData($slide): array
    {
        return [
            'image' => is_array($slide) ? ($slide['image'] ?? '') : ($slide->image ?? ''),
            'title' => is_array($slide) ? ($slide['title'] ?? '') : ($slide->title ?? ''),
            'center' => is_array($slide) ? ($slide['center'] ?? '') : ($slide->center ?? ''),
            'rating' => is_array($slide) ? ($slide['rating'] ?? 4.5) : ($slide->rating ?? 4.5),
            'description' => is_array($slide) ? ($slide['description'] ?? '') : ($slide->description ?? ''),
            'date' => is_array($slide) ? ($slide['date'] ?? '') : ($slide->date ?? ''),
            'mode' => is_array($slide) ? ($slide['mode'] ?? '') : ($slide->mode ?? ''),
            'language' => is_array($slide) ? ($slide['language'] ?? 'English') : ($slide->language ?? 'English'),
            'link' => is_array($slide) ? ($slide['link'] ?? null) : ($slide->link ?? null),
            'alt' => is_array($slide) ? ($slide['alt'] ?? '') : ($slide->alt ?? ''),
        ];
    }

    private function buildSingleSlide(array $slideData, string $cardWrapperCls, string $imageClass): string
    {
        // Use CardHelper to render the card content
        $cardOptions = [
            'cardClass' => $cardWrapperCls,
            'imageClass' => $imageClass,
            'wrapperClass' => 'swiper-slide px-2 pb-4'
        ];
        
        return $this->Card->render($slideData, $cardOptions);
    }

    private function buildNavigationHtml(string $navStyle = 'nav-style-1'): array
    {
        $uniqueId = uniqid();
        $prevClass = "swiper-button-prev-{$uniqueId}";
        $nextClass = "swiper-button-next-{$uniqueId}";
        
        if ($navStyle === 'nav-style-2') {
            // Top right positioning
            $html = '<div class="absolute top-4 right-4 flex items-center gap-2 z-20">'
                . '<div class="' . $prevClass . ' relative !top-0 !left-0 !right-auto !bottom-auto !translate-x-0 !translate-y-0 bg-white/90 hover:bg-white text-slate-700 hover:text-rose-700 w-10 h-10 rounded-full flex items-center justify-center shadow-lg ring-1 ring-slate-200 transition after:content-[\'\'] cursor-pointer">'
                . '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7"/></svg>'
                . '</div>'
                . '<div class="' . $nextClass . ' relative !top-0 !left-0 !right-auto !bottom-auto !translate-x-0 !translate-y-0 bg-white/90 hover:bg-white text-slate-700 hover:text-rose-700 w-10 h-10 rounded-full flex items-center justify-center shadow-lg ring-1 ring-slate-200 transition after:content-[\'\'] cursor-pointer">'
                . '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7"/></svg>'
                . '</div>'
                . '</div>';
        } else {
            // Default nav-style-1: Side positioning
            $html = '<div class="' . $prevClass . ' absolute -left-6 md:-left-12 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-slate-700 hover:text-rose-700 w-12 h-12 rounded-full flex items-center justify-center shadow-lg ring-1 ring-slate-200 transition z-10 after:content-[\'\'] cursor-pointer">'
                . '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7"/></svg>'
                . '</div>'
                . '<div class="' . $nextClass . ' absolute -right-6 md:-right-12 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-slate-700 hover:text-rose-700 w-12 h-12 rounded-full flex items-center justify-center shadow-lg ring-1 ring-slate-200 transition z-10 after:content-[\'\'] cursor-pointer">'
                . '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7"/></svg>'
                . '</div>';
        }
        
        return [
            'html' => $html,
            'classes' => [
                'prev' => $prevClass,
                'next' => $nextClass
            ]
        ];
    }

    private function buildInitScript(string $id, array $options): string
    {
        $jsonOptions = json_encode($options, JSON_UNESCAPED_SLASHES);
        $safeId = addslashes($id);
        return "<script>document.addEventListener('DOMContentLoaded',function(){if(typeof Swiper!=='undefined'){new Swiper('#$safeId',$jsonOptions);}});</script>";
    }

    private function buildHeaderHtml(array $config): string
    {
        if (empty($config['title']) && empty($config['description'])) {
            return '';
        }

        $titleHtml = '';
        $descriptionHtml = '';

        if (!empty($config['title'])) {
            $titleHtml = '<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4 text-left">' . h($config['title']) . '</h2>';
        }

        if (!empty($config['description'])) {
            $descriptionHtml = '<p class="text-lg text-gray-600 mb-8 text-left">' . h($config['description']) . '</p>';
        }

        return '<div class="text-center mb-12">' . $titleHtml . $descriptionHtml . '</div>';
    }
}
