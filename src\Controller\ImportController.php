<?php
namespace App\Controller;

use Cake\Datasource\ConnectionManager;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Cake\I18n\FrozenTime;

class ImportController extends AppController
{
    public function importLocalities()
    {
        // Only allow admin users
        $user = $this->request->getSession()->read('Auth');
        if (empty($user) || (isset($user['role']) && $user['role'] !== 'admin')) {
            $this->set('error', 'Unauthorized: Only admin can access this route.');
            $this->render(false);
            return;
        }
        $excelPath = WWW_ROOT . 'files' . DS . 'localities.xls';
        if (!file_exists($excelPath)) {
            $this->set(compact('excelPath'));
            $this->set('error', 'Excel file not found.');
            return;
        }

        $spreadsheet = IOFactory::load($excelPath);
        $connection = ConnectionManager::get('default');
        $now = FrozenTime::now();
        $nowFormatted = $now->format('Y-m-d H:i:s');
        $overallInserted = 0;
        $overallSkipped = 0;
        $cityStats = [];
        $missingCities = [];

        foreach ($spreadsheet->getSheetNames() as $sheetName) {
            $city = $connection->execute('SELECT id, name FROM cities WHERE name = :name', ['name' => $sheetName])->fetch('assoc');
            if (!$city) {
                $missingCities[] = $sheetName;
                continue;
            }
            $cityId = $city['id'];
            $cityName = $city['name'];
            $sheet = $spreadsheet->getSheetByName($sheetName);
            $rows = $sheet->toArray(null, true, true, true);
            $inserted = 0;
            $skipped = 0;
            $existsCount = 0;
            foreach ($rows as $row) {
                foreach ($row as $cell) {
                    $localityName = is_string($cell) ? trim($cell) : '';
                    if ($localityName === '') {
                        continue;
                    }
                    $exists = $connection->execute('SELECT id FROM localities WHERE name = :name AND city_id = :city_id', [
                        'name' => $localityName,
                        'city_id' => $cityId
                    ])->fetch('assoc');
                    if ($exists) {
                        $skipped++;
                        $existsCount++;
                        continue;
                    }
                    $connection->insert('localities', [
                        'name' => $localityName,
                        'city_id' => $cityId,
                        'created_at' => $nowFormatted,
                        'updated_at' => $nowFormatted
                    ]);
                    $inserted++;
                }
            }
            $cityStats[] = [
                'city' => $cityName,
                'sheet' => $sheetName,
                'inserted' => $inserted,
                'skipped' => $skipped,
                'existsCount' => $existsCount
            ];
            $overallInserted += $inserted;
            $overallSkipped += $skipped;
        }
        $this->set(compact('cityStats', 'missingCities', 'overallInserted', 'overallSkipped'));
    }
}
