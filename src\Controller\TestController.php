<?php
declare(strict_types=1);

namespace App\Controller;

class TestController extends AppController
{
    public function index()
    {
        $this->viewBuilder()->setLayout('ajax');
        
        $request = $this->request;
        $currentHost = $request->host();
        $scheme = $request->is('https') ? 'https' : 'http';
        $port = $request->port();
        
        if ($port && $port != 80 && $port != 443) {
            $siteUrl = $scheme . '://' . $currentHost . ':' . $port . '/';
        } else {
            $siteUrl = $scheme . '://' . $currentHost . '/';
        }
        
        $googleRedirectUri = rtrim($siteUrl, '/') . '/login/social/google/callback';
        
        echo "<h1>Social Login Test - Dynamic URL Detection</h1>";
        echo "<p><strong>Current Host:</strong> " . $currentHost . "</p>";
        echo "<p><strong>Scheme:</strong> " . $scheme . "</p>";
        echo "<p><strong>Port:</strong> " . ($port ?: 'default') . "</p>";
        echo "<p><strong>Built Site URL:</strong> " . $siteUrl . "</p>";
        echo "<p><strong>Google Redirect URI:</strong> " . $googleRedirectUri . "</p>";
        echo "<hr>";
        echo "<p>Testing Google Login:</p>";
        echo "<a href='/login/social?provider=google' style='background: #4285f4; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Google Login</a><br><br>";
        echo "<a href='/login' style='background: #ccc; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Login Page</a>";
        
        exit();
    }
}
