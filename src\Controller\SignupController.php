<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Event\EventInterface;
use Cake\Utility\Security;
use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\Log\Log;
use Cake\Routing\Router;
use Cake\Core\Configure;

class SignupController extends AppController
{
    protected $UsersTable;

    public function initialize(): void
    {
        parent::initialize();
        $this->loadComponent('Flash');
        $this->loadComponent('Global');
        $this->UsersTable = $this->fetchTable('Users');

        // Ensure we have the Authentication component loaded
        if (!isset($this->Authentication) && method_exists($this, 'loadComponent')) {
            $this->loadComponent('Authentication.Authentication');
        }
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['index', 'register', 'verifyOtp', 'verifyOtpAndRegister', 'socialLogin', 'sendOtp', 'sendOtpAllOrOne']);
    }

    public function index()
    {
        $identity = $this->Authentication->getResult();
       
        if ($identity && $identity->isValid()) {
            return $this->redirect($this->referer());
        }
        
        $this->viewBuilder()->setLayout('webauth');
         // Check if there's a redirect parameter
        $qryRedirect = $this->request->getQuery('redirect');
        $signup = $this->UsersTable->newEmptyEntity();
        $this->set(compact('signup', 'qryRedirect'));
    }

    /**
     * Register a new user with password
     */
    public function register()
    {
        $this->request->allowMethod(['post']);
      
        if ($this->request->is('ajax')) {
            $this->viewBuilder()->setLayout('ajax');
            $response = ['success' => false, 'message' => ''];

            $data = $this->request->getData();
            $user = $this->UsersTable->newEmptyEntity();
        
            // Check if required fields are present
            if (empty($data['first_name']) || empty($data['email_or_mobile']) || empty($data['password'])) {
                Log::error('Missing required fields in signup data');
                $response['message'] = 'Please fill in all required fields.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Determine if input is email or mobile
            $emailOrMobile = trim($data['email_or_mobile']);
            $isEmail = filter_var($emailOrMobile, FILTER_VALIDATE_EMAIL);
            $isMobile = preg_match('/^[0-9]{10}$/', $emailOrMobile);

            if (!$isEmail && !$isMobile) {
                Log::error('Invalid email or mobile format: ' . $emailOrMobile);
                $response['message'] = 'Please enter a valid email address or mobile number.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Check if email/mobile already exists
            $query = $this->UsersTable->find()->where(['status !=' => 'D','user_type' => 'Customer']);

            if ($isEmail) {
                $query->where(['email' => $emailOrMobile]);
            } else {
                $query->where(['mobile' => $emailOrMobile]);
            }

            $existingUser = $query->first();

            if ($existingUser) {
                $type = $isEmail ? 'email' : 'mobile number';
                Log::error("This {$type} already exists: " . $emailOrMobile);
                $response['message'] = "This {$type} is already registered.";
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Prepare user data
            $userData = [
                'first_name' => $data['first_name'],
                'password' => $data['password'],
                'role_id' => 2, // Regular user role
                'status' => 'A', // Active
                'user_type' => 'Customer' // Set user type as Customer
            ];

            // Set email or mobile based on input type
            if ($isEmail) {
                $userData['email'] = $emailOrMobile;
            } else {
                $userData['mobile'] = $emailOrMobile;
                $userData['country_code'] = '91'; // Default to India code
            }

            $user = $this->UsersTable->patchEntity($user, $userData);

            // Begin transaction
            $connection = $this->UsersTable->getConnection();
            $connection->begin();

            try {
                if ($this->UsersTable->save($user)) {
                    // Verify the password was saved correctly
                    $savedUser = $this->UsersTable->get($user->id);

                    // Now create a customer record
                    $customersTable = $this->fetchTable('Customers');
                    $customer = $customersTable->newEmptyEntity();
                    $customer = $customersTable->patchEntity($customer, [
                        'user_id' => $user->id,
                        'status' => 'A' // Active
                    ]);

                    if ($customersTable->save($customer)) {
                        $connection->commit();
                        
                        // Send welcome email if email is provided
                        if ($isEmail) {
                           $this->_sendWelcomeEmail($savedUser,$data['password']);
                        }
                        
                        $response['success'] = true;
                        $response['message'] = 'Account created successfully!';
                        $this->Authentication->setIdentity($user);
                        
                        $redirectUrl = Router::url(['controller' => 'Home', 'action' => 'index'], true);
                        if ($data['reqdirect_url']) {
                            $redirectUrl = Router::url($data['reqdirect_url']);
                        }
                        // $redirectUrl = Router::url(['controller' => 'Login', 'action' => 'index'], true);
                        // if ($data['reqdirect_url']) {
                        //      $redirectUrl = Router::url(['controller' => 'Login', 'action' => 'index',
                        //         '?' => [
                        //             'redirect' => $data['reqdirect_url']
                        //         ]
                        //     ], true);
                        // }
                   
                        $response['redirect'] = $redirectUrl;
                        
                    } else {
                        $connection->rollback();
                        $response['message'] = 'Failed to create customer record.';
                        $response['errors'] = $customer->getErrors();
                    }
                } else {
                    Log::error('Failed to save user: ' . json_encode($user->getErrors()));
                    $connection->rollback();
                    $response['message'] = 'Failed to create account.';
                    $response['errors'] = $user->getErrors();
                }
            } catch (\Exception $e) {
                Log::error('Exception during signup: ' . $e->getMessage());
                Log::error('Stack trace: ' . $e->getTraceAsString());
                $connection->rollback();
                $response['message'] = 'An error occurred: ' . $e->getMessage();
            }

            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        // For non-AJAX requests, redirect
        $this->Flash->error('Please use the signup form to register.');
        return $this->redirect(['action' => 'index']);
    }

    /**
     * Send welcome email to newly registered user
     */
    protected function _sendWelcomeEmail($user,$password)
    {
        try {
            // Try using Global component if available
            if (!isset($this->Global)) {
                $this->loadComponent('Global');
            }
            
            // Prepare email data
            $emailData = [
                'firstName' => $user->first_name,
                'email' => $user->email,
                'password' => $password,
                'datetime' => date('Y-m-d H:i:s')
            ];
            
            // Try to send email using Global component
            $result = false;
            if (isset($this->Global) && method_exists($this->Global, 'send_email')) {
                $result = $this->Global->send_email(
                    $user->email,
                    Configure::read('Settings.FROM_EMAIL'),
                    'Welcome to Yoga.in - Your Account Details',
                    'customer_welcome',
                    $emailData
                );
            }
            
            // If Global component fails or is not available, use fallback method
            if (!$result) {
                return $this->_sendWelcomeEmailFallback($user,$password);
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send welcome email: ' . $e->getMessage());
            // Try fallback method
            return $this->_sendWelcomeEmailFallback($user,$password);
        }
    }

    /**
     * Fallback method to send welcome email directly using Mailer
     */
    protected function _sendWelcomeEmailFallback($user,$password)
    {
        try {
            Log::debug('Using fallback method to send welcome email');

            $mailer = new \Cake\Mailer\Mailer('default');
            $mailer->setFrom([Configure::read('Settings.FROM_EMAIL') => 'Yoga.in'])
                ->setTo($user->email)
                ->setSubject('Welcome to Yoga.in')
                ->setViewVars([
                    'firstName' => $user->first_name,
                    'email' => $user->email,
                    'password' => $password,
                    'datetime' => date('Y-m-d H:i:s')
                ]);

            // Set email format
            $mailer->setEmailFormat('both');

            // Set template using viewBuilder
            $mailer->viewBuilder()
                ->setTemplate('customer_welcome')
                ->setLayout('default');

            $result = $mailer->deliver();
            
            if ($result) {
                Log::info('Welcome email sent successfully to: ' . $user->email);
                return true;
            } else {
                Log::error('Failed to send welcome email via Mailer');
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Exception in fallback email sending: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send OTP to user's email or mobile
     */
    public function sendOtp()
    {
        $this->request->allowMethod(['post']);

        if ($this->request->is('ajax')) {
            $this->viewBuilder()->setLayout('ajax');
            $response = ['success' => false, 'message' => '', 'input_type' => ''];

            $data = $this->request->getData();
            $emailOrMobile = trim($data['email'] ?? '');

            if (empty($emailOrMobile)) {
                $response['message'] = 'Email or mobile number is required.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Determine if input is email or mobile
            $isEmail = filter_var($emailOrMobile, FILTER_VALIDATE_EMAIL);
            $isMobile = preg_match('/^[0-9]{10}$/', $emailOrMobile); // Exactly 10 digits

            if (!$isEmail && !$isMobile) {
                Log::error('Invalid email or mobile format: ' . $emailOrMobile);
                $response['message'] = $isEmail === false && strpos($emailOrMobile, '@') !== false
                    ? 'Please enter a valid email address.'
                    : 'Please enter a valid 10-digit mobile number.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Check if email/mobile already exists in the users table
            $query = $this->UsersTable->find()->where(['status !=' => 'D','user_type'=> 'Customer']);
            
            if ($isEmail) {
                $query->where(['email' => $emailOrMobile]);
            } else {
                $query->where(['mobile' => $emailOrMobile]);
            }
            
            $existingUser = $query->first();
            
            if ($existingUser) {
                $type = $isEmail ? 'email' : 'mobile number';
                Log::error("This {$type} already exists: " . $emailOrMobile);
                $response['message'] = "This {$type} is already registered.";
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Generate a random 4-digit OTP
            $otp = 1111; // sprintf('%04d', mt_rand(0, 9999));

            if ($isMobile) {
                // Send OTP via 2Factor.in API
                $twoFactorService = new \App\Service\TwoFactorService();
                $sendResult = $twoFactorService->sendOtp($emailOrMobile);
                
                if ($sendResult['success']) {
                    // Store session ID for verification later
                    $this->request->getSession()->write('signup_otp', [
                        'email_or_mobile' => $emailOrMobile,
                        'session_id' => $sendResult['session_id'],
                        'expires' => time() + 300 // 5 minutes expiry
                    ]);
                    
                    $response['success'] = true;
                    $response['message'] = 'OTP has been sent to your mobile number.';
                    $response['input_type'] = 'mobile';
                } else {
                    $response['message'] = 'Failed to send OTP: ' . $sendResult['message'];
                }
            } else {
                // For email, store OTP in email-specific session
                $this->request->getSession()->write('email_id', [
                    'email_id' => $emailOrMobile,
                    'otp' => $otp,
                    'expires' => time() + 300 // 5 minutes expiry
                ]);
                
                // Send OTP via email
                try {
                    $email = new \Cake\Mailer\Mailer('default');
                    $email->setFrom([Configure::read('Settings.FROM_EMAIL') => 'Yoga.in'])
                        ->setTo($emailOrMobile)
                        ->setSubject('Your OTP for Yoga.in Registration')
                        ->setEmailFormat('html')  // Changed from 'both' to 'html'
                        ->setViewVars(['otp' => $otp])
                        ->viewBuilder()
                        ->setTemplate('otp_email');
                    
                    $email->deliver();
                    
                    $response['success'] = true;
                    $response['message'] = 'OTP has been sent to your email address.';
                    $response['input_type'] = 'email';
                } catch (\Exception $e) {
                    $response['message'] = 'Failed to send OTP email: ' . $e->getMessage();
                }
            }

            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        return $this->redirect(['action' => 'index']);
    }
    public function sendOtpAllOrOne()
    {
        $this->request->allowMethod(['post']);

        if ($this->request->is('ajax')) {
            $this->viewBuilder()->setLayout('ajax');
            $response = ['success' => false, 'message' => '', 'input_type' => ''];

            $data = $this->request->getData();
            $emailOrMobile = trim($data['email_or_mobile'] ?? '');
            $countryCode = trim($data['country_code'] ?? '');
            $emailId = trim($data['email_id'] ?? '');

            // Check if both are empty
            if (empty($emailOrMobile) && empty($emailId)) {
                $response['message'] = 'Email or mobile number is required.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Validate mobile (with country code)
            $isMobile = false;
            if (!empty($emailOrMobile)) {
                // Accept only digits for mobile and country code
                $isMobile = preg_match('/^[0-9]{10}$/', $emailOrMobile) && preg_match('/^[0-9]{1,4}$/', $countryCode);
            }

            // Validate email
            $isEmail = false;
            if (!empty($emailId)) {
                $isEmail = filter_var($emailId, FILTER_VALIDATE_EMAIL);
            }

            // If neither is valid
            if (!$isMobile && !$isEmail) {
                $response['message'] = 'Please enter a valid email address or 10-digit mobile number with country code.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Check if mobile already exists
            if ($isMobile) {
                $queryMobile = $this->UsersTable->find()
                    ->where([
                        'status !=' => 'D',
                        'user_type' => 'Customer',
                        'mobile' => $emailOrMobile,
                        'country_code' => $countryCode
                    ]);
                $existingMobileUser = $queryMobile->first();
                if ($existingMobileUser) {
                    $response['message'] = "This mobile number is already registered.";
                    return $this->response->withType('application/json')
                        ->withStringBody(json_encode($response));
                }
            }

            // Check if email already exists
            if ($isEmail) {
                $queryEmail = $this->UsersTable->find()
                    ->where([
                        'status !=' => 'D',
                        'user_type' => 'Customer',
                        'email' => $emailId
                    ]);
                $existingEmailUser = $queryEmail->first();
                if ($existingEmailUser) {
                    $response['message'] = "This email address is already registered.";
                    return $this->response->withType('application/json')
                        ->withStringBody(json_encode($response));
                }
            }

            // Generate a random 4-digit OTP
            $otp = sprintf('%04d', mt_rand(0, 9999));

            // Send OTP to mobile if provided
            if ($isMobile) {
                $twoFactorService = new \App\Service\TwoFactorService();
                $sendResult = $twoFactorService->sendOtp($countryCode . $emailOrMobile);

                if ($sendResult['success']) {
                    $this->request->getSession()->write('signup_otp', [
                        'email_or_mobile' => $emailOrMobile,
                        'country_code' => $countryCode,
                        'session_id' => $sendResult['session_id'],
                        'expires' => time() + 300 // 5 minutes expiry
                    ]);
                    $response['success'] = true;
                    $response['message'] = 'OTP has been sent to your mobile number.';
                    $response['input_type'] = 'mobile';
                } else {
                    $response['message'] = 'Failed to send OTP: ' . $sendResult['message'];
                }
            }

            // Send OTP to email if provided
            if ($isEmail) {
                $this->request->getSession()->write('email_id', [
                    'email_id' => $emailId,
                    'otp' => $otp,
                    'expires' => time() + 300 // 5 minutes expiry
                ]);
                try {
                    $email = new \Cake\Mailer\Mailer('default');
                    $email->setFrom([Configure::read('Settings.FROM_EMAIL') => 'Yoga.in'])
                        ->setTo($emailId)
                        ->setSubject('Your OTP for Yoga.in Registration')
                        ->setEmailFormat('html')
                        ->setViewVars(['otp' => $otp])
                        ->viewBuilder()
                        ->setTemplate('otp_email');
                    $email->deliver();

                    $response['success'] = true;
                    $response['message'] .= ($response['message'] ? ' ' : '') . 'OTP has been sent to your email address.';
                    $response['input_type'] .= ($response['input_type'] ? ',' : '') . 'email';
                } catch (\Exception $e) {
                    $response['message'] .= ($response['message'] ? ' ' : '') . 'Failed to send OTP email: ' . $e->getMessage();
                }
            }

            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        return $this->redirect(['action' => 'index']);
    }

    /**
     * Verify OTP and register user
     */
    public function verifyOtp()
    {
        $this->request->allowMethod(['post']);

        if ($this->request->is('ajax')) {
            $this->viewBuilder()->setLayout('ajax');
            $response = ['success' => false, 'message' => ''];

            $data = $this->request->getData();
            
            $otp = $data['otp'] ?? '';
            $firstName = $data['first_name'] ?? '';
            $emailOrMobile = $data['email'] ?? '';

            if (empty($otp) || empty($firstName) || empty($emailOrMobile)) {
                $response['message'] = 'All fields are required.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Get stored OTP data from session - check both mobile and email sessions
            $storedMobileOtpData = $this->request->getSession()->read('signup_otp');
            $storedEmailOtpData = $this->request->getSession()->read('email_id');

            $isVerified = false;
            $isEmail = filter_var($emailOrMobile, FILTER_VALIDATE_EMAIL);

            // Check mobile OTP if it's a mobile number
            if (!$isEmail && $storedMobileOtpData) {
                if ($storedMobileOtpData['email_or_mobile'] === $emailOrMobile && time() <= $storedMobileOtpData['expires']) {
                    if (isset($storedMobileOtpData['session_id'])) {
                        // Verify via 2Factor API
                        $twoFactorService = new \App\Service\TwoFactorService();
                        $verifyResult = $twoFactorService->verifyOtp($storedMobileOtpData['session_id'], $otp);
                        $isVerified = $verifyResult['success'];
                    } else {
                        // Verify local OTP
                        $isVerified = $storedMobileOtpData['otp'] === $otp;
                    }
                }
            }

            // Check email OTP if it's an email address and mobile verification failed
            if (!$isVerified && $isEmail && $storedEmailOtpData) {
                if ($storedEmailOtpData['email_id'] === $emailOrMobile && time() <= $storedEmailOtpData['expires']) {
                    $isVerified = $storedEmailOtpData['otp'] === $otp;
                }
            }

            // If no valid session found
            if (!$storedMobileOtpData && !$storedEmailOtpData) {
                $response['message'] = 'No OTP session found. Please request a new OTP.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            if (!$isVerified) {
                // Provide more specific error messages
                if ($isEmail && !$storedEmailOtpData) {
                    $response['message'] = 'No email OTP session found. Please request a new OTP.';
                } elseif (!$isEmail && !$storedMobileOtpData) {
                    $response['message'] = 'No mobile OTP session found. Please request a new OTP.';
                } elseif ($isEmail && $storedEmailOtpData && time() > $storedEmailOtpData['expires']) {
                    $response['message'] = 'Email OTP has expired. Please request a new OTP.';
                } elseif (!$isEmail && $storedMobileOtpData && time() > $storedMobileOtpData['expires']) {
                    $response['message'] = 'Mobile OTP has expired. Please request a new OTP.';
                } else {
                    $response['message'] = 'Invalid OTP. Please check your OTP and try again.';
                }
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // OTP is valid, create user account
            $user = $this->UsersTable->newEmptyEntity();
            
            // Determine if input is email or mobile
            $isEmail = filter_var($emailOrMobile, FILTER_VALIDATE_EMAIL);
            
            $userData = [
                'first_name' => $firstName,
                'role_id' => 2, // Regular user role
                'status' => 'A', // Active
                'user_type' => 'Customer', // Set user type as Customer
                // Generate a random password since we're using OTP
                'password' => Security::hash(Security::randomString(16), 'sha256', true),
            ];

            // Set email or mobile based on input type
            if ($isEmail) {
                $userData['email'] = $emailOrMobile;
            } else {
                $userData['mobile'] = $emailOrMobile;
                $userData['country_code'] = '91'; // Default to India code
            }

            $user = $this->UsersTable->patchEntity($user, $userData);
            // Begin transaction
            $connection = $this->UsersTable->getConnection();
            $connection->begin();

            try {
                if ($this->UsersTable->save($user)) {
                    // Create customer record
                    $customersTable = $this->fetchTable('Customers');
                    $customer = $customersTable->newEmptyEntity();
                    $customer = $customersTable->patchEntity($customer, [
                        'user_id' => $user->id,
                        'status' => 'A' // Active
                    ]);

                    if ($customersTable->save($customer)) {
                        $connection->commit();

                        // Clear both OTP sessions
                        $this->request->getSession()->delete('signup_otp');
                        $this->request->getSession()->delete('email_id');

                        $this->Authentication->setIdentity($user);
                        $response['success'] = true;
                        $response['message'] = 'Your account has been created successfully.';

                        // redirect to referral page //
                        $redirectUrl = Router::url(['controller' => 'Home', 'action' => 'index'], true);
                        if ($data['reqdirect_url']) {
                            $redirectUrl = Router::url($data['reqdirect_url']);
                        }

                        $response['redirect'] = $redirectUrl;
                    } else {
                        $connection->rollback();
                        $response['message'] = 'Failed to create customer record.';
                        $response['errors'] = $customer->getErrors();
                    }
                } else {
                    $connection->rollback();
                    $response['message'] = 'Failed to create account.';
                    $response['errors'] = $user->getErrors();
                }
            } catch (\Exception $e) {
                $connection->rollback();
                $response['message'] = 'An error occurred: ' . $e->getMessage();
            }

            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        return $this->redirect(['action' => 'index']);
    }

    /**
     * Verify OTP and register user
     */
    public function verifyOtpAll()
    {
        $this->request->allowMethod(['post']);

        if ($this->request->is('ajax')) {
            $this->viewBuilder()->setLayout('ajax');
            $response = ['success' => false, 'message' => ''];

            $data = $this->request->getData();
            
            $otp = $data['otp'] ?? '';
            $firstName = $data['first_name'] ?? '';
            $emailOrMobile = $data['email'] ?? '';

            if (empty($otp) || empty($firstName) || empty($emailOrMobile)) {
                $response['message'] = 'All fields are required.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Get stored OTP data from session
            $storedOtpData = $this->request->getSession()->read('signup_otp');

            if (!$storedOtpData || $storedOtpData['email_or_mobile'] !== $emailOrMobile || time() > $storedOtpData['expires']) {
                $response['message'] = 'Invalid or expired OTP session. Please try again.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            $isVerified = false;

            // Determine if we're verifying via 2Factor API or local OTP
            if (isset($storedOtpData['session_id'])) {
                // Verify via 2Factor API
                $twoFactorService = new \App\Service\TwoFactorService();
                $verifyResult = $twoFactorService->verifyOtp($storedOtpData['session_id'], $otp);
                $isVerified = $verifyResult['success'];
            } else {
                // Verify local OTP
                $isVerified = $storedOtpData['otp'] === $otp;
            }

            if (!$isVerified) {
                $response['message'] = 'Invalid OTP. Please try again.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // OTP is valid, create user account
            $user = $this->UsersTable->newEmptyEntity();
            
            // Determine if input is email or mobile
            $isEmail = filter_var($emailOrMobile, FILTER_VALIDATE_EMAIL);
            
            $userData = [
                'first_name' => $firstName,
                'role_id' => 2, // Regular user role
                'status' => 'A', // Active
                'user_type' => 'Customer', // Set user type as Customer
                // Generate a random password since we're using OTP
                'password' => Security::hash(Security::randomString(16), 'sha256', true),
            ];

            // Set email or mobile based on input type
            if ($isEmail) {
                $userData['email'] = $emailOrMobile;
            } else {
                $userData['mobile'] = $emailOrMobile;
                $userData['country_code'] = '91'; // Default to India code
            }

            $user = $this->UsersTable->patchEntity($user, $userData);
            // Begin transaction
            $connection = $this->UsersTable->getConnection();
            $connection->begin();

            try {
                if ($this->UsersTable->save($user)) {
                    $connection->commit();
                    $this->request->getSession()->delete('signup_otp');
                    $this->Authentication->setIdentity($user);
                    $response['success'] = true;
                    $response['message'] = 'Your account has been created successfully.';

                    // redirect to referral page //
                    $redirectUrl = Router::url(['controller' => 'Home', 'action' => 'index'], true);
                    if ($data['reqdirect_url']) {
                        $redirectUrl = Router::url($data['reqdirect_url']);
                    }
                        
                    $response['redirect'] = $redirectUrl;
                } else {
                    $connection->rollback();
                    $response['message'] = 'Failed to create account.';
                    $response['errors'] = $user->getErrors();
                }
            } catch (\Exception $e) {
                $connection->rollback();
                $response['message'] = 'An error occurred: ' . $e->getMessage();
            }

            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        return $this->redirect(['action' => 'index']);
    }

    /**
     * Verify OTP and register user with full form data
     */
    public function verifyOtpAndRegister()
    {
        $this->request->allowMethod(['post']);

        if ($this->request->is('ajax')) {
            $this->viewBuilder()->setLayout('ajax');
            $response = ['success' => false, 'message' => ''];

            $data = $this->request->getData();
            
            // Required fields from the form
            $otp = $data['otp'] ?? '';
            $firstName = $data['first_name'] ?? '';
            $lastName = $data['last_name'] ?? '';
            $emailId = $data['email_id'] ?? '';
            $mobileNumber = $data['email_or_mobile'] ?? '';
            $password = $data['password'] ?? '';
            $countryCode = $data['country_code'] ?? '91';

            // Determine primary contact method
            $emailOrMobile = '';
            if ($mobileNumber) {
                $emailOrMobile = $mobileNumber;
            } else if ($emailId) {
                $emailOrMobile = $emailId;
            }

            if (empty($otp) || empty($firstName) || empty($emailOrMobile) || empty($password)) {
                $response['message'] = 'All required fields must be provided.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Step 1: Verify OTP first
            $storedOtpData = $this->request->getSession()->read('signup_otp');
            $storedEmailOtpData = $this->request->getSession()->read('email_id');

            $isVerified = false;
            
            // Check mobile OTP if mobile was used
            if ($mobileNumber && $storedOtpData) {
                if ($storedOtpData['email_or_mobile'] === $mobileNumber && time() <= $storedOtpData['expires']) {
                    if (isset($storedOtpData['session_id'])) {
                        // Verify via 2Factor API
                        $twoFactorService = new \App\Service\TwoFactorService();
                        $verifyResult = $twoFactorService->verifyOtp($storedOtpData['session_id'], $otp);
                        $isVerified = $verifyResult['success'];
                    } else {
                        // Verify local OTP
                        $isVerified = $storedOtpData['otp'] === $otp;
                    }
                }
            }
            
            // Check email OTP if email was used and mobile OTP failed/not available
            if (!$isVerified && $emailId && $storedEmailOtpData) {
                if ($storedEmailOtpData['email_id'] === $emailId && time() <= $storedEmailOtpData['expires']) {
                    $isVerified = $storedEmailOtpData['otp'] === $otp;
                }
            }

            if (!$isVerified) {
                $response['message'] = 'Invalid or expired OTP. Please try again.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Step 2: OTP is valid, now register the user
            
            // Determine if primary contact is email or mobile
            $isEmail = filter_var($emailOrMobile, FILTER_VALIDATE_EMAIL);
            $isMobile = preg_match('/^[0-9]{10}$/', $emailOrMobile);

            if (!$isEmail && !$isMobile) {
                $response['message'] = 'Please enter a valid email address or mobile number.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Check if email/mobile already exists
            $query = $this->UsersTable->find()->where(['status !=' => 'D','user_type' => 'Customer']);

            if ($isEmail) {
                $query->where(['email' => $emailOrMobile]);
            } else {
                $query->where(['mobile' => $emailOrMobile, 'country_code' => $countryCode]);
            }

            $existingUser = $query->first();

            if ($existingUser) {
                $type = $isEmail ? 'email' : 'mobile number';
                $response['message'] = "This {$type} is already registered.";
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Prepare user data
            $userData = [
                'first_name' => $firstName,
                'password' => $password,
                'role_id' => 2, // Regular user role
                'status' => 'A', // Active
                'user_type' => 'Customer' // Set user type as Customer
            ];

            // Add optional last name
            if ($lastName) {
                $userData['last_name'] = $lastName;
            }

            // Set primary contact field
            if ($isEmail) {
                $userData['email'] = $emailOrMobile;
            } else {
                $userData['mobile'] = $emailOrMobile;
                $userData['country_code'] = $countryCode;
            }

            // Add secondary contact if both email and mobile provided
            if ($emailId && $mobileNumber) {
                if ($isEmail) {
                    // Primary is email, secondary is mobile
                    $userData['mobile'] = $mobileNumber;
                    $userData['country_code'] = $countryCode;
                } else {
                    // Primary is mobile, secondary is email
                    $userData['email'] = $emailId;
                }
            }

            $user = $this->UsersTable->newEmptyEntity();
            $user = $this->UsersTable->patchEntity($user, $userData);

            // Begin transaction
            $connection = $this->UsersTable->getConnection();
            $connection->begin();

            try {
                if ($this->UsersTable->save($user)) {
                    // Create customer record
                    $customersTable = $this->fetchTable('Customers');
                    $customer = $customersTable->newEmptyEntity();
                    $customer = $customersTable->patchEntity($customer, [
                        'user_id' => $user->id,
                        'status' => 'A' // Active
                    ]);

                    if ($customersTable->save($customer)) {
                        $connection->commit();
                        
                        // Clear OTP sessions
                        $this->request->getSession()->delete('signup_otp');
                        $this->request->getSession()->delete('email_id');
                        
                        // Send welcome email if email is provided
                        if ($userData['email'] ?? null) {
                            $this->_sendWelcomeEmail($user, $password);
                        }
                        
                        // Set user identity for authentication
                        $this->Authentication->setIdentity($user);
                        
                        $response['success'] = true;
                        $response['message'] = 'Account created successfully!';
                        
                        $redirectUrl = Router::url(['controller' => 'Home', 'action' => 'index'], true);
                        if ($data['reqdirect_url']) {
                            $redirectUrl = Router::url($data['reqdirect_url']);
                        }
                        $response['redirect'] = $redirectUrl;
                        
                    } else {
                        $connection->rollback();
                        $response['message'] = 'Failed to create customer record.';
                        $response['errors'] = $customer->getErrors();
                    }
                } else {
                    Log::error('Failed to save user: ' . json_encode($user->getErrors()));
                    $connection->rollback();
                    $response['message'] = 'Failed to create account.';
                    $response['errors'] = $user->getErrors();
                }
            } catch (\Exception $e) {
                Log::error('Exception during OTP registration: ' . $e->getMessage());
                $connection->rollback();
                $response['message'] = 'An error occurred: ' . $e->getMessage();
            }

            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        return $this->redirect(['action' => 'index']);
    }

    /**
     * Helper method to verify OTP
     * Implement your actual verification logic
     */
    private function verifyUserOtp($email, $otp)
    {
        // Implement your OTP verification logic
        // This is just a placeholder
        return false;
    }

    /**
     * Handle social login (Google, Facebook)
     */
    public function socialLogin()
    {
        $this->request->allowMethod(['post']);

        // Ensure it's an AJAX request
        $isAjax = $this->request->is('ajax') ||
            $this->request->getHeader('X-Requested-With') === ['XMLHttpRequest'] ||
            $this->request->getHeader('Accept') === ['application/json'];

        if ($isAjax) {
            $this->viewBuilder()->setLayout('ajax');
            $response = ['success' => false, 'message' => ''];

            // Get JSON data
            $data = $this->request->getData();

            // Validate required fields
            if (empty($data['provider']) || empty($data['email']) || empty($data['socialID'])) {
                $response['message'] = 'Missing required social login information.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Check if user already exists with this email
            $existingUser = $this->UsersTable->find()
                ->where(['email' => $data['email'], 'status !=' => 'D'])
                ->first();

            // Begin transaction
            $connection = $this->UsersTable->getConnection();
            $connection->begin();

            try {
                if ($existingUser) {
                    // User exists, update social provider info if needed
                    if (empty($existingUser->social_provider) || empty($existingUser->socialID)) {
                        $existingUser = $this->UsersTable->patchEntity($existingUser, [
                            'social_provider' => $data['provider'],
                            'socialID' => $data['socialID']
                        ]);

                        if (!$this->UsersTable->save($existingUser)) {
                            throw new \Exception('Failed to update user social information.');
                        }
                    }

                    $user = $existingUser;
                } else {
                    // Create new user
                    $userData = [
                        'first_name' => $data['first_name'] ?? '',
                        'last_name' => $data['last_name'] ?? '',
                        'email' => $data['email'],
                        // Generate a random password since they're using social login
                        'password' => (new DefaultPasswordHasher())->hash(Security::randomString(16)),
                        'social_provider' => $data['provider'],
                        'socialID' => $data['socialID'],
                        'profile_pic' => $data['profile_pic'] ?? null,
                        'role_id' => 2, // Regular user role
                        'status' => 'A', // Active
                        'user_type' => 'Customer' // Set user type as Customer
                    ];

                    $user = $this->UsersTable->newEmptyEntity();
                    $user = $this->UsersTable->patchEntity($user, $userData);
                    if (!$this->UsersTable->save($user)) {
                        throw new \Exception('Failed to create user account: ' . json_encode($user->getErrors()));
                    }

                    // Create customer record
                    $customersTable = $this->fetchTable('Customers');
                    $customer = $customersTable->newEmptyEntity();
                    $customer = $customersTable->patchEntity($customer, [
                        'user_id' => $user->id,
                        'status' => 'A' // Active
                    ]);

                    if (!$customersTable->save($customer)) {
                        throw new \Exception('Failed to create customer record: ' . json_encode($customer->getErrors()));
                    }
                }

                // Commit transaction
                $connection->commit();
                // Set user identity for authentication
                $this->Authentication->setIdentity($user);

                $response['success'] = true;
                $response['message'] = 'Successfully logged in with ' . ucfirst($data['provider']) . '.';
                $response['redirect'] = $this->Url->build(['controller' => 'Login', 'action' => 'index'], ['fullBase' => false]);
            } catch (\Exception $e) {
                Log::error('Social login error: ' . $e->getMessage());
                $connection->rollback();
                $response['message'] = 'An error occurred: ' . $e->getMessage();
            }

            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        // For non-AJAX requests, redirect
        $this->Flash->error('Invalid request method.');
        return $this->redirect(['action' => 'index']);
    }
}






