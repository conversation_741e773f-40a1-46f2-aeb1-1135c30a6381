<?php 
$courseCountry =  ucfirst($this->request->getParam('country'));
$this->assign('title', 'Yoga in India - Find Yoga Courses, Retreats & Teacher Training'); 
$this->assign('meta_desc', 'Find Yoga Courses, Retreats & Teacher Training');
 ?>

 <?php
ob_start();
echo '<script type="application/ld+json">' . json_encode($allCourseSchema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . '</script>';
$this->assign('json_ld_schema', ob_get_clean());
?>
<?php
$ogTags = <<<HTML
<meta property="og:title" content="Yoga in India - Find Yoga Courses, Retreats & Teacher Training" />
<meta property="og:description" content="Find Yoga Courses, Retreats & Teacher Training" />
<meta property="og:url" content="{$this->Url->build(null, ['fullBase' => true])}" />
<meta property="og:type" content="website" />
HTML;
$this->assign('og_tags', $ogTags);
?>



<link rel="stylesheet" href="<?= $this->Url->webroot('css/course.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/filter.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/card.css') ?>">
<script src="<?= $this->Url->webroot('js/course.js') ?>"></script>
<script src="<?= $this->Url->webroot('js/filter-list.js') ?>"></script>
<script>
    window.isCustomerLoggedIn = <?= $this->Identity->isLoggedIn() ? 'true' : 'false' ?>;
</script>

<script src="https://rawgit.com/ckrack/scrollsnap-polyfill/develop/dist/scrollsnap-polyfill.bundled.js"></script>
<section class="course-list-container overflow-y-auto max-h-screen snap-y snap-mandatory md:overflow-visible md:max-h-none md:snap-none">
    <div class="px-6 md:px-10 lg:px-25 xl:pl-40 xl:pr-21">
         <nav class="flex breadcrumb-nav desktop-view" aria-label="Breadcrumb">
               <ol class="pt-4 flex items-center space-x-1 md:space-x-0 rtl:space-x-reverse">
                <li>
                    <a href="<?= $this->Url->build(['controller' => 'Home', 'action' => 'index']) ?>" 
                    class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        Home
                    </a>
                </li>
                <li class="flex items-center">
                    <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                    </svg>
                    <a href="#" class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        Courses
                    </a>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-1" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">India</span>
                    </div>
                </li>
            </ol>
        </nav>
                        <div x-data="{ showToast: false, toastMessage: '', toastColor: 'bg-green-500' }"
                            x-show="showToast"
                            x-transition:enter="transition ease-out duration-300 transform" x-transition:enter-start="translate-x-full opacity-0" x-transition:enter-end="translate-x-0 opacity-100" x-transition:leave="transition ease-in duration-200 transform" x-transition:leave-start="translate-x-0 opacity-100" x-transition:leave-end="translate-x-full opacity-0"
                            :class="'fixed right-4 top-4 text-white px-4 py-2 rounded shadow-lg z-50 ' + toastColor"
                            :style="showToast ? 'display: block;' : 'display: none;'"
                            x-init="window.addEventListener('show-toast', e => { toastMessage = e.detail.message; toastColor = e.detail.color; showToast = true; setTimeout(() => { showToast = false; }, 10000); });">
                            <span x-text="toastMessage"></span>
                        </div>
        <h1 class="heading desktop-view">Yoga Courses In <?= ucfirst($this->request->getParam('country')); ?></h1>
        <div class="search-content-wrapper px-0" x-data="loadCourses()" x-init="init(); exposeForExternal()" x-ref="alpineCourseData">
            <div class="mobile-filter mobile-view snap-start" x-show="isMobile">
                <div class="filter-container">
                    <div class="mt-[15px] flex items-center justify-between">
                        <h1 class="heading">Yoga Courses In India</h1>
                    </div>
    
                    <div class="flex items-center justify-between h-[50px] px-[10px]" x-show="mobileData.length > 0">
                        <span class="font-medium"><span x-text="total"></span> <span
                                x-text="total === 1 ? 'Result' : 'Results'"></span></span>
            
                        <!-- Right: Filter icon and label -->
                        <div class="flex items-center space-x-2 cursor-pointer filter-wrapper" @click="showFilters">
                            <!-- <img src="<?= $this->Url->webroot('img/filter-icon.png') ?>" class="w-[20px] h-[20px]"
                                alt="filter icon">
                            <span>Filters</span> -->
                            <!-- Filter count badge -->
                            <!-- <template x-if="filterCount > 0">
                                <span
                                    class="ml-1 inline-flex items-center justify-center text-xs font-semibold text-white bg-default-600 rounded-full w-5 h-5">
                                    <span x-text="filterCount"></span>
                                </span>
                            </template> -->
                        </div>
                    </div>
                    <div class="sort-filter-container">
                        <div class="sort-filter-wrapper">
                            <div class="sort"><i class="fas fa-sort"></i> Sort By</div>
                            <div class="filter filter-wrapper" @click="showFilters">
                                <span>
                                    <i class="fas fa-filter"></i> Filter
                                    <!-- Filter count badge -->
                                    <span>
                                        <template x-if="filterCount > 0">
                                            <span
                                                class="ml-1 inline-flex items-center justify-center text-xs font-semibold text-white bg-default-600 rounded-full w-5 h-5">
                                                <span x-text="filterCount" class="count"></span>
                                            </span>
                                        </template>
                                    </span>
                                </span>
                            </div>
                        </div>
                        <div class="sort-option">
                            <ul>
                                <li class="list-head"  @click="selectedSort = ''; fetchData()"  value="">Sort By</li>
                                <li value="featured" @click="selectedSort = 'featured'; fetchData()">Featured</li>
                                <li value="newest" @click="selectedSort = 'newest'; fetchData()">Newest First</li>
                                <li value="price_asc" @click="selectedSort = 'price_asc'; fetchData()">Price: Low to High</li>
                                <li value="price_desc" @click="selectedSort = 'price_desc'; fetchData()">Price: High to Low</li>
                            </ul>
                        </div>
                    </div>
                    <div class="filters">
                        <div class="filters-container bg-white">
                            <div x-data="{ search: '' }" class="mx-auto pr-0 course-filter">
                                <div class="close flex items-center justify-between">
                                    <span>Filters</span>
                                    <!-- <button><i class="fas fa-times"></i></button> -->
                                    <!-- Clear All Link -->
                                    <template x-if="Object.values(selectedFilters).some(f => 
                                        Array.isArray(f) 
                                            ? f.some(v => v !== 'all' && v.trim() !== '') 
                                            : f && f !== 'all' && f.trim() !== ''
                                        )">
                                        <a href="javascript:void(0)"
                                            class="text-blue-700 hover:underline font-medium filter-clear-all whitespace-nowrap"
                                            @click="clearAllFilters">
                                            Clear All
                                        </a>
                                    </template>
                                </div>
                                <div class="flex items-center justify-between px-2 space-x-2">
                                    <!-- Search Box -->
                                    <div class="mobile-search relative flex-1">
                                        <input type="search" name="search" placeholder="Search"
                                            class="bg-white w-full pr-8 search" x-model="search"
                                            @input.debounce.500ms="pageNumber = 0;" />
                                        <i class="fas fa-search absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500"
                                            x-show="search.length === 0" x-transition></i>
                                    </div>
                                </div>
                                <div class="flex h-100" x-data="{tab: 1}">
                                    <div class="flex flex-col justify-start w-[44%] h-[100vh] overflow-scroll bg-[#EFEFEF] pb-[180px]">
                                        <div class="text-sm border-b-2 border-slate-200 tab"
                                            :class="{'z-20 transform tab-active font-bold': tab === 1}"
                                            @click.prevent="tab = 1">
                                            <div class="tab-content w-auto">
                                                <p class="mt-0 mb-0 text-[#000]">Course Type</p>
                                            </div>
                                        </div>
                                        <div class="text-sm border-b-2 border-slate-200 tab"
                                            :class="{'z-20 transform tab-active font-bold': tab === 2}"
                                            @click.prevent="tab = 2" @click.prevent="tab = 2">
                                            <div class="tab-content w-auto">
                                                <p class="mt-0 mb-0 text-[#000]">Yoga Style</p>
                                            </div>
                                        </div>
                                        <div class="text-sm border-b-2 border-slate-200 tab"
                                            :class="{'z-20 transform tab-active font-bold': tab === 3}"
                                            @click.prevent="tab = 3">
                                            <div class="tab-content w-auto">
                                                <p class="mt-0 mb-0 text-[#000]">Special Need</p>
                                            </div>
                                        </div>
                                        <div class="text-sm border-b-2 border-slate-200 tab"
                                            :class="{'z-20 transform tab-active font-bold': tab === 4}"
                                            @click.prevent="tab = 4" @click.prevent="tab = 4">
                                            <div class="tab-content w-auto">
                                                <p class="mt-0 mb-0 text-[#000]">Where</p>
                                            </div>
                                        </div>
                                        <div class="text-sm border-b-2 border-slate-200 tab"
                                            :class="{'z-20 transform tab-active font-bold': tab === 5}"
                                            @click.prevent="tab = 5" @click.prevent="tab = 5">
                                            <div class="tab-content w-auto">
                                                <p class="mt-0 mb-0 text-[#000]">When</p>
                                            </div>
                                        </div>
                                        <div class="text-sm border-b-2 border-slate-200 tab"
                                            :class="{'z-20 transform tab-active font-bold': tab === 6}"
                                            @click.prevent="tab = 6" @click.prevent="tab = 6">
                                            <div class="tab-content w-auto">
                                                <p class="mt-0 mb-0 text-[#000]">Language</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-[79%] 2xl:w-102 ml-[20px] tab-options">
                                        <div class="space-y-6 h-[100vh] overflow-scroll pb-[180px]" x-show="tab === 1">
                                            <p>
                                                <label class="cursor-pointer">
                                                    <input type="checkbox" value="all"
                                                        :checked="selectedTypes.length === 0 || selectedTypes.includes('all')"
                                                        @change="handleFilterChange($data, 'selectedTypes', 'all', event)">
                                                    All Course Types </label>
                                            </p>
                                        <template x-if="Object.keys(filterLabels.selectedTypes).length">
                                            <template x-for="(label, slug) in filterLabels.selectedTypes" :key="slug">
                                                    <p class="flex items-center">
                                                    <label class="custom-checkbox">
                                                        <input type="checkbox"
                                                            :value="slug"
                                                            x-model="selectedTypes"
                                                            @change="handleFilterChange($data, 'selectedTypes', slug, $event)">
                                                            <span class="checkmark"></span>
                                                            <span x-text="label"></span>
                                                        
                                                            <span class="list-filter-count"
                                                            x-text="'(' + (filterCounts.course_types[slug] || 0) + ')'"></span>
                                                    </label>
                                                </p>
                                            </template>
                                        </template>
                                        </div>

                                        <div class="space-y-6 h-[100vh] overflow-scroll pb-[180px]" x-show="tab === 2" x-data="{
                                                styles: window.yogaStyles
                                            }">
                                            <p>
                                                <label class="cursor-pointer">
                                                    <input type="checkbox" value="all"
                                                        :checked="selectedStyles.length === 0 || selectedStyles.includes('all')"
                                                        @change="handleFilterChange($data, 'selectedStyles', 'all', event)">
                                                    All Yoga Styles
                                                </label>
                                            </p>
                                            <template x-for="([slug, style], index) in Object.entries(styles).slice(0, showAll ? undefined : 4)" :key="slug">
                                                <p>
                                                    <label class="cursor-pointer">
                                                        <input type="checkbox" :value="slug" x-model="selectedStyles"
                                                            :checked="selectedStyles.includes(slug)"
                                                            @change="handleFilterChange($data, 'selectedStyles', slug, event)">
                                                        <span x-text="style"></span>
                                                        <span
                                                            x-text="'(' + (filterCounts.yoga_styles[`${slug}`] || 0) + ')'"></span>
                                                    </label>
                                                </p>
                                            </template>

                                            <a href="javascript:void(0)" @click="showAll = !showAll" class="btn-view">
                                                <span x-text="showAll ? 'Show Less' : 'Show All'"></span>
                                            </a>
                                        </div>
                                        <div class="space-y-6 h-[100vh] overflow-scroll pb-[180px]" x-show="tab === 3">
                                            <p>
                                                <label class="cursor-pointer">
                                                    <input type="checkbox" value="all"
                                                        :checked="selectedNeeds.length === 0 || selectedNeeds.includes('all')"
                                                        @change="handleFilterChange($data, 'selectedNeeds', 'all', event)">
                                                    All Special Needs
                                                </label>
                                            </p>
                                         <template x-if="Object.keys(filterLabels.selectedNeeds).length">
                                            <template x-for="(label, slug) in filterLabels.selectedNeeds" :key="slug">
                                                    <p class="flex items-center">
                                                    <label class="custom-checkbox">
                                                        <input type="checkbox"
                                                            :value="slug"
                                                            x-model="selectedNeeds"
                                                            @change="handleFilterChange($data, 'selectedNeeds', slug, $event)">
                                                            <span class="checkmark"></span>
                                                            <span x-text="label"></span>
                                                        
                                                            <span class="list-filter-count"
                                                            x-text="'(' + (filterCounts.special_needs[slug] || 0) + ')'"></span>
                                                    </label>
                                                </p>
                                            </template>
                                        </template>
                                        </div>
                                        <div class="space-y-6 h-[100vh] overflow-scroll pb-[180px]" x-show="tab === 4">
                                            <p>
                                                <label class="cursor-pointer">
                                                    <input type="checkbox" name="siteWhere" value="all"
                                                        :checked="selectedWhere.includes('all')"
                                                        @change="handleFilterChange($data, 'selectedWhere', 'all', event)">Anywhere
                                                </label>
                                            </p>

                                            <p>
                                                <label class="cursor-pointer">
                                                    <input class="bg-white" type="text" name="location"
                                                        placeholder="Enter District, City, State..."
                                                        x-model="selectedLocation" x-init="selectedLocation = '<?= h($locationUrl) ?>';  fetchData();"
                                                        @change="pageNumber = 0; fetchData()">
                                                </label>
                                            </p>
                                            <template x-for="mode in modalities" :key="mode.id">
                                                <p>
                                                    <label class="cursor-pointer">
                                                        <input type="checkbox"
                                                            name="mode"
                                                            :value="mode.id"
                                                            x-model="selectedMode"
                                                            @change="handleFilterChange($data, 'selectedMode', mode.id, $event)">
                                                        <span x-text="mode.name"></span>
                                                        <span class="list-filter-count"
                                                            x-text="'(' + (filterCounts.modes[mode.id] || 0) + ')'"></span>
                                                    </label>
                                                </p>
                                            </template>
                                        </div>
                                        <div class="space-y-6 h-[100vh] overflow-scroll pb-[180px]" x-show="tab === 5">
                                            <!-- <p class="title">When</p> -->
                                            <?php if (!empty($months)) {
                                                foreach ($months as $month) {
                                                    ?>
                                                    <p>
                                                        <label class="cursor-pointer">
                                                            <input type="checkbox" value="<?= $month ?>" name="start_month"
                                                                x-model="selectedCourseDate"
                                                                @change="handleFilterChange($data, 'selectedCourseDate', '<?= $month ?>', event)"><?= $month ?>
                                                            <span class="list-filter-count"
                                                                x-text="'(' + (filterCounts.course_dates['<?= $month ?>'] || 0) + ')'"></span>
                                                        </label>
                                                    </p>
                                                <?php }
                                            } ?>

                                        </div>

                                        <div class="space-y-6 h-100" x-show="tab === 6">
                                            <p>
                                                <label class="cursor-pointer">
                                                    <input type="checkbox" value="all" x-model="selectedLanguage"
                                                        @change="handleFilterChange($data, 'selectedLanguage', 'all', event)"
                                                        :checked="selectedLanguage.length === 0 || selectedLanguage.includes('all')">All Languages
                                                </label>
                                            </p>
                                            <?php if (!empty($languages)) {
                                                foreach ($languages as $language) {
                                                    ?>
                                                    <p>
                                                        <label class="cursor-pointer">
                                                            <input type="checkbox" value="<?= $language ?>" name="language"
                                                                x-model="selectedLanguage"
                                                                @change="handleFilterChange($data, 'selectedLanguage', '<?= $language ?>', event)"><?= $language ?>
                                                            <span class="list-filter-count"
                                                                x-text="'(' + (filterCounts.languages['<?= $language ?>'] || 0) + ')'"></span>
                                                        </label>
                                                    </p>
                                                <?php }
                                            } ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="apply-close-footer">
                                    <div class="apply-close-wrapper">
                                        <div class="close">
                                            <button>close</button>
                                        </div>
                                        <div class="apply-filter">
                                            <button @click="applyMobileFilters"
                                                class="px-4 py-2 rounded filter-apply-btn mb-2">
                                                Apply
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="list-search">
                <div class="flex items-start justify-between">
                    <div class="left-details">
                        <div class="flex flex-col space-y-2 desktop-view">
                            <div class="sorting flex items-center justify-between">
                                <span class="font-medium mr-2"><span x-text="total"></span> <span x-text="total === 1 ? 'Result' : 'Results'"></span></span>    
                                <select x-model="selectedSort" @change="fetchData()">
                                    <option value="">Sort By</option>
                                    <!-- <option value="popular">Most Popular</option> -->
                                    <!-- <option value="rating">Highest Rated</option> -->
                                    <option value="featured">Featured</option>
                                    <option value="newest">Newest First</option>
                                    <option value="price_asc">Price: Low to High</option>
                                    <option value="price_desc">Price: High to Low</option>
                                </select>
                            </div>
                        </div>
                        <div class="search-content flex flex-wrap gap-2 gap-y-3" x-show="!isMobile">
                            <template x-for="(filters, category) in selectedFiltersLabels" :key="category">
                                <template x-if="filters.length > 0 && filters.some(f => f !== 'all' && f.trim() !== '')">
                                    <template x-for="(filter, index) in filters" :key="index">
                                        <template x-if="filter !== 'all' && filter.trim() !== ''">
                                            <span>
                                                <span x-text="getFilterLabel(category, filter)"></span>
                                                <i class="fas fa-times text-xs ml-2 cursor-pointer"
                                                @click="removeFilter(category, index)"></i>
                                            </span>
                                        </template>
                                    </template>
                                </template>
                            </template>
                        </div>
                          
                        <div x-show="isLoading" x-cloak id="loader" class="text-gray-500 spinner-loader"></div>

                        <template x-if="!isLoading && filteredData.length === 0 && mobileData.length === 0">
                            <div class="text-gray-500">No data available.</div>
                        </template>
                                            
                        <div class="featured-yoga course-card-container grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-4 desktop-view"
                            x-show="!isMobile">
                            <template x-for="item in filteredData" :key="item.id"  x-data="{
                                baseUrl: '<?= $this->Url->build('/') ?>',
                                lang: '<?= $this->request->getParam('lang') ?>'
                            }">
                             
                              <?= $this->element('frontend/Courses/card_alpine') ?>    
                            </template>
                        </div>
                        <!-- Pagination Controls -->
                        <div class="flex justify-start space-x-2 pagination" x-show="!isLoading && filteredData.length > 0"
                            x-show="pageCount() > 1">
                            <button class="prev bg-gray-200 hover:bg-gray-300" @click="prevPage"
                                :disabled="currentPage === 1">
                                <svg class="h-8 w-8 text-[#A3C4A9]" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round">
                                    <polyline points="15 18 9 12 15 6"></polyline>
                                </svg>
                            </button>

                            <template x-for="page in totalPages" :key="page">
                                <button class="px-3 py-1 rounded"
                                    :class="page === currentPage ? 'bg-[#A3C4A9] text-[#FFFFFF] active' : 'bg-[#FFFFFF] text-[#A3C4A9] hover:bg-[#A3C4A9] hover:text-[#ffffff]'"
                                    @click="goToPage(page)" x-text="page"></button>
                            </template>

                            <button class="next bg-gray-200 hover:bg-gray-300" @click="nextPage"
                                :disabled="currentPage === totalPages">
                                <svg class="h-8 w-8 text-[#A3C4A9]" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round">
                                    <polyline points="9 18 15 12 9 6"></polyline>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <aside class="right-filters">
                        <div x-data="{ search: '' }" class="filters-container">
                            <div class="course-filter">
                                <div class="flex items-center justify-between space-x-4 ">
                                    <span class="text-gray-700 text-[17px] font-medium mb-2">Filters</span>
                                    <template x-if="filterCount > 0">
                                        <span
                                            class="inline-flex items-center justify-center text-xs font-semibold text-white bg-default-600 rounded-full w-5 h-5"
                                            style="margin-right:70px;">
                                            <span x-text="filterCount"></span>
                                        </span>
                                    </template>
                                    <template x-if="Object.values(selectedFilters).some(f => 
                                        Array.isArray(f) 
                                            ? f.some(v => v !== 'all' && v.trim() !== '') 
                                            : f && f !== 'all' && f.trim() !== ''
                                        )">
                                        <a href="javascript:void(0)"
                                            class="text-blue-700 hover:underline font-medium filter-clear-all"
                                            @click="clearAllFilters">Clear All</a>
                                    </template>
                                </div>
                                <div class="filter-group">
                                    <input type="search" name="search" placeholder="Search" class="bg-white search"
                                        x-model="search" @input.debounce.500ms="pageNumber = 0; fetchData()" />
                                    <i class="fas fa-search" x-show="search.length === 0" x-transition></i>
                                </div>
                                <p class="title">Course Type</p>
                                <div class="yoga-types">
                                    <p class="flex items-center">
                                        <label class="custom-checkbox">
                                            <input type="checkbox" value="all"
                                            :checked="selectedTypes.length === 0 || selectedTypes.includes('all')"
                                            @change="handleFilterChange($data, 'selectedTypes', 'all', event)">  <span class="checkmark"></span>
                                            All Course Types
                                        </label>
                                    </p>
                                    <template x-if="Object.keys(filterLabels.selectedTypes).length">
                                        <template x-for="(label, id) in filterLabels.selectedTypes" :key="id">
                                                <p class="flex items-center">
                                                <label class="custom-checkbox">
                                                    <input type="checkbox"
                                                        :value="id"
                                                        x-model="selectedTypes"
                                                        @change="handleFilterChange($data, 'selectedTypes', id, $event)">
                                                        <span class="checkmark"></span>
                                                        <span x-text="label"></span>
                                                    
                                                        <span class="list-filter-count"
                                                        x-text="'(' + (filterCounts.course_types[id] || 0) + ')'"></span>
                                                </label>
                                            </p>
                                        </template>
                                    </template>
                                </div>
                            </div>
                            <!-- <div class="yoga-filter"
                                x-data="loadCourses()"
                                x-init="init()"
                                x-cloak> -->
                            <div class="yoga-filter" x-data="{
                                    styles: window.yogaStyles
                                }">
                                <p class="title">Yoga Style</p>
                                <div class="yoga-types">
                                    <p class="flex items-center">
                                        <label class="custom-checkbox">
                                        <input type="checkbox" value="all"
                                            :checked="selectedStyles.length === 0 || selectedStyles.includes('all')"
                                            @change="handleFilterChange($data, 'selectedStyles', 'all', event)">  
                                            <span class="checkmark"></span>
                                        All Yoga Styles
                                    </label>
                                    </p>

                                    <template x-for="([id, style], index) in Object.entries(styles).slice(0, showAll ? undefined : 4)" :key="id">
                                        <p class="flex items-center">
                                            <label class="custom-checkbox">
                                                <input type="checkbox" :value="id" 
                                                x-model="selectedStyles"
                                              
                                                @change="handleFilterChange($data, 'selectedStyles', id, event)">
                                                <span class="checkmark"></span>
                                                <span x-text="style"></span>
                                                <span class="list-filter-count" x-text="'(' + (filterCounts.yoga_styles[`${id}`] || 0) + ')'"></span>
                                            </label>
                                        </p>
                                    </template>
                                    <a href="javascript:void(0)" @click="showAll = !showAll" class="btn-view">
                                        <span x-text="showAll ? 'Show Less' : 'View All'"></span>
                                    </a>
                                </div>
                            </div>
                            <div class="special-filter">
                                <p class="title">Special Need</p>
                                <p class="flex items-center">
                                    <label class="custom-checkbox"><input type="checkbox" value="all"
                                    :checked="selectedNeeds.length === 0 || selectedNeeds.includes('all')"
                                    @change="handleFilterChange($data, 'selectedNeeds', 'all', event)">  <span class="checkmark"></span>
                                All Special Needs</label>
                                    </p>
                                <template x-if="Object.keys(filterLabels.selectedNeeds).length">
                                    <template x-for="(label, id) in filterLabels.selectedNeeds" :key="id">
                                            <p class="flex items-center">
                                            <label class="custom-checkbox">
                                                <input type="checkbox"
                                                    :value="id"
                                                    x-model="selectedNeeds"
                                                    @change="handleFilterChange($data, 'selectedNeeds', id, $event)">
                                                    <span class="checkmark"></span>
                                                    <span x-text="label"></span>
                                                
                                                    <span class="list-filter-count"
                                                    x-text="'(' + (filterCounts.special_needs[id] || 0) + ')'"></span>
                                            </label>
                                        </p>
                                    </template>
                                </template>
                            </div>
                            <div class="where-filter">
                                <p class="title">Where</p>
                                <div class="yoga-types">
                                    <p class="flex items-center">
                                        <label class="custom-checkbox"><input type="checkbox" name="siteWhere"
                                                value="all" x-model="selectedWhere"
                                                @change="handleFilterChange($data, 'selectedWhere', 'all', event)">
                                                <span class="checkmark"></span>
                                            Anywhere
                                        </label>
                                    </p>

                                    <p class="flex items-center">
                                        <label class="">
                                            <input class="bg-white" type="text"
                                            name="location" placeholder="Enter District, City, State..."
                                            x-model="selectedLocation"
                                            x-init="selectedLocation = '<?= h($locationUrl) ?>';  fetchData();"
                                            @change="pageNumber = 0; fetchData()"><span class="checkmark"></span>
                                        </label>
                                    </p>
                                    <template x-for="mode in modalities" :key="mode.id">
                                        <p>
                                            <label class="cursor-pointer">
                                                <input type="checkbox"
                                                    name="mode"
                                                    :value="mode.id"
                                                    x-model="selectedMode"
                                                    @change="handleFilterChange($data, 'selectedMode', mode.id, $event)">
                                                <span x-text="mode.name"></span>
                                                <span class="list-filter-count"
                                                    x-text="'(' + (filterCounts.modes[mode.id] || 0) + ')'"></span>
                                            </label>
                                        </p>
                                    </template>
                                </div>
                            </div>
                            <div class="when-filter">
                                <p class="title">When</p>
                                <div class="yoga-types">
                                    <div class="filter-date">
                                        <?php if (!empty($months)) {
                                            foreach ($months as $month) {
                                                ?>
                                                <p class="flex items-center">
                                                    <label class="custom-checkbox"><input type="checkbox" value="<?= $month ?>"
                                                            name="start_month" x-model="selectedCourseDate"
                                                            @change="handleFilterChange($data, 'selectedCourseDate', '<?= $month ?>', event)">
                                                        <?= $month ?>
                                                        <span class="checkmark"></span>
                                                        <span class="list-filter-count" x-text="'(' + (filterCounts.course_dates['<?= $month ?>'] || 0) + ')'"></span>
                                                    </label>
                                                </p>
                                            <?php }
                                        } ?>
                                    </div>
                                </div>
                            </div>
                            <div class="lang-filter">
                                <p class="title">Language</p>
                                <div class="yoga-types">
                                    <!-- <p class="flex items-center">
                                        <input type="checkbox" value="all" x-model="selectedLanguage"
                                            @change="handleFilterChange($data, 'selectedLanguage', 'all', event)"
                                            :checked="selectedLanguage.length === 0 || selectedLanguage.includes('all')">
                                        All Languages
                                    </p> -->
                                    <p class="flex items-center">
                                        <label class="custom-checkbox">
                                            <input type="checkbox" value="all" x-model="selectedLanguage"
                                                @change="handleFilterChange($data, 'selectedLanguage', 'all', event)"
                                                :checked="selectedLanguage.length === 0 || selectedLanguage.includes('all')">
                                            <span class="checkmark"></span>
                                            All Languages
                                        </label>
                                    </p>
                                    <?php if (!empty($languages)) {
                                        foreach ($languages as $language) {
                                            ?>
                                            <p class="flex items-center">
                                                <label class="custom-checkbox">
                                                    <input type="checkbox" value="<?= $language ?>" name="language"
                                                        x-model="selectedLanguage"
                                                        @change="handleFilterChange($data, 'selectedLanguage', '<?= $language ?>', event)">
                                                    <?= $language ?>
                                                    <span class="checkmark"></span>
                                                    <span class="list-filter-count" x-text=" '(' + (filterCounts.languages['<?= $language ?>'] || 0) + ')'"></span>
                                                </label>
                                            </p>
                                        <?php }
                                    } ?>
                                </div>
                            </div>
                        </div>
                        <div class="popular">
                            <p class="title">Most Popular</p>
                            <a href="#" class="btn-popular">Yoga Course in Goa</a>
                            <a href="#" class="btn-popular">Yoga TTC in Rishikesh</a>
                            <a href="#" class="btn-popular">Yoga TTC in Kerala</a>
                        </div>
                    </aside>
                </div>
                <div id="course-card-container" class="course-card-container featured-yoga flex flex-col gap-4 pt-[20px] mobile-view" x-show="isMobile">
                    <template x-for="item in mobileData" :key="item.id" x-data="{
                            baseUrl: '<?= $this->Url->build('/') ?>',
                            lang: '<?= $this->request->getParam('lang') ?>'
                        }">
                   
                        <!-- <a :href="`${baseUrl}${lang}/yoga-courses/` +
                            (item.country?.name?.toLowerCase().replace(/\s+/g, '-') || 'unknown') +
                            '/' +
                            (item.region_name?.toLowerCase().replace(/\s+/g, '-') || 'unknown-region') +
                            '/' +
                            (item.state?.name?.toLowerCase().replace(/\s+/g, '-') || '') +
                            '/' +
                            (item.city?.name?.toLowerCase().replace(/\s+/g, '-') || '') +
                            '/' +
                            (item.id + '-' + (item.slug || ''))"> -->


                             <a :href="getCourseUrl(item)">
                    
                            <div class="relative w-full featured-slider course-card bg-white snap-start">
                                <img class="h-[157px] w-100 object-cover yoga-img" :src="`${item.image_url}`"
                                    alt="Yoga Image" />
                                <!-- <img class="wish-icon" src="<?= $this->Url->webroot('img/whishlist.png') ?>" alt="Wishlist Icon" /> -->
                                <div class="card-body text-sm">
                                    <p class="info line-clamp-2" x-html='item.partner ? "@ " + item.partner.name : ""'>
                                    </p>
                                    <h3 class="text-gray-900 leading-none yoga-name line-clamp-3"
                                        x-html="item.name +`<span class='rating-wrapper'><span class='rating'>4.5</span> <i class='fas fa-star'></i></span>`">
                                    </h3>
                                    <p class="text-gray-600 yoga-description line-clamp-4"
                                        x-show="item.short_description" x-text="item.short_description"></p>
                                      <div class="time-container" x-data="{
                                            formatDate(date) {
                                                const d = new Date(item.course_batches[0].start_date);
                                                const day = String(d.getDate()).padStart(2, '0');
                                                const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are 0-based
                                                const year = d.getFullYear().toString().slice(-2);
                                                return `${day}-${month}-${year}`;
                                            }
                                        }">
                                        <p class="time"
                                            x-html="(() => {
                                                const batches = item.course_batches;
                                                const hasBatches = Array.isArray(batches) && batches.length > 0;
                                                const firstBatch = hasBatches ? batches[0] : null;

                                                let html = `<i class='fas fa-calendar-alt'></i>`;

                                                if (firstBatch && firstBatch.start_date) {
                                                    html += ' ' + formatDate(firstBatch.start_date);
                                                    if (batches.length > 1) {
                                                        html += ` <small>+${batches.length - 1} more</small>`;
                                                    }
                                                }

                                                return html;
                                            })()">
                                        </p>
                                        <p class="text-gray-600 mode" x-show="getMode(item)" x-html="getMode(item)"></p>
                                        <p class="text-gray-600 lang" x-show="item.language"
                                            x-html="`<i class='fas fa-globe'></i> `+item.language"></p>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </template>
                </div>
                <div class="text-center pb-3">
                    <button @click="nextPage()" x-show="mobileData.length > 0 && mobileData.length < total"
                        class="px-3 py-1 rounded bg-[#D87A61] text-[#FFFFFF]">
                        Load More
                    </button>
                </div>
            </div>
        </div>
    </div>
    
</section>
<?php 
$hybridIconUrl = $this->Url->webroot('img/home_work.png');
$locationIconUrl = $this->Url->webroot('img/vector.png');
?>
<script>
function bookmarkComponent_old(item) {
    return {
        isBookmarked: item.is_bookmarked === true,
        async init() {
            // Fetch if already bookmarked
            const response = await fetch(`/profile/check?type=course&id=${item.id}`);
            const data = await response.json();
            this.isBookmarked = data.bookmarked;
        },

        async toggleBookmark() {
        
            this.isBookmarked = !this.isBookmarked;
            const response = await fetch('/profile/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>',
                },
                body: JSON.stringify({
                    type: 'course',
                    ref_id: item.id,
                }),
            });

            const data = await response.json();
            if (data.redirect) {
                console.log('Redirecting to:');
                // window.location.href = data.redirect;
            } else {
              console.log('dsdsds1212212');  
            this.isBookmarked = data.status === 'added';
            this.item.is_bookmarked = this.isBookmarked ? 1 : 0;
            console.log(data.status);
            window.dispatchEvent(new CustomEvent('show-toast', { detail: { message: data.status === 'added' ? 'Bookmark added' : 'Bookmark removed', color: data.status === 'added' ? 'bg-green-500' : 'bg-red-500' } }));
                //  this.isBookmarked = data.status === 'added';
                //  this.item.is_bookmarked = this.isBookmarked ? 1 : 0;
                // this.isBookmarked = data.status === 'added';
            }
        }
    };
}



function bookmarkComponent(item) {

    return {
        isBookmarked: item.is_bookmarked === true,

        async init() {
            // Skip check if not logged in
            if (!window.isCustomerLoggedIn) return;

            try {
                const response = await fetch(`/profile/check?type=course&id=${item.id}`);
                const data = await response.json();
                this.isBookmarked = data.bookmarked;
            } catch (e) {
                console.error('Error checking bookmark:', e);
            }
        },

        async toggleBookmark() {
            // Redirect to login if not logged in
            if (!window.isCustomerLoggedIn) {
                window.location.href = '/login/customer-login';
                return;
            }

            try {
               
                const response = await fetch('/profile/toggle', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>',
                    },
                    body: JSON.stringify({
                        type: 'course',
                        ref_id: item.id,
                    }),
                });

                const data = await response.json();

                if (data.redirect) {
                    window.location.href = data.redirect;
                    return;
                }

                this.isBookmarked = data.status === 'added';
                this.item.is_bookmarked = this.isBookmarked ? 1 : 0;

                window.dispatchEvent(new CustomEvent('show-toast', {
                    detail: {
                        message: data.status === 'added' ? 'Bookmark added' : 'Bookmark removed',
                        color: data.status === 'added' ? 'bg-green-500' : 'bg-red-500'
                    }
                }));
            } catch (e) {
                console.error('Bookmark toggle error:', e);
            }
        }
    };
}

</script>

<script>

    
    window.yogaStyles = <?= json_encode($yoga_styles, JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT) ?>;
     // when getting data from URL set as array //
    var selectedTypeFromUrl = <?= json_encode($courseTypeFilter, JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT) ?>;
    window.filterLabels = <?= json_encode($filterLabels, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_AMP | JSON_HEX_QUOT) ?>;
    window.modalities = <?= !empty($modeList) ? json_encode($modeList, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_AMP | JSON_HEX_QUOT) : [] ?>;

    var hybridIconUrl = '<?= $hybridIconUrl ?>';
    var locationIconUrl = '<?= $locationIconUrl ?>';

    var siteUrl = "<?= $siteUrl ?>";
    var limit = "<?= $limit ?>";
    var totalRecords = "<?= $totalRecords ?>";
    var selectedLang = "<?= $selectedLang ?>";
    var selectedCountry = "<?= $selectedCountry ?>";
    var region = "<?= $region ?>";
    var selectedCourseTypes = [];

    if (selectedTypeFromUrl) {
        if (Array.isArray(selectedTypeFromUrl)) {
            selectedCourseTypes = selectedTypeFromUrl;
        } else if (typeof selectedTypeFromUrl === 'string') {
            selectedCourseTypes = selectedTypeFromUrl.split(',').map(s => s.trim()).filter(Boolean);
        }
    }

    var specialNeedFilterUrl = <?= json_encode($specialNeedFilter, JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT) ?>;
    var selectedSpecialNeeds = [];

    if (specialNeedFilterUrl) {
        if (Array.isArray(specialNeedFilterUrl)) {
            selectedSpecialNeeds = specialNeedFilterUrl;
        } else if (typeof specialNeedFilterUrl === 'string') {
            selectedSpecialNeeds = specialNeedFilterUrl.split(',').map(s => s.trim()).filter(Boolean);
        }
    }

    var styleFilter = <?= json_encode($styleFilter, JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT) ?>;
    var selectedStyle = [];

    if (styleFilter) {
        if (Array.isArray(styleFilter)) {
            selectedStyle = styleFilter;
        } else if (typeof styleFilter === 'string') {
            selectedStyle = styleFilter.split(',').map(s => s.trim()).filter(Boolean);
        }
    }
</script>