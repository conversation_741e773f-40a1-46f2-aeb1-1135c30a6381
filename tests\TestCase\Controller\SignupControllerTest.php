<?php
declare(strict_types=1);

namespace App\Test\TestCase\Controller;

use Cake\TestSuite\IntegrationTestTrait;
use Cake\TestSuite\TestCase;

/**
 * App\Controller\SignupController Test Case
 *
 * @uses \App\Controller\SignupController
 */
class SignupControllerTest extends TestCase
{
    use IntegrationTestTrait;

    /**
     * Fixtures
     *
     * @var array<string>
     */
    protected $fixtures = [
        'app.Users',
        'app.Customers',
        'app.Roles',
    ];

    /**
     * Test index method
     *
     * @return void
     * @uses \App\Controller\SignupController::index()
     */
    public function testIndex()
    {
        $this->get('/signup');
        $this->assertResponseOk();
        $this->assertResponseContains('Create an account');
    }

    /**
     * Test sendOtp method with email
     *
     * @return void
     * @uses \App\Controller\SignupController::sendOtp()
     */
    public function testSendOtpWithEmail()
    {
        $this->configRequest([
            'headers' => [
                'X-Requested-With' => 'XMLHttpRequest',
                'Accept' => 'application/json'
            ]
        ]);

        $data = [
            'email' => '<EMAIL>'
        ];

        $this->post('/signup/send-otp', $data);
        $this->assertResponseOk();
        
        $response = json_decode((string)$this->_response->getBody(), true);
        $this->assertTrue($response['success']);
        $this->assertStringContainsString('OTP has been sent', $response['message']);
        
        // Check that session contains OTP data
        $session = $this->_request->getSession();
        $emailOtpData = $session->read('email_id');
        $this->assertNotNull($emailOtpData);
        $this->assertEquals('<EMAIL>', $emailOtpData['email_id']);
        $this->assertArrayHasKey('otp', $emailOtpData);
        $this->assertArrayHasKey('expires', $emailOtpData);
    }

    /**
     * Test sendOtp method with mobile
     *
     * @return void
     * @uses \App\Controller\SignupController::sendOtp()
     */
    public function testSendOtpWithMobile()
    {
        $this->configRequest([
            'headers' => [
                'X-Requested-With' => 'XMLHttpRequest',
                'Accept' => 'application/json'
            ]
        ]);

        $data = [
            'email' => '9876543210'
        ];

        $this->post('/signup/send-otp', $data);
        $this->assertResponseOk();
        
        $response = json_decode((string)$this->_response->getBody(), true);
        // Note: This might fail if 2Factor API is not configured properly
        // In that case, we should mock the TwoFactorService
    }

    /**
     * Test verifyOtp method with valid email OTP
     *
     * @return void
     * @uses \App\Controller\SignupController::verifyOtp()
     */
    public function testVerifyOtpWithValidEmailOtp()
    {
        // First, set up session with OTP data
        $session = $this->_request->getSession();
        $session->write('email_id', [
            'email_id' => '<EMAIL>',
            'otp' => '1234',
            'expires' => time() + 300
        ]);

        $this->configRequest([
            'headers' => [
                'X-Requested-With' => 'XMLHttpRequest',
                'Accept' => 'application/json'
            ]
        ]);

        $data = [
            'first_name' => 'Test',
            'email' => '<EMAIL>',
            'otp' => '1234'
        ];

        $this->post('/signup/verify-otp', $data);
        $this->assertResponseOk();
        
        $response = json_decode((string)$this->_response->getBody(), true);
        $this->assertTrue($response['success']);
        $this->assertStringContainsString('created successfully', $response['message']);
    }

    /**
     * Test verifyOtp method with invalid OTP
     *
     * @return void
     * @uses \App\Controller\SignupController::verifyOtp()
     */
    public function testVerifyOtpWithInvalidOtp()
    {
        // First, set up session with OTP data
        $session = $this->_request->getSession();
        $session->write('email_id', [
            'email_id' => '<EMAIL>',
            'otp' => '1234',
            'expires' => time() + 300
        ]);

        $this->configRequest([
            'headers' => [
                'X-Requested-With' => 'XMLHttpRequest',
                'Accept' => 'application/json'
            ]
        ]);

        $data = [
            'first_name' => 'Test',
            'email' => '<EMAIL>',
            'otp' => '5678' // Wrong OTP
        ];

        $this->post('/signup/verify-otp', $data);
        $this->assertResponseOk();
        
        $response = json_decode((string)$this->_response->getBody(), true);
        $this->assertFalse($response['success']);
        $this->assertStringContainsString('Invalid OTP', $response['message']);
    }

    /**
     * Test verifyOtp method with expired OTP
     *
     * @return void
     * @uses \App\Controller\SignupController::verifyOtp()
     */
    public function testVerifyOtpWithExpiredOtp()
    {
        // First, set up session with expired OTP data
        $session = $this->_request->getSession();
        $session->write('email_id', [
            'email_id' => '<EMAIL>',
            'otp' => '1234',
            'expires' => time() - 100 // Expired
        ]);

        $this->configRequest([
            'headers' => [
                'X-Requested-With' => 'XMLHttpRequest',
                'Accept' => 'application/json'
            ]
        ]);

        $data = [
            'first_name' => 'Test',
            'email' => '<EMAIL>',
            'otp' => '1234'
        ];

        $this->post('/signup/verify-otp', $data);
        $this->assertResponseOk();
        
        $response = json_decode((string)$this->_response->getBody(), true);
        $this->assertFalse($response['success']);
        $this->assertStringContainsString('expired', $response['message']);
    }

    /**
     * Test verifyOtp method with no session
     *
     * @return void
     * @uses \App\Controller\SignupController::verifyOtp()
     */
    public function testVerifyOtpWithNoSession()
    {
        $this->configRequest([
            'headers' => [
                'X-Requested-With' => 'XMLHttpRequest',
                'Accept' => 'application/json'
            ]
        ]);

        $data = [
            'first_name' => 'Test',
            'email' => '<EMAIL>',
            'otp' => '1234'
        ];

        $this->post('/signup/verify-otp', $data);
        $this->assertResponseOk();
        
        $response = json_decode((string)$this->_response->getBody(), true);
        $this->assertFalse($response['success']);
        $this->assertStringContainsString('No OTP session found', $response['message']);
    }
}
