
function loadCourses() {
    return {
    search: "",
    selectedTypes: selectedCourseTypes || [],
    selectedStyles: selectedStyle || [],
    selectedLocation: "",
    selectedCourseDate: [],
    selectedLanguage: [],
    selectedWhere: ['all'],
    selectedMode: [],
    total: totalRecords,
    myForData: [],
    isLoading: false,
    selectedSort: "",
    filteredData: [],
    mobileData: [],
    styles: window.yogaStyles || [],
    selectedNeeds: selectedSpecialNeeds || [],
    showAll: false,
    pageNumber: 0,
    currentPage: 1,
    pageSize: limit,
    isMobile: window.innerWidth <= 768,
    region: region,
    filterLabels: window.filterLabels,
    filterCounts: {
        course_types: {},
        yoga_styles: {},
        languages: {},
        modes: {},
        special_needs: {},
        site_types: {},
        course_dates: {}
    },
     modalities: Object.entries(window.modalities || {}).map(([id, name]) => ({ id, name })),

    exposeForExternal() {
        window.getfilteredData = () => this.filteredData;
    },

    get selectedFilters() {
        return {
            selectedTypes: this.selectedTypes,
            selectedStyles: this.selectedStyles,
            selectedLocation: this.selectedLocation ? [this.selectedLocation] : [],
            selectedCourseDate: this.selectedCourseDate,
            selectedLanguage: this.selectedLanguage,
            selectedWhere: this.selectedWhere,
            selectedMode: this.selectedMode,
            selectedNeeds: this.selectedNeeds
        };
    },

    // Return readable labels for selected filters
    get selectedFiltersLabels() {
        const result = {};
        for (const key in this.selectedFilters) {
            const selected = this.selectedFilters[key];
            const labelMap = this.filterLabels[key] || {};
            result[key] = selected.map(slug => labelMap[slug] || slug);
        }
        return result;
    },

    get filterCount() {
        let count = 0;
        const filters = this.selectedFilters;

        for (const key in filters) {
            const value = filters[key];
            if (Array.isArray(value)) {
                const filtered = value.filter(v => v !== 'all' && v.trim() !== '');
                count += filtered.length;
            } else if (value && value !== 'all' && value.trim() !== '') {
                count += 1;
            }
        }

        return count;
    },

    getMode(item) {
        if (!item.modalities || !Array.isArray(item.modalities)) {
            return `<i class='fas fa-laptop-code'></i>`; // Default icon
        }

        const names = item.modalities.map(m => m.name);
        const modeNames = [];

        // Online types
        if (names.includes("Online Live")) modeNames.push("Online - Live");
        if (names.includes("Online VOD")) modeNames.push("Online - VOD");
        if (names.includes("Hybrid")) modeNames.push("Hybrid");

        // On-site type
        const isOnSite = names.includes("On Site");
        const location = item.city?.name ?? '';
        
        if (isOnSite) modeNames.push(location || "On-Site");

        const totalModes = modeNames.length;

        // Decide the icon:
        let icon = "<i class='fas fa-laptop-code'></i>"; // default
        if (totalModes === 1 && isOnSite) {
            icon = "<i class='fas fa-map-marker-alt'></i>";
        }

        if(names.includes("Hybrid") || totalModes >  1){
            icon = '<img src="'+hybridIconUrl+'"></img>';
        }
        // Always show 1 icon, then list modes if any
        if (totalModes > 0) {
            return `${icon} ${modeNames.join(' | ')}`;
        }

        // If no recognizable modes, still return icon
        return icon;
    },

    /**
     * Generate course URL based on modalities
     */
    getCourseUrl(item) {
        const baseUrl = this.baseUrl || '';
        const lang = this.lang || 'en';

        if (!item.modalities || !Array.isArray(item.modalities)) {
            // No modalities, use location-based URL
            return this.getLocationBasedUrl(item, baseUrl, lang);
        }

        const modalityNames = item.modalities.map(m => m.name);
        const hasOnlineVOD = modalityNames.includes('Online VOD');
        const hasOnlineLive = modalityNames.includes('Online Live');
        const hasOnSite = modalityNames.includes('On Site');
        const hasHybrid = modalityNames.includes('Hybrid');

        // Determine URL pattern based on modalities
        if (hasOnlineVOD && !hasOnlineLive && !hasOnSite && !hasHybrid) {
            // Only Online-VOD
            return `${baseUrl}${lang}/yoga-courses/video/${item.slug}`;
        } else if (hasOnlineLive && !hasOnlineVOD && !hasOnSite && !hasHybrid) {
            // Only Online-Live
            return `${baseUrl}${lang}/yoga-courses/online/${item.slug}`;
        } else {
            // On-site, hybrid, or multiple modalities - use location-based URL
          
            return this.getLocationBasedUrl(item, baseUrl, lang);
        }
    },

    /**
     * Generate location-based URL for courses
     */
    getLocationBasedUrl(item, baseUrl, lang) {
        const parts = [
            item.country?.name,
            item.region_name,
            item.state?.name,
            item.city?.name,
            item.locality?.name,
            item.slug
        ].filter(Boolean).map(part => part.toLowerCase().replace(/\s+/g, '-'));

        return `${baseUrl}${lang}/yoga-courses/${parts.join('/')}`;
    },

    // Add a filter
    addFilter(category, value) {
        if (!this[category].includes(value)) {
            this[category].push(value);
            this.fetchData();
        }
    },

    // Remove filter
    removeFilter(category, index) {
        const currentValue = this[category];
        if (Array.isArray(currentValue)) {
            this[category].splice(index, 1);
        } else if (typeof currentValue === 'string') {
            this[category] = '';
        } else {
            // Fallback for unsupported types
            console.warn(`Unexpected type for ${category}:`, typeof currentValue);
        }

        this.fetchData();
    },

    // Clean up filters and remove "all" if other selections are made
    cleanFilter(category) {
        return this[category].includes("all") && this[category].length > 1
            ? this[category].filter(t => t !== "all")
            : this[category];
    },

    getFilterLabel(category, filter) {
        if (category === 'selectedMode') {
            if (filter === 'live') return 'Online - Live';
            if (filter === 'vod') return 'Online - Video on Demand';
        }
        return filter;
    },

    handleFilterChange(data, category, value, event) {
        const isChecked = event.target.checked;
  
        if (value === 'all') {
            
            if (!isChecked && data[category].length === 0) {
                data[category] = ['all'];
                return
            }
             if (!isChecked && data[category].length === 1 && data[category].includes('all')) {
                // Prevent unchecking "All" when it's the only one selected
                data[category] = ['all'];
            }

            if (isChecked || data[category].length === 0) {
                // "All" checked => deselect others
                data[category] = ['all'];
            } 
        } else {

            if (isChecked) {
                // Add the value if not already present
                if (!data[category].includes(value)) {
                    data[category].push(value);
                }
            
                // Remove "all" if something else is selected
                data[category] = data[category].filter(item => item !== 'all');
            
            } else {
                // Checkbox was unchecked — remove the value
                data[category] = data[category].filter(item => item !== value);
            
                // If nothing is selected, fallback to "all"
                if (data[category].length === 0) {
                    data[category] = ['all'];
                }
            }
        }
        
        data.pageNumber = 0;
        if (!data.isMobile && typeof data.fetchData === 'function') {
            data.fetchData();
        }
    },

    applyMobileFilters() {
        this.pageNumber = 0;
        this.fetchData();
        if (this.isMobile) {
            $('.filters').hide('slow');
        }
    },

    clearAllFilters() {
        this.search = "",
        this.selectedTypes = [];
        this.selectedStyles = [];
        this.selectedLocation = '';
        this.selectedCourseDate = [];
        this.selectedLanguage = ['all'];
        this.selectedWhere = ['all'];
        this.selectedMode = [];
        this.selectedNeeds = [];

        this.fetchData(); // call your data-fetching logic
    },

    async fetchData(append = false) {
        this.isLoading = true;
        // Remove "all" from selected types if others are selected
        const types = this.cleanFilter("selectedTypes");
        const styles = this.cleanFilter("selectedStyles");
        const location = this.selectedLocation?.trim() || ""; // Default empty string if undefined
        const courseDate = this.cleanFilter("selectedCourseDate");
        const language = this.cleanFilter("selectedLanguage");
        const site = this.cleanFilter("selectedWhere");
        const mode = this.cleanFilter("selectedMode");
        const specialNeeds = this.cleanFilter("selectedNeeds");
        
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search: this.search,
                type: types.join(','),
                style: styles.join(','),
                location: location,
                courseDate: courseDate.join(','),
                language: language.join(','),
                site: site.join(','),
                mode: mode.join(','),
                sort: this.selectedSort || '' ,
                specialNeeds: specialNeeds.join(','),
                region: this.region
            });
    
            const response = await fetch(siteUrl+selectedLang+`/yoga-courses/`+selectedCountry+`?${params.toString()}`,{
                headers: {
                  'Accept': 'application/json'
                }
            });

            const data = await response.json();
            this.total = data.totalRecords;
            this.filterCounts = data.filterCounts;
          
            if (!this.isMobile) {
                // Desktop: overwrite full data
                this.myForData = data.listData;
            } else {
                // Mobile: append or reset
                if (append) {
                    this.mobileData = [...this.mobileData, ...data.listData];
                } else {
                    this.mobileData = data.listData || [];
                }
              
            }

        } catch (error) {
            console.error("Fetch error:", error);
        } finally {
            this.isLoading = false; 
        }
    },

    get filteredData() {
        return this.myForData;
    },

    //Create array of all pages (for loop to display page numbers)
    get totalPages() {
        return Math.ceil(this.total / this.pageSize);
    },

    get paginatedData() {
        const start = (this.currentPage - 1) * this.pageSize;
        return this.myForData.slice(start, start + this.pageSize);
    },

    goToPage(page) {
        if (page >= 1 && page <= this.totalPages) {
            this.currentPage = page;
            this.fetchData();
        }
    },

    nextPage() {
        if (this.currentPage < this.totalPages) {
            this.currentPage++;
            if (this.isMobile) {
                this.fetchData(true); // Append on mobile only
            } else {
                this.fetchData(); // For desktop pagination
            }
        }
    },

    prevPage() {
        if (this.currentPage > 1) {
        this.currentPage--;
        this.fetchData();
        }
    },

    //Return the start range of the paginated results
    startResults() {
        return this.pageNumber * this.pageSize + 1;
    },

    //Return the end range of the paginated results
    endResults() {
        let resultsOnPage = (this.pageNumber + 1) * this.pageSize;

        if (resultsOnPage <= this.total) {
        return resultsOnPage;
        }

        return this.total;
    },

    //Link to navigate to page
    viewPage(index) {
        this.pageNumber = index;
        this.fetchData();
    },

    init() {
        if (this.initialized) return; // skip if already initialized

        this.initialized = true;
        const params = new URLSearchParams(window.location.search);
        // Apply params to Alpine model
        if (params.has('location')) {
            this.location = params.get('location');
            this.selectedLocation = params.get('location'); // assuming multi-select
        }

        if (params.has('type')) {
            this.types = params.get('type');
            this.selectedTypes = [params.get('type')];
        }

        if (params.has('style')) {
            this.styles = params.get('style');
            this.selectedStyles = [params.get('style')];
        }

        if (params.has('specialNeeds')) {
            this.selectedNeeds = [params.get('specialNeeds')];
        }
        
        if (params.has('language')) {
            this.selectedLanguage = [params.get('language')];
        }
         else {
            // Default to 'all' if nothing provided
            this.selectedLanguage = ['all'];
        }

        window.addEventListener('resize', () => {
            this.isMobile = window.innerWidth <= 768;
        });
        // this.fetchData();
    },
    };
}