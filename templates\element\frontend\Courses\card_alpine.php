

    <div class="course-card bg-white rounded-[10px] rounded-lg card-container min-h-[450px] h-[600px]">
  <div x-data="bookmarkComponent({...item})" x-init="init()">
    <img
      class="wish-icon cursor-pointer"
      :src="item.is_bookmarked == 1 || item.is_bookmarked === true ? '<?= $this->Url->webroot('img/whishlist-selected.png') ?>' : '<?= $this->Url->webroot('img/whishlist.png') ?>'"
      alt="Wishlist Icon"
      @click.stop="toggleBookmark()"
    />
  </div>

  <a :href="getCourseUrl(item)" class="block h-full">
    <img class="h-[157px] w-100 object-cover yoga-img" :src="`${item.image_url}`" alt="Yoga Image" />
    <!-- <p x-text="'is_bookmarked: ' + item.is_bookmarked"></p> -->
    <div class="text-sm card-body">
      <p class="info line-clamp-2" x-html='item.partner ? "@ " + item.partner.name : ""'></p>

      <h3 class="text-gray-900 leading-none yoga-name"
          x-html="`<span class='line-clamp-2'>${item.name}</span> <span class='rating-wrapper'><span class='rating'>4.5</span> <i class='fas fa-star'></i></span>`">
      </h3>

      <p class="text-gray-600 yoga-description line-clamp-4"
          x-show="item.short_description" x-text="item.short_description"></p>

      <div class="time-container" x-data="{
        formatDate(date) {
            const d = new Date(date);
            const day = String(d.getDate()).padStart(2, '0');
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const year = d.getFullYear().toString().slice(-2);
            return `${day}-${month}-${year}`;
        }
      }">

        <p class="time"
           x-html="(() => {
               const batches = item.course_batches;
               const hasBatches = Array.isArray(batches) && batches.length > 0;
               const firstBatch = hasBatches ? batches[0] : null;
               let html = `<i class='fas fa-calendar-alt'></i>`;
               if (firstBatch && firstBatch.start_date) {
                   html += ' ' + formatDate(firstBatch.start_date);
                   if (batches.length > 1) {
                       html += ` <small>+${batches.length - 1} more</small>`;
                   }
               }
               return html;
           })()">
        </p>

        <p class="text-gray-600 mode" x-show="getMode(item)" x-html="getMode(item)"></p>
        <p class="text-gray-600 lang" x-show="item.language"
           x-html="`<i class='fas fa-globe'></i> `+item.language"></p>
      </div>
    </div>
  </a>
</div>