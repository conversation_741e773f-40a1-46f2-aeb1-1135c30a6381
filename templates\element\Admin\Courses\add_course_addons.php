<div x-data="addonForm(<?= !empty($addonsJson) ? h($addonsJson) : '' ?>)" x-init="init()" class="alpine-section mb-4">
    <h5 class="mb-3 pt-4">Add-ons</h5>

    <div class="accordion" id="addonAccordion">
        <template x-for="(addon, index) in addons" :key="index">
            <div class="accordion-item border-secondary mb-3" :data-index="index">
                <h2 class="accordion-header" :id="`addonHeading_${index}`">
                    <button class="accordion-button" type="button"
                            :data-bs-toggle="'collapse'"
                            :data-bs-target="`#addonCollapse_${index}`"
                            :aria-expanded="index === 0 ? 'true' : 'false'"
                            :aria-controls="`addonCollapse_${index}`">
                        Add-on <span x-text="index + 1"></span>
                    </button>
                </h2>
                <div :id="`addonCollapse_${index}`" class="accordion-collapse collapse show" :aria-labelledby="`addonHeading_${index}`" data-bs-parent="#addonAccordion">
                    <div class="accordion-body">

                        <!-- Top Right Remove -->
                        <div class="d-flex justify-content-end mb-2">
                            <button type="button" class="btn btn-outline-danger btn-sm" @click="removeAddon(index)">
                                🗑 Remove
                            </button>
                        </div>

                        <div class="row g-3">
                            <!-- Addon Type -->
                           <div class="form-group row">
                                <!-- <label class="col-sm-2 col-form-label">Addon Type</label>
                                <div class="col-sm-6 ">
                                    <select class="form-select"
                                            x-model="addon.type"
                                            @change="loadAddonNames(addon)"
                                            :class="{'is-invalid': showErrors && !addon.type}">
                                        <option value="">Select Type</option>
                                        <?php foreach ($addonTypes as $type): ?>
                                            <option value="<?= h($type) ?>"><?= ucfirst(h($type)) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback" x-show="showErrors && !addon.type">Type is required.</div>
                                </div>
                            </div> -->

                            <!-- Addon Name (from DB) -->
                            <!-- <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Name (optional if custom)</label>
                                <div class="col-sm-6 ">
                                    <select class="form-select"
                                            x-model="addon.master_data_id"
                                            :disabled="!(addon.names && addon.names.length)">
                                        <option value="">Select Addon</option>
                                        <template x-for="opt in addon.names" :key="opt.id">
                                               <option :value="Number(opt.id)" x-text="opt.name" :selected="Number(opt.id) === Number(addon.master_data_id)"></option>
                                        </template>
                                    </select>
                                     <div class="invalid-feedback" x-show="showErrors && !addon.master_data_id">Name is required.</div>
                                </div>
                            </div> -->

                             <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Name</label>
                                <div class="col-sm-6 ">
                                    <select class="form-select"
                                            x-model="addon.master_data_id"
                                            :class="{'is-invalid': showErrors && !addon.name}">
                                        <option value="">Select</option>
                                        <?php foreach ($addonTypes as $id => $name): ?>
                                            <option value="<?= $id ?>"><?= ucfirst(h($name)) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback" x-show="showErrors && !addon.name">Name is required.</div>
                                </div>
                            </div>
                            

                            <!-- Custom name/category -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Custom Name (if not selected above)</label>
                                <div class="col-sm-6 ">
                                    <input type="text" class="form-control"
                                        x-model="addon.custom_name"
                                        :class="{'is-invalid': showErrors && !addon.custom_name && !addon.custom_name}">
                                    <div class="invalid-feedback" x-show="showErrors && !addon.custom_name && !addon.custom_name">
                                        Either select addon or provide custom name.
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Custom Category</label>
                                <div class="col-sm-6 ">
                                    <input type="text" class="form-control" x-model="addon.custom_category">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Total Count</label>
                                <div class="col-sm-6 ">
                                    <input type="number" min="1" class="form-control"
                                        x-model="addon.total_slots"
                                        :class="{'is-invalid': showErrors && !addon.total_slots && !addon.total_slots}">
                                    <div class="invalid-feedback" x-show="showErrors && !addon.total_slots && !addon.total_slots">
                                    </div>
                                </div>
                            </div>
                             <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Sort Order</label>
                                <div class="col-sm-6 ">
                                    <input type="number" min="1" class="form-control"
                                        x-model="addon.sort_order"
                                        :class="{'is-invalid': showErrors && !addon.sort_order && !addon.sort_order}">
                                    <div class="invalid-feedback" x-show="showErrors && !addon.sort_order && !addon.sort_order">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Status</label>
                                <div class="col-sm-6 ">
                                    <select class="form-select"
                                            x-model="addon.status">
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                     <div class="invalid-feedback" x-show="showErrors && !addon.status">Status is required.</div>
                                </div>
                            </div>

                            <!-- Pricing (multiple) -->
                           <div class="col-12 mt-3">
                                <label class="form-label">Pricing</label>

                                <template x-for="(price, pIndex) in addon.prices" :key="pIndex">
                                    <div class="row g-2 align-items-end mb-4 border p-2 rounded bg-light">
                                        <!-- Currency -->
                                        <div class="col-md-2">
                                            <select class="form-select form-select-sm" x-model="price.currency" :class="{'is-invalid': showErrors && !price.currency}">
                                                <option value=""><?= __('Select') ?></option>
                                                <?php if (!empty($currencies)): ?>
                                                    <?php foreach ($currencies as $key => $currency): ?>
                                                        <option value="<?= h($key) ?>"><?= h($currency) ?></option>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </select>
                                            <div class="invalid-feedback" x-show="showErrors && !price.currency">Currency required</div>
                                        </div>

                                        <!-- Amount -->
                                        <div class="col-md-2">
                                            <input type="number" step="0.01" class="form-control form-control-sm" x-model="price.amount" placeholder="Amount"
                                                :class="{'is-invalid': showErrors && !price.amount}">
                                            <div class="invalid-feedback" x-show="showErrors && !price.amount">Amount required</div>
                                        </div>

                                        <!-- Max Count -->
                                        <!-- <div class="col-md-2">
                                            <input type="number" step="1" class="form-control form-control-sm" x-model="price.total_slots" placeholder="Max Count"
                                                :class="{'is-invalid': showErrors && !price.total_slots}">
                                            <div class="invalid-feedback" x-show="showErrors && !price.total_slots">Max Count required</div>
                                        </div> -->

                                        <!-- Sort Order -->
                                        <!-- <div class="col-md-2">
                                            <input type="number" step="1" class="form-control form-control-sm" x-model="price.sort_order" placeholder="Sort Order"
                                                :class="{'is-invalid': showErrors && !price.sort_order}">
                                            <div class="invalid-feedback" x-show="showErrors && !price.sort_order">Sort Order required</div>
                                        </div> -->

                                        <!-- Status -->
                                        <div class="col-md-2">
                                            <select class="form-select form-select-sm" x-model="price.status" :class="{'is-invalid': showErrors && !price.status}">
                                                <option value="active">Active</option>
                                                <option value="inactive">Inactive</option>
                                            </select>
                                            <div class="invalid-feedback" x-show="showErrors && !price.status">Status required</div>
                                        </div>

                                        <!-- Remove Button -->
                                        <div class="col-md-1 text-end">
                                            <button type="button" class="btn btn-sm btn-danger" @click="addon.prices.splice(pIndex, 1)">×</button>
                                        </div>
                                    </div>
                                </template>

                                <button type="button" class="btn btn-outline-secondary btn-sm mt-2" @click="addon.prices.push({currency: '', amount: '', total_slots: '', sort_order: '', status: ''})">+ Add Price</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>

    <button type="button" class="btn btn-outline-primary mt-3" @click="addAddon()">+ Add Addon</button>

    <!-- Hidden input for CakePHP -->
    <textarea name="course_addons_json" class="d-none" x-text="JSON.stringify(addons)"></textarea>
<!-- 
    <div class="mt-3">
        <button type="button" class="btn btn-success" @click="validate()">Validate Addons</button>
    </div> -->
</div>
<script>
const baseUrl = "<?= $this->Url->build(['controller' => 'Courses', 'action' => 'masterDataByType']) ?>";

function addonForm(initialAddons = []) {
    return {
        showErrors: false,
        addons: [],
        // addons: initialAddons.length ? initialAddons : [{
        //     type: '',
        //     master_data_id: '',
        //     names: [],
        //     custom_name: '',
        //     custom_category: '',
        //     total_slots: '',
        //     sort_order: '',
        //     prices: [{currency: '', amount: '', status: ''}]
        // }],
         async init() {
            if (initialAddons.length) {
                // Populate addons and preload names if type exists
                for (let addon of initialAddons) {
                    addon.names = [];
                    if (addon.type) {
                        const res = await fetch(`${baseUrl}/${addon.type}`);
                        addon.names = await res.json();
                    }
                }
                this.addons = initialAddons;
            } else {
                this.addons = [{
                    type: '',
                    master_data_id: '',
                    names: [],
                    custom_name: '',
                    custom_category: '',
                    total_slots: '',
                    sort_order: '',
                    prices: [{currency: '', amount: '', status: ''}]
                }];
            }
        },
        addAddon() {
            this.addons.push({
                type: '',
                master_data_id: '',
                names: [],
                custom_name: '',
                custom_category: '',
                total_slots: '',
                sort_order: '',
                prices: [{currency: '', amount: '', status: ''}]
            });
        },
        removeAddon(index) {
            this.addons.splice(index, 1);
        },
        loadAddonNames(addon) {
            addon.master_data_id = '';
            addon.names = [];

            if (!addon.type) return;

            fetch(`${baseUrl}/${addon.type}`)
                .then(res => res.json())
                .then(data => addon.names = data);
        },
        // validate() {
        //     this.showErrors = true;
        //     let valid = this.addons.every(addon => {
        //         let hasType = !!addon.type;
        //         let hasName = !!addon.master_data_id || !!addon.custom_name;
        //         let validPrices = addon.prices.length > 0 && addon.prices.every(p => p.currency && p.amount > 0);
        //         return hasType && hasName && validPrices;
        //     });
        //     return valid;
            // if (valid) {
            //     alert("All addons valid!");
            // } else {
            //     alert("Please fix errors.");
            // }
        // },
        get jsonAddons() {
            return JSON.stringify(this.addons);
        }
    }
}
</script>

