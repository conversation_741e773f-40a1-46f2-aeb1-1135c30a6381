<?php

/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Course> $partners
 */
?>
<?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')) ?>
<style>
    th.no-sort::after,
    th.no-sort::before {
        display: none !important;
    }

    /* Optional: prevent pointer cursor & sort highlight on hover */
    th.no-sort {
        cursor: default !important;
        background-image: none !important;
    }

    td a {
        color: black;
    }
</style>
<?php $this->append('style'); ?>
<?php $this->end(); ?>
<div class="main-content">
    <section class="section">
        <div class="section-body1">
            <div class="container-fluid">
                <?php
                $successMessage = $this->Flash->render('partner_success');
                $errorMessage = $this->Flash->render('partner_error');
                ?>
                <?php if (!empty($successMessage)): ?>
                    <?= $successMessage ?>
                <?php elseif (!empty($errorMessage)): ?>
                    <?= $errorMessage ?>
                <?php endif; ?>
            </div>
        </div>
        <div class="section-body" id="list">
            <div class="container-fluid">
                <div class="card card-primary">
                    <ul class="breadcrumb breadcrumb-style ">
                        <li class="breadcrumb-item">Partners</li>
                        <li class="breadcrumb-item">Partners List</li>
                    </ul>
                    <div class="card-header">
                        <div class="d-block d-sm-flex actions">
                            <div class="action-header">
                                <h4>Partners List</h4>
                            </div>
                            <div class="filter-options">
                                <!-- <div class="search-content">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="customSearchBox" placeholder="Search">
                                        <button class="btn btn-outline-secondary" type="button" id="customSearchBtn">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div> -->
                                <div class="action-button">
                                    <button onclick="window.location.href='<?= $this->Url->build(['controller' => 'Partners', 'action' => 'add']) ?>'">
                                        <i class="fas fa-plus"></i> <?= __("Add Partner") ?>
                                    </button>
                                </div>
                                <div class="filter-button">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-primary filter-toggle">
                                            <i class="fas fa-filter"></i> Filter
                                        </button>
                                    </div>
                                </div>
                                <div class="download-icon">
                                    <button class="btn btn-primary" id="downloadPartners"><i class="fas fa-file-download"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='filter-action row'>
                        <div class='col-12 col-sm-3'>
                            <select id='filterStatus' class='form-control form-select'>
                                <option value=""><?= __("Filter By Status") ?></option>
                                <option value="A"><?= __("Active") ?></option>
                                <option value="I"><?= __("Inactive") ?></option>
                                <option value="D"><?= __("Deleted") ?></option>
                            </select>
                        </div>
                        <div class='col-12 col-sm-2 d-flex'>
                            <button type='submit' id='filter' class='btn btn-primary me-2'>
                                <i class='fas fa-filter'></i>
                            </button>
                            <button type="reset" class="btn btn-primary" onclick="resetFilters()">
                                <i class="fas fa-redo-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped dataTable no-footer display nowrap table-hover border" id="partner-table">
                                <thead>
                                    <tr>
                                        <th class="no-sort"><?= __("Actions") ?></th>
                                        <th><?= __("Id") ?></th>
                                        <th><?= __("Partner Name") ?></th>
                                        <th><?= __("Partner Type") ?></th>
                                        <!-- <th><?= __("Name") ?></th> -->
                                        <th><?= __("Email") ?></th>
                                        <th><?= __("Phone") ?></th>
                                        <th><?= __("Address") ?></th>
                                        <th id="status"><?= __("Status") ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $i = 1; ?>
                                    <?php foreach ($partners as $partner): ?>
                                        <tr>
                                            <td>
                                                <a href="<?= $this->Url->build(['controller' => 'Partners', 'action' => 'view', $partner->id]) ?>"
                                                    class="" data-toggle="tooltip" title="<?= __("View") ?>"><i
                                                        class="far fa-eye m-r-10"></i></a>
                                                <a href="<?= $this->Url->build(['controller' => 'Partners', 'action' => 'edit', $partner->id]) ?>"
                                                    class="" data-toggle="tooltip" title="<?= __("Edit") ?>"><i
                                                        class="fas fa-edit m-r-10"></i></a>
                                                <a href="javascript:void(0);" class="delete-button" data-id="<?= $partner->id ?>" data-toggle="tooltip" title="Delete"
                                                    data-bs-toggle="modal" data-bs-target="#exampleModalCenter"> <i class="far fa-trash-alt"></i>
                                                </a>
                                                <?= $this->Html->link(
                                                    '<i class="fas fa-check"></i>',
                                                    ['controller' => 'Partners', 'action' => 'approve', $partner->id],
                                                    [
                                                        'class' => ' btn-sm approve approve ms-2',
                                                        'title' => $partner->status === 'A' ? 'Mark as Inactive' : 'Mark as Active',
                                                        'escape' => false,
                                                        'data-toggle' => 'tooltip'
                                                    ]
                                                ) ?>
                                            </td>
                                            <td><?= $partner->id ?></td>
                                            <td data-bs-toggle="tooltip" data-bs-placement="top" title="<?= h($partner->name) ?>">
                                                <?= !empty($partner->name) ? h(strlen($partner->name) > 40 ? substr($partner->name, 0, 40) . '...' : $partner->name) : 'None' ?>
                                            </td>
                                            <!-- <td><?= !empty($partner->user->first_name) ? h($partner->user->first_name) . ' ' . h($partner->user->last_name) : 'None' ?></td> -->
                                            <td><?= !empty($partner->partner_type->name) ? h($partner->partner_type->name) : 'None' ?></td>
                                            <td><?= !empty($partner->email) ? h($partner->email) : 'None' ?></td>
                                            <td><?= !empty($partner->phone) ? '+' . h($partner->country_code) . ' ' . h($partner->phone) : 'None' ?></td>
                                            <td data-bs-toggle="tooltip" data-bs-placement="top" title="<?= h($partner->address) ?>">
                                                <?= !empty($partner->address) ? h(strlen($partner->address) > 40 ? substr($partner->address, 0, 40) . '...' : $partner->address) : 'None' ?></td>

                                            <td>
                                                <?php
                                                $statusMap = [
                                                    'A' => ['label' => __('Active'), 'class' => 'badge-outline col-green'],
                                                    'I' => ['label' => __('Inactive'), 'class' => 'badge-outline col-red'],
                                                    'D' => ['label' => __('Deleted'), 'class' => 'badge-outline col-red']
                                                ];
                                                $status = $statusMap[$partner->status] ?? ['label' => __('Unknown'), 'class' => 'col-red'];
                                                ?>
                                                <span class="<?= h($status['class']) ?>">
                                                    <?= h($status['label']) ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php $i++; ?>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Vertically Center Modal -->
    <div class="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">Delete Confirmation</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p class="icon"><i class="fas fa-exclamation-triangle danger"></i></p>
                    <!-- <p>Are you sure you want to delete this partner?</p> -->
                     <p id="delete-warning-message">Are you sure you want to delete this partner?</p>
                </div>
                <div class="modal-footer br">
                    <button type="button" class="btn btn-primary confirm-delete" id="confirmDeleteBtn">Delete</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<!-- <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script> -->
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<script>
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    var table = $("#partner-table").DataTable({
        columnDefs: [{
            orderable: false,
            targets: [0] // "Action" column
        },
        {
            className: 'text-center',
            targets: [1] // Apply class to the ID/serial number column
        }
    ],
        
        "columns": [{
                "data": "actions",
                "render": function(data, type, row) {
                    return data;
                }
            },
            {
                data: "id"
            },
            {
                data: "name",
                "render": function(data, type, row) {
                    return `<td data-bs-toggle="tooltip" data-bs-placement="top" title="${data}">${data}</td>`;
                }
            },
            {
                data: "partner_type"
            },
            {
                data: "email"
            },
            {
                data: "phone"
            },
            {
                data: "address",
                "render": function(data, type, row) {
                    return `<td data-bs-toggle="tooltip" data-bs-placement="top" title="${data}">${data}</td>`;
                }
            },
            {
                data: "status"
            }
        ]
    });

    // $('#customSearchBox').on('keyup', function() {
    //     table.search(this.value).draw();
    // });

    function resetFilters() {
        // $('#customSearchBox').val('');
        $('#filterStatus').val('');
        table.search('').columns().search('').draw();
        table.ajax.url('<?= $this->Url->build(['controller' => 'Partners', 'action' => 'filterSearch']) ?>').load();
    }

    $('#filter').on('click', function(event) {
        performSearch();
    });

    function performSearch() {
        var filterStatus = $('#filterStatus').val();
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Partners', 'action' => 'filterSearch']) ?>',
            type: 'GET',
            data: {
                filterStatus: filterStatus
            },
            cache: false, // Prevent caching issues
            success: function(response) {
                console.log(response);
                table.clear().rows.add(response.data).draw();
            },
            error: function(xhr, status, error) {
                console.log('Error:', error);
            }
        });
    }

   let deleteUserId = null;

    $(document).on('click', '.delete-button', function () {
        deleteUserId = $(this).data('id');

        // Default message
        let message = "Are you sure you want to delete this partner?";

        // Check with server if this partner has courses
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'Partners', 'action' => 'hasCourses']) ?>/" + deleteUserId,
            type: 'GET',
            success: function (res) {
                if (res.hasCourses) {
                    message = "This partner is associated with one or more courses. Deleting this partner will also remove those courses. Do you want to proceed?";
                }
                $('#delete-warning-message').text(message);
            },
            error: function () {
                $('#delete-warning-message').text("Unable to check course associations. Are you sure you want to delete?");
            }
        });
    });

    $('#confirmDeleteBtn').on('click', function() {
        if (deleteUserId) {
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Partners', 'action' => 'delete']) ?>/" + deleteUserId,
                type: 'POST',
                headers: {
                    'X-CSRF-Token': $('meta[name="csrfToken"]').attr('content') // CakePHP 4/5 CSRF token
                },
                success: function(res) {
                    if (res.success) {
                        $('#exampleModalCenter').modal('hide');
                        location.reload(); // Refresh to reflect changes
                    } else {
                        alert(res.message || 'Something went wrong');
                    }
                },
                error: function() {
                    alert('Delete failed. Please try again.');
                }
            });
        }
    });

    // Download User CSV
    $(document).ready(function() {
        $("#downloadPartners").on("click", function() {
            var status = $("#filterStatus").val(); // Get selected filter status

            // Generate the download URL with status filter
            var downloadUrl = '<?= $this->Url->build(['controller' => 'Partners', 'action' => 'exportPartners']) ?>';
            if (status !== "") {
                downloadUrl += "?status=" + encodeURIComponent(status);
            }

            console.log("Downloading CSV with status:", status);
            window.location.href = downloadUrl; // Trigger file download
        });
    });
</script>
<?php $this->end(); ?>