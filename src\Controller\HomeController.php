<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Routing\Router;
use Cake\Http\Client;
use App\Service\WordpressImportService;
use Cake\Log\Log;
use Cake\Core\Configure;

/**
 * Home Controller
 *
 */
class HomeController extends AppController
{

    protected $CoursesTable;
    protected $PartnersTable;
    protected $CourseTypesTable;
    protected $YogaStylesTable;
    protected $PartnerTypesTable;
    protected $NewsletterSubscribersTable;
    protected $SpecialNeedsTable;
    protected $Cities;
    protected $Countries;
    protected $MasterDataTable;
    protected $Destinations;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('home');
        $this->loadComponent('Global');
        $this->CoursesTable     = $this->fetchTable('Courses');
        $this->PartnersTable    = $this->fetchTable('Partners');
        $this->CourseTypesTable = $this->fetchTable('CourseTypes');
        $this->YogaStylesTable  = $this->fetchTable('YogaStyles');
        $this->MasterDataTable  = $this->fetchTable('MasterData');
        $this->PartnerTypesTable          = $this->fetchTable('PartnerTypes');
        $this->NewsletterSubscribersTable = $this->fetchTable('NewsletterSubscribers');
        $this->SpecialNeedsTable = $this->fetchTable('SpecialNeeds');
        $this->Cities = $this->fetchTable('Cities');
        $this->Countries = $this->fetchTable('Countries');
        $this->Destinations = $this->fetchTable('Destinations');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        // Allow access to index action without login
        $this->Authentication->addUnauthenticatedActions(['destinationListPage','cityDetails','index', 'subscribeNewsletter', 'unsubscribeNewsletter']);
    }

     /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    public function cityDetails($name = null)
    {
        try {
            // if name is integer then it is id else slug
            $city = null;
            
            // Extract breadcrumb data from request parameters (for vanity URLs)
            $breadcrumbData = [
                'country' => $this->request->getParam('country'),
                'region' => $this->request->getParam('region'), 
                'state' => $this->request->getParam('state'),
                'city' => $this->request->getParam('city')
            ];
            

            
            if (is_numeric($name)) {
            $city = $this->Destinations->findBySlug($name, 'id')->firstOrFail();
            } else {
            // If name is not numeric, treat it as slug
            $city = $this->Destinations->findBySlug($name, 'slug')->firstOrFail();
            }










      $safeCall = function (callable $fn, string $label) {
            try {
                return $fn();
            } catch (\Exception $e) {
                Log::error("Failed to fetch $label: " . $e->getMessage());
                return [];
            }
        };
    
        $featured_courses   = $safeCall(fn() => $this->CoursesTable->getFeaturedCoursesHome(), 'featured courses');
        $featured_partners_list  = $safeCall(fn() => $this->PartnersTable->getFeaturesCentres(), 'featured partners');
        $course_types       = $safeCall(fn() => $this->CourseTypesTable->selectInputOptions(), 'course types');
        $yoga_styles        = $safeCall(fn() => $this->MasterDataTable->selectInputOptionsYogaStyles(), 'yoga styles');
        $special_needs   = $safeCall(fn() => $this->MasterDataTable->selectInputOptionsSpecialNeeds(), 'featured courses');
        $featured_teachers = [];

        try {
            $partnerType = $this->PartnerTypesTable->getByName('Teachers');
       
            if (!empty($featured_partners_list)) {
                foreach ($featured_partners_list as $key => $item) {
                    $styles = [];
                    $languages = [];
                    if (!empty($item->courses)) {
                        foreach ($item->courses as $course) {
                            if (!empty($course->yoga_styles)) {
                                foreach ($course->yoga_styles as $style) {
                                    $styles[$style->id] = $style->name;
                                }
                            }

                            if (!empty($course->language)) {
                                $languages[] = $course->language;
                            }
                        }
                    }
                 
                    $styles = implode(', ', array_values($styles));
                    $languages = implode(', ', array_unique($languages));
                    
                    $location = !empty($item->city) ? $item->city->name : '';
                    if(!empty($item->state)){
                        $location .= ', '.$item->state->name;
                    }
                    
                    // Teachers list //
                    if ($partnerType && $partnerType->id == $item->partner_type_id) {
                        $caption = '';
                        if($item->short_description){
                            $text = strip_tags($item->short_description);
                            $caption = strlen($text) > 50 ? substr($text, 0, 48) . '...' : $text;    
                        }
                      
                        $featured_teachers[] = [
                            'src'     => $item->logo_url . '?text=Item+' . $key,
                            'name'    => $item->name,
                            'caption' => $caption,
                            'slug'    => urlencode($item->slug),
                            'country' => $item->country ? $item->country->name : '',
                            'state'   => $item->state ? $item->state->name : '',
                            'city'    => $item->city ? $item->city->name : '',
                        ];
                    } else {
                        $featured_partners[] = [
                        'imgSrc' => $item->logo_url,
                        'imgAlt' => $item->name,
                        'info'   => $location,
                        'title'  => $item->name,
                        'desc'   => $item->short_description,
                        'slug'   => urlencode($item->slug),
                        'is_open' => $item->is_open ? 'Open' : 'Closed',
                        'styles' => $styles,
                        'lang'   => $languages,
                        'country'=> !empty($item->country) ? $item->country->name : 'india',
                        'state'=> !empty($item->state) ? $item->state->name : '',
                        'city'=> !empty($item->city) ? $item->city->name : '' 
                    ]; 
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error('Failed to prepare featured teachers: ' . $e->getMessage());
        }

        $imported = $safeCall(fn() => (new WordpressImportService())->importData(), 'WordPress import');







            $this->viewBuilder()->setLayout('default');
            $this->set(compact('city', 'breadcrumbData', 'featured_courses',
            'featured_partners',
            'course_types',
            'yoga_styles',
            'featured_teachers',
            'imported',
            'special_needs'));
        } catch (\Exception $e) {
            Log::error('Failed to fetch city details: ' . $e->getMessage());
            $this->Flash->error('City not found.');
            return $this->redirect(['action' => 'destinationListPage']);
        }

    }

    public function destinationListPage()
    {
        $data = $this->Destinations->getActiveCityNamesGroupedByRegionState();
        
        // Debug: Let's also get some basic stats
        $totalDestinations = $this->Destinations->find()->count();
        $totalCities = $this->Destinations->find()->where(['type' => 'city'])->count();
        $totalStates = $this->Destinations->find()->where(['type' => 'state'])->count();
        $activeCities = $this->Destinations->find()->where(['type' => 'city', 'status' => 'A'])->count();
        $activeStates = $this->Destinations->find()->where(['type' => 'state', 'status' => 'A'])->count();
        
        // Get raw data for debugging
        $sampleCities = $this->Destinations->find()
            ->select(['id', 'name', 'type', 'status', 'state_id'])
            ->where(['type' => 'city'])
            ->limit(5)
            ->enableHydration(false)
            ->toArray();
        
        // Get sample states and regions for debugging
        $sampleStates = $this->fetchTable('States')->find()
            ->select(['id', 'name', 'region_id'])
            ->limit(5)
            ->enableHydration(false)
            ->toArray();
            
        $sampleRegions = $this->fetchTable('Regions')->find()
            ->select(['id', 'name'])
            ->limit(5)
            ->enableHydration(false)
            ->toArray();
            
        // If no data, let's create a simple test structure
        if (empty($data) && $activeCities > 0) {
            $data = [
                'Test Region' => [
                    [
                        'state_name' => 'Test State',
                        'state_slug' => null,
                        'cities' => []
                    ]
                ]
            ];
            
            // Add first few active cities to test structure
            foreach ($sampleCities as $city) {
                if ($city['status'] === 'A') {
                    $cityObj = (object) [
                        'id' => $city['id'],
                        'name' => $city['name'],
                        'slug' => 'test-slug'
                    ];
                    $data['Test Region'][0]['cities'][] = $cityObj;
                }
            }
        }


        $safeCall = function (callable $fn, string $label) {
            try {
                return $fn();
            } catch (\Exception $e) {
                Log::error("Failed to fetch $label: " . $e->getMessage());
                return [];
            }
        };

        $imported = $safeCall(fn() => (new WordpressImportService())->importData(), 'WordPress import');

            
        $this->viewBuilder()->setLayout('default');
        $this->set(compact('imported', 'data', 'totalDestinations', 'totalCities', 'totalStates', 'activeCities', 'activeStates', 'sampleCities', 'sampleStates', 'sampleRegions'));
    }

    public function index()
    {
        // Safe call wrapper for clean error handling
        $safeCall = function (callable $fn, string $label) {
            try {
                return $fn();
            } catch (\Exception $e) {
                Log::error("Failed to fetch $label: " . $e->getMessage());
                return [];
            }
        };
    
        $featured_courses   = $safeCall(fn() => $this->CoursesTable->getFeaturedCoursesHome(), 'featured courses');
        $featured_partners_list  = $safeCall(fn() => $this->PartnersTable->getFeaturesCentres(), 'featured partners');
        $course_types       = $safeCall(fn() => $this->CourseTypesTable->selectInputOptions(), 'course types');
        $yoga_styles        = $safeCall(fn() => $this->MasterDataTable->selectInputOptionsYogaStyles(), 'yoga styles');
        $special_needs   = $safeCall(fn() => $this->MasterDataTable->selectInputOptionsSpecialNeeds(), 'featured courses');
        $featured_teachers = [];

        try {
            $partnerType = $this->PartnerTypesTable->getByName('Teachers');
       
            if (!empty($featured_partners_list)) {
                foreach ($featured_partners_list as $key => $item) {
                    $styles = [];
                    $languages = [];
                    if (!empty($item->courses)) {
                        foreach ($item->courses as $course) {
                            if (!empty($course->yoga_styles)) {
                                foreach ($course->yoga_styles as $style) {
                                    $styles[$style->id] = $style->name;
                                }
                            }

                            if (!empty($course->language)) {
                                $languages[] = $course->language;
                            }
                        }
                    }
                 
                    $styles = implode(', ', array_values($styles));
                    $languages = implode(', ', array_unique($languages));
                    
                    $location = !empty($item->city) ? $item->city->name : '';
                    if(!empty($item->state)){
                        $location .= ', '.$item->state->name;
                    }
                    
                    // Teachers list //
                    if ($partnerType && $partnerType->id == $item->partner_type_id) {
                        $caption = '';
                        if($item->short_description){
                            $text = strip_tags($item->short_description);
                            $caption = strlen($text) > 50 ? substr($text, 0, 48) . '...' : $text;    
                        }
                      
                        $featured_teachers[] = [
                            'src'     => $item->logo_url . '?text=Item+' . $key,
                            'name'    => $item->name,
                            'caption' => $caption,
                            'slug'    => urlencode($item->slug),
                            'country' => $item->country ? $item->country->name : '',
                            'state'   => $item->state ? $item->state->name : '',
                            'city'    => $item->city ? $item->city->name : '',
                        ];
                    } else {
                        $featured_partners[] = [
                        'imgSrc' => $item->logo_url,
                        'imgAlt' => $item->name,
                        'info'   => $location,
                        'title'  => $item->name,
                        'desc'   => $item->short_description,
                        'slug'   => urlencode($item->slug),
                        'is_open' => $item->is_open ? 'Open' : 'Closed',
                        'styles' => $styles,
                        'lang'   => $languages,
                        'country'=> !empty($item->country) ? $item->country->name : 'india',
                        'state'=> !empty($item->state) ? $item->state->name : '',
                        'city'=> !empty($item->city) ? $item->city->name : '' 
                    ]; 
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error('Failed to prepare featured teachers: ' . $e->getMessage());
        }

        $imported = $safeCall(fn() => (new WordpressImportService())->importData(), 'WordPress import');

        // Set layout and pass data to the view
        $this->viewBuilder()->setLayout('web');
        $this->set(compact(
            'featured_courses',
            'featured_partners',
            'course_types',
            'yoga_styles',
            'featured_teachers',
            'imported',
            'special_needs'
        ));
    }


    public function indexOld()
    {
        $featured_courses = $this->CoursesTable->getFeaturedCousres();
        $featured_partners = $this->PartnersTable->getFeaturesCentres(); // popular centres
        $course_types     = $this->CourseTypesTable->selectInputOptions();
        $yoga_styles      = $this->YogaStylesTable->selectInputOptions();
        $featured_teachers= [];
 
        $partnerType = $this->PartnerTypesTable->getByName('Teacher');

        if(!empty($featured_partners)){
            foreach($featured_partners as $key => $item){
                if($partnerType->id == $item->partner_type_id){
                    $text = strip_tags($item->description);
                    $caption = strlen($text) > 100 ? substr($text, 0, 97) . '...' : $text;

                    $featured_teachers[] = [
                        'src'    => $item->logo_url.'?text=Item+'.$key,
                        'name'   => $item->name,
                        'caption'=> $caption
                    ];
                }
            }
        }

        $wpimporter = new WordpressImportService();
        $imported   = $wpimporter->importData();

        $this->viewBuilder()->setLayout('home');
        $this->set(compact('featured_courses', 'featured_partners', 'course_types', 'yoga_styles', 'featured_teachers', 'imported'));
    }

    /**
     * View method
     *
     * @param string|null $id Home id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $home = $this->Home->get($id, ['contain' => []]);
        $this->set(compact('home'));
    }

   public function subscribeNewsletter()
    {
        try {
            $this->request->allowMethod(['post']);
            $reqdata = $this->request->getData();
            $lang = $this->request->getParam('lang') ?? 'en';

            // Validate email
            if (empty($reqdata['email']) || !filter_var($reqdata['email'], FILTER_VALIDATE_EMAIL)) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => 'Please enter a valid email address.',
                ]));
            }

            $domain = substr(strrchr($reqdata['email'], "@"), 1);
         
            if (!checkdnsrr($domain, 'MX')) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => 'Email domain is not valid or unable to receive emails.',
                ]));
            }
            
            // Check if subscriber already exists
            $existingSubscriber = $this->NewsletterSubscribersTable->find()
                ->where(['email' => $reqdata['email']])
                ->first();

            if ($existingSubscriber) {
                if ($existingSubscriber->status === 'A') {
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'success' => false,
                        'message' => 'This email is already subscribed.',
                    ]));
                } else {
                    // Reactivate and send email again
                    $existingSubscriber->status = 'A';
                    if ($this->NewsletterSubscribersTable->save($existingSubscriber)) {
                        $emailVar = [
                            'course_listing_url' => Router::url("/{$lang}/yoga-courses/india", true),
                            'subscriber_id' => $existingSubscriber->id
                        ];

                        $this->Global->send_email(
                            $existingSubscriber->email,
                            Configure::read('Settings.FROM_EMAIL'),
                            "Welcome back to Yoga.in's Community",
                            'newsletter_subscription',
                            $emailVar
                        );

                        return $this->response->withType('application/json')->withStringBody(json_encode([
                            'success' => true,
                            'message' => 'Subscribed successfully!',
                        ]));
                    }

                    // Fails to save existing subscriber
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'success' => false,
                        'message' => 'Could not Subscribe. Please try again.',
                    ]));
                }
            }

            // New subscription
            $subscriber = $this->NewsletterSubscribersTable->newEmptyEntity();
            $subscriber = $this->NewsletterSubscribersTable->patchEntity($subscriber, [
                'email' => $reqdata['email'],
                'status' => 'A'
            ]);

            if ($subscriber->getErrors()) {
                $errors = $subscriber->getErrors();
                $firstErrorMessage = $this->Global->formatValidationErrors($errors);
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => $firstErrorMessage,
                    'errors'  => $errors
                ]));
            }

            if ($this->NewsletterSubscribersTable->save($subscriber)) {
              
                $emailVar = [
                    'course_listing_url' => Router::url("/{$lang}/yoga-courses/india", true),
                    'subscriber_id' => $subscriber->id
                ];

                $this->Global->send_email(
                    $subscriber->email,
                    Configure::read('Settings.FROM_EMAIL'),
                    "Welcome to Yoga.in's Community",
                    'newsletter_subscription',
                    $emailVar
                );

                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => true,
                    'message' => 'Subscribed successfully!',
                ]));
            }

            // If save fails
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => 'Could not subscribe. Please try again.',
            ]));

        } catch (\Exception $e) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => 'Something went wrong! Please try again.',
            ]));
        }
    }


    public function unsubscribeNewsletter($id){
        $this->viewBuilder()->setLayout('default');
        $this->request->allowMethod(['GET']);

        $decodedId = base64_decode($id, true);
        $error = '';
        if (!$decodedId || !is_numeric($decodedId)) {
            $error = 'Invalid or expired unsubscription link.';
        }

        $subscriber = $this->NewsletterSubscribersTable->find()
            ->where(['id' => $decodedId,  'status' => 'I'])
            ->first();

        if (!$subscriber) {
            $error = 'Invalid or expired unsubscription link.';

        } else {
            $subscriber->status = 'I';
            if ($this->NewsletterSubscribersTable->save($subscriber)) {
                $this->Flash->success('You have been unsubscribed from our newsletter.');
            
            } else {
                $error = 'Something went wrong. Please try again later.';
            }
        }
    
        $this->set(compact('subscriber', 'error'));
        return $this->render('unsubscribed'); 
    }
}
