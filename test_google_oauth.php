<?php
// Simple test to verify Google OAuth setup
$clientId = '625613922975-4n5ovn2qslvdibe0jk6knb9m3msmgb7t.apps.googleusercontent.com';
$clientSecret = '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';
$siteUrl = 'http://localhost:8080/';
$redirectUri = rtrim($siteUrl, '/') . '/login/social/google/callback';

echo "=== Google OAuth Configuration Test ===" . PHP_EOL;
echo "Client ID: " . $clientId . PHP_EOL;
echo "Client Secret: " . substr($clientSecret, 0, 10) . "..." . PHP_EOL;
echo "Redirect URI: " . $redirectUri . PHP_EOL;
echo "Site URL: " . $siteUrl . PHP_EOL;

$authUrl = 'https://accounts.google.com/o/oauth2/auth?' . http_build_query([
    'client_id' => $clientId,
    'redirect_uri' => $redirectUri,
    'scope' => 'email profile',
    'response_type' => 'code',
    'access_type' => 'offline',
    'prompt' => 'consent'
]);

echo PHP_EOL . "=== Generated Auth URL ===" . PHP_EOL;
echo $authUrl . PHP_EOL;
echo PHP_EOL . "Social login should work now!" . PHP_EOL;
?>
