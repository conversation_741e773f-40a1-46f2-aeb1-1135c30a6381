<?php

return [

    'Features' => [
        'introduction' => 'Introduction',
        'excursions' => 'Excursions',
        'getting_to' => 'Getting To',
        'more_details' => 'More Details',
        'places_to_see' => 'Places to See',
        'finding_accommodation' => 'Finding Accommodation',
        'other_resources' => 'Other Resources',
        'best_time_to_visit' => 'Best Time To Visit',
        'where_to_eat' => 'Where to Eat'
    ],

    'NearPlaces' => [
        'near_beach' => 'Near Beach',
        'near_mountain' => 'Near Mountain',
    ],
    // Records Techniques in each table without delete
    'Techniques' => [
        'Pranayama' => 'Pranayama',
        'Meditation' => 'Meditation',
        'Cleansing' => 'Cleansing',
        'Chanting' => 'Chanting',
    ],

    // Records language in each table without delete
    'Language' => [
        'English' => 'English',
        'Spanish' => 'Spanish',
        'French'  => 'French',
        'Arabic'  => 'Arabic'
    ],

    // Records Level in each table without delete
    'Level' => [
        'Beginner' => 'Beginner',
        'Intermediate' => 'Intermediate',
        'Advanced' => 'Advanced'
    ],

    'Food' => [
        'Vegetarian' => 'Vegetarian',
        'Vegan' => 'Vegan',
        'Satvik' => 'Satvik'
    ],

    'Accommodation' => [
        'Single' => 'Single',
        'Double' => 'Double',
        'Luxury' => 'Luxury',
        'Budget' => 'Budget',
        'Shared' => 'Shared',
        'Private'=> 'Private'
    ],
    // Records status in each table without delete
    'STATUS' => [
        'A' => 'Active',
        'I' => 'Inactive',
        //'D' => 'Deleted'
    ],

    // Records status in each table with delete
    'STATUS_ALL' => [
        'A' => 'Active',
        'I' => 'Inactive',
        'D' => 'Deleted'
    ],

    //Status Map color code
    'STATUS_MAP' => [
        'A' => ['label' => 'Active', 'class' => 'label-sucess'],
        'I' => ['label' => 'Inactive', 'class' => 'label-progress'],
        'D' => ['label' => 'Deleted', 'class' => 'label-fail']
    ],

    'LANGUAGE_MAP' => [
        'AF' => 'fa',   // Afghanistan – Persian (Dari)
        'AL' => 'sq',   // Albania – Albanian
        'DZ' => 'ar',   // Algeria – Arabic
        'AR' => 'es',   // Argentina – Spanish
        'AM' => 'hy',   // Armenia – Armenian
        'AU' => 'en',   // Australia – English
        'AT' => 'de',   // Austria – German
        'AZ' => 'az',   // Azerbaijan – Azerbaijani
        'BD' => 'bn',   // Bangladesh – Bengali
        'BE' => 'nl',   // Belgium – Dutch (can also be fr, de)
        'BR' => 'pt',   // Brazil – Portuguese
        'BG' => 'bg',   // Bulgaria – Bulgarian
        'CA' => 'en',   // Canada – English (can also be fr)
        'CL' => 'es',   // Chile – Spanish
        'CN' => 'zh',   // China – Chinese (Simplified)
        'CO' => 'es',   // Colombia – Spanish
        'CR' => 'es',   // Costa Rica – Spanish
        'HR' => 'hr',   // Croatia – Croatian
        'CZ' => 'cs',   // Czech Republic – Czech
        'DK' => 'da',   // Denmark – Danish
        'EG' => 'ar',   // Egypt – Arabic
        'EE' => 'et',   // Estonia – Estonian
        'FI' => 'fi',   // Finland – Finnish
        'FR' => 'fr',   // France – French
        'GE' => 'ka',   // Georgia – Georgian
        'DE' => 'de',   // Germany – German
        'GR' => 'el',   // Greece – Greek
        'HK' => 'zh',   // Hong Kong – Chinese (Trad or Simplified)
        'HU' => 'hu',   // Hungary – Hungarian
        'IS' => 'is',   // Iceland – Icelandic
        'IN' => 'hi',   // India – Hindi
        'ID' => 'id',   // Indonesia – Indonesian
        'IR' => 'fa',   // Iran – Persian
        'IQ' => 'ar',   // Iraq – Arabic
        'IE' => 'en',   // Ireland – English
        'IL' => 'he',   // Israel – Hebrew
        'IT' => 'it',   // Italy – Italian
        'JP' => 'ja',   // Japan – Japanese
        'JO' => 'ar',   // Jordan – Arabic
        'KZ' => 'kk',   // Kazakhstan – Kazakh
        'KE' => 'sw',   // Kenya – Swahili
        'KR' => 'ko',   // South Korea – Korean
        'KW' => 'ar',   // Kuwait – Arabic
        'LV' => 'lv',   // Latvia – Latvian
        'LB' => 'ar',   // Lebanon – Arabic
        'LT' => 'lt',   // Lithuania – Lithuanian
        'LU' => 'fr',   // Luxembourg – French (also de, lb)
        'MY' => 'ms',   // Malaysia – Malay
        'MX' => 'es',   // Mexico – Spanish
        'MA' => 'ar',   // Morocco – Arabic
        'NL' => 'nl',   // Netherlands – Dutch
        'NZ' => 'en',   // New Zealand – English
        'NG' => 'en',   // Nigeria – English
        'NO' => 'no',   // Norway – Norwegian
        'PK' => 'ur',   // Pakistan – Urdu
        'PH' => 'tl',   // Philippines – Tagalog (Filipino)
        'PL' => 'pl',   // Poland – Polish
        'PT' => 'pt',   // Portugal – Portuguese
        'RO' => 'ro',   // Romania – Romanian
        'RU' => 'ru',   // Russia – Russian
        'SA' => 'ar',   // Saudi Arabia – Arabic
        'RS' => 'sr',   // Serbia – Serbian
        'SG' => 'en',   // Singapore – English
        'SK' => 'sk',   // Slovakia – Slovak
        'SI' => 'sl',   // Slovenia – Slovenian
        'ZA' => 'en',   // South Africa – English
        'ES' => 'es',   // Spain – Spanish
        'LK' => 'si',   // Sri Lanka – Sinhala
        'SE' => 'sv',   // Sweden – Swedish
        'CH' => 'de',   // Switzerland – German (also fr, it, rm)
        'SY' => 'ar',   // Syria – Arabic
        'TW' => 'zh',   // Taiwan – Chinese (Traditional)
        'TH' => 'th',   // Thailand – Thai
        'TR' => 'tr',   // Turkey – Turkish
        'UA' => 'uk',   // Ukraine – Ukrainian
        'AE' => 'ar',   // UAE – Arabic
        'GB' => 'en',   // United Kingdom – English
        'US' => 'en',   // United States – English
        'UZ' => 'uz',   // Uzbekistan – Uzbek
        'VN' => 'vi',   // Vietnam – Vietnamese
        'YE' => 'ar',   // Yemen – Arabic
    ],
    'META_ROBOT_OPTIONS' => [
        'index, follow' => 'Index, Follow',
        'noindex, follow' => 'NoIndex, Follow',
        'index, nofollow' => 'Index, NoFollow',
        'noindex, nofollow' => 'NoIndex, NoFollow',
    ],
    'EXCHANGERATES'=>[
        'INR'=> 1,
        'USD'=> 0.012,
        'EUR'=> 0.011
    ],

];
