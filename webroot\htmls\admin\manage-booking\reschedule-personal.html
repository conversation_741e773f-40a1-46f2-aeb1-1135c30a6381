<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>

<body>
    <section class="group-booking grp-booking-body-content">
        <div class="container mx-auto px-4">
            <div class="flex items-center space-x-4 p-6 bg-[] grp-booking-body">
                <!-- Image -->
                <img src="https://www.shutterstock.com/shutterstock/videos/1074813608/thumb/1.jpg" alt="Yoga Group"
                    class="w-24 h-24 rounded-lg object-cover" />

                <!-- Text Content -->
                <div class="content">
                    <!-- Organizer -->
                    <p class="text-sm text-[#D87A61] font-medium">@The Art of Living</p>

                    <!-- Title -->
                    <h2 class="text-xl md:text-2xl font-bold text-gray-800">
                        200-Hour Yoga Teacher Training in India
                    </h2>

                    <!-- Info Row -->
                    <div class="flex items-center mt-2 space-x-6 text-sm text-gray-700 time-info hidden sm:block">
                        <!-- Time -->
                        <div class="flex items-center space-x-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                                fill="none">
                                <path
                                    d="M14.6673 7.99967C14.6673 11.6797 11.6807 14.6663 8.00065 14.6663C4.32065 14.6663 1.33398 11.6797 1.33398 7.99967C1.33398 4.31967 4.32065 1.33301 8.00065 1.33301C11.6807 1.33301 14.6673 4.31967 14.6673 7.99967Z"
                                    stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                                <path opacity="0.4"
                                    d="M10.4739 10.1202L8.40724 8.88684C8.04724 8.6735 7.75391 8.16017 7.75391 7.74017V5.00684"
                                    stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <span><span class="italic font-semibold">Mon - Fri : 10:00 AM to 6:00 PM</span></span>
                        </div>

                        <!-- Language -->
                        <div class="flex items-center space-x-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                                fill="none">
                                <path
                                    d="M8.00065 14.6663C11.6825 14.6663 14.6673 11.6816 14.6673 7.99967C14.6673 4.31778 11.6825 1.33301 8.00065 1.33301C4.31875 1.33301 1.33398 4.31778 1.33398 7.99967C1.33398 11.6816 4.31875 14.6663 8.00065 14.6663Z"
                                    stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                                <g opacity="0.4">
                                    <path d="M5.33372 2H6.00039C4.70039 5.89333 4.70039 10.1067 6.00039 14H5.33372"
                                        stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                                    <path d="M10 2C11.3 5.89333 11.3 10.1067 10 14" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path d="M2 10.6667V10C5.89333 11.3 10.1067 11.3 14 10V10.6667" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path d="M2 6.00039C5.89333 4.70039 10.1067 4.70039 14 6.00039" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                </g>
                            </svg>
                            <span class="italic font-semibold">English</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content block sm:hidden ">
                <!-- Info Row -->
                <div class="flex items-center text-sm text-gray-600 mt-1 date-content justify-between py-2">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M5.33398 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10"
                                stroke-linecap="round" stroke-linejoin="round" />
                            <path d="M10.666 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10"
                                stroke-linecap="round" stroke-linejoin="round" />
                            <path opacity="0.4" d="M2.33398 6.05957H13.6673" stroke="#C45F44" stroke-miterlimit="10"
                                stroke-linecap="round" stroke-linejoin="round" />
                            <path
                                d="M14.6673 12.6667C14.6673 13.1667 14.5273 13.64 14.2807 14.04C13.8207 14.8133 12.974 15.3333 12.0007 15.3333C11.3273 15.3333 10.714 15.0867 10.2473 14.6667C10.0407 14.4933 9.86065 14.28 9.72065 14.04C9.47399 13.64 9.33398 13.1667 9.33398 12.6667C9.33398 11.1933 10.5273 10 12.0007 10C12.8007 10 13.514 10.3533 14.0007 10.9067C14.414 11.38 14.6673 11.9933 14.6673 12.6667Z"
                                stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path d="M10.9609 12.667L11.6209 13.327L13.0409 12.0137" stroke="#C45F44"
                                stroke-linecap="round" stroke-linejoin="round" />
                            <path
                                d="M14 5.66634V10.9063C13.5133 10.353 12.8 9.99967 12 9.99967C10.5267 9.99967 9.33333 11.193 9.33333 12.6663C9.33333 13.1663 9.47333 13.6397 9.72 14.0397C9.86 14.2797 10.04 14.493 10.2467 14.6663H5.33333C3 14.6663 2 13.333 2 11.333V5.66634C2 3.66634 3 2.33301 5.33333 2.33301H10.6667C13 2.33301 14 3.66634 14 5.66634Z"
                                stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path opacity="0.4" d="M7.99764 9.13314H8.00363" stroke="#C45F44" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path opacity="0.4" d="M5.52889 9.13314H5.53488" stroke="#C45F44" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path opacity="0.4" d="M5.5293 11.1338H5.53528" stroke="#C45F44" stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                        <span class="date pl-2 italic font-bold text-[#000000]">06 Mar 2025 - 06 Apr 2025</span>
                    </div>
                    <span class="ml-4 flex items-center date">
                        <span class="px-2 text-[#00AE4D] text-[10px] border border-[#00AE4D] rounded-full font-bold">2
                            Days to Go</span>
                    </span>
                </div>
            </div>
        </div>
    </section>

    <section class="select-branch bg-[#FFF0E9]">
        <div class="container mx-auto px-4">
            <div class="select-branch-grid-content py-10 px-4">
                <div class=" mx-auto text-center">
                    <!-- Slot Date -->
                    <div class="mb-8 flex items-center pt-5 justify-center relative">
                        <h3 class="text-lg font-semibold text-gray-800 mr-2 slot-title">
                            Slot Date
                        </h3>

                        <!-- Date Input Wrapper -->
                        <div class="relative">
                            <input type="date" id="slot-date"
                                class="slot-date select-date bg-[#ffffff00] border-b-2 border-gray-300 text-gray-600 px-10 py-2 w-60 text-center outline-none"
                                placeholder="DD / MMM / YYYY" />
                            <!-- SVG Icon Positioned -->
                            <div class="absolute left-2 top-1/2 -translate-y-1/2 pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19"
                                    fill="none">
                                    <path
                                        d="M13.2609 2.81801V1.58301C13.2609 1.25842 12.9918 0.989258 12.6672 0.989258C12.3426 0.989258 12.0734 1.25842 12.0734 1.58301V2.77051H6.92759V1.58301C6.92759 1.25842 6.65842 0.989258 6.33384 0.989258C6.00926 0.989258 5.74009 1.25842 5.74009 1.58301V2.81801C3.60259 3.01592 2.56551 4.29051 2.40717 6.18259C2.39134 6.41217 2.58134 6.60217 2.80301 6.60217H16.198C16.4276 6.60217 16.6176 6.40426 16.5938 6.18259C16.4355 4.29051 15.3984 3.01592 13.2609 2.81801Z"
                                        fill="#C45F44" />
                                    <path opacity="0.4"
                                        d="M16.625 8.58182V9.95931C16.625 10.4422 16.1975 10.8143 15.7225 10.7352C15.5008 10.7035 15.2713 10.6797 15.0417 10.6797C12.6429 10.6797 10.6875 12.6351 10.6875 15.0339C10.6875 15.3981 10.83 15.9047 10.9804 16.3639C11.1546 16.8785 10.7746 17.4089 10.2283 17.4089H6.33333C3.5625 17.4089 2.375 15.8256 2.375 13.4506V8.57389C2.375 8.13848 2.73125 7.78223 3.16667 7.78223H15.8333C16.2688 7.79014 16.625 8.1464 16.625 8.58182Z"
                                        fill="#C45F44" />
                                    <path
                                        d="M15.0417 11.875C13.2921 11.875 11.875 13.2921 11.875 15.0417C11.875 15.6354 12.0413 16.1975 12.3342 16.6725C12.8804 17.5908 13.8858 18.2083 15.0417 18.2083C16.1975 18.2083 17.2029 17.5908 17.7492 16.6725C18.0421 16.1975 18.2083 15.6354 18.2083 15.0417C18.2083 13.2921 16.7913 11.875 15.0417 11.875ZM16.6804 14.7013L14.9942 16.2608C14.8833 16.3638 14.7329 16.4192 14.5904 16.4192C14.44 16.4192 14.2896 16.3637 14.1708 16.245L13.3871 15.4612C13.1575 15.2317 13.1575 14.8517 13.3871 14.6221C13.6167 14.3925 13.9967 14.3925 14.2263 14.6221L14.6062 15.0021L15.8729 13.8304C16.1104 13.6088 16.4904 13.6246 16.7121 13.8621C16.9338 14.0996 16.9179 14.4717 16.6804 14.7013Z"
                                        fill="#C45F44" />
                                    <path
                                        d="M6.72917 11.8751C6.52333 11.8751 6.3175 11.788 6.16708 11.6455C6.02458 11.495 5.9375 11.2892 5.9375 11.0834C5.9375 10.8776 6.02458 10.6717 6.16708 10.5213C6.34917 10.3392 6.62625 10.2521 6.8875 10.3075C6.935 10.3155 6.9825 10.3313 7.03 10.355C7.0775 10.3709 7.125 10.3946 7.1725 10.4263C7.21208 10.458 7.25167 10.4896 7.29125 10.5213C7.43375 10.6717 7.52083 10.8776 7.52083 11.0834C7.52083 11.2892 7.43375 11.495 7.29125 11.6455C7.25167 11.6771 7.21208 11.7088 7.1725 11.7405C7.125 11.7721 7.0775 11.7959 7.03 11.8117C6.9825 11.8355 6.935 11.8513 6.8875 11.8592C6.83208 11.8671 6.77667 11.8751 6.72917 11.8751Z"
                                        fill="#C45F44" />
                                    <path
                                        d="M9.50065 11.8752C9.29482 11.8752 9.08899 11.7881 8.93857 11.6456C8.79607 11.4952 8.70898 11.2894 8.70898 11.0835C8.70898 10.8777 8.79607 10.6719 8.93857 10.5214C9.2394 10.2285 9.76982 10.2285 10.0627 10.5214C10.2052 10.6719 10.2923 10.8777 10.2923 11.0835C10.2923 11.2894 10.2052 11.4952 10.0627 11.6456C9.91232 11.7881 9.70648 11.8752 9.50065 11.8752Z"
                                        fill="#C45F44" />
                                    <path
                                        d="M6.72917 14.6459C6.52333 14.6459 6.3175 14.5588 6.16708 14.4163C6.02458 14.2659 5.9375 14.0601 5.9375 13.8543C5.9375 13.6484 6.02458 13.4426 6.16708 13.2922C6.24625 13.2209 6.32542 13.1655 6.42834 13.1259C6.72125 12.9993 7.06958 13.0705 7.29125 13.2922C7.43375 13.4426 7.52083 13.6484 7.52083 13.8543C7.52083 14.0601 7.43375 14.2659 7.29125 14.4163C7.14083 14.5588 6.935 14.6459 6.72917 14.6459Z"
                                        fill="#C45F44" />
                                </svg>
                            </div>
                        </div>
                    </div>


                    <!-- Time Slot Title -->
                    <h3 class="text-lg font-semibold text-gray-800 mb-2 slot-title">
                        Select Time Slot <span class="italic font-normal">(IST Timings)</span>
                    </h3>

                    <!-- Time Slots Grid -->
                    <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-4 content justify-center mt-4">
                        <!-- Replace each date button with time slot -->
                        <button
                            class="select-date bg-white px-6 py-3 border border-[#C45F44] rounded-full text-center font-semibold text-sm text-gray-900">5:30
                            AM</button>
                        <button
                            class="select-date bg-[#C45F44] text-white px-6 py-3 rounded-full text-center font-semibold text-sm">6:30
                            AM</button>
                        <button
                            class="select-date bg-white px-6 py-3 border border-[#C45F44] rounded-full text-center font-semibold text-sm text-gray-900">7:30
                            AM</button>
                        <button
                            class="select-date bg-white px-6 py-3 border border-[#C45F44] rounded-full text-center font-semibold text-sm text-gray-900">8:30
                            AM</button>
                        <button
                            class="select-date bg-white px-6 py-3 border border-[#C45F44] rounded-full text-center font-semibold text-sm text-gray-900">9:30
                            AM</button>
                        <button
                            class="select-date bg-white px-6 py-3 border border-[#C45F44] rounded-full text-center font-semibold text-sm text-gray-900">10:30
                            AM</button>
                        <button
                            class="select-date bg-white px-6 py-3 border border-[#C45F44] rounded-full text-center font-semibold text-sm text-gray-900">12:30
                            PM</button>
                        <button
                            class="select-date bg-white px-6 py-3 border border-[#C45F44] rounded-full text-center font-semibold text-sm text-gray-900">1:30
                            PM</button>
                        <button
                            class="select-date bg-white px-6 py-3 border border-[#C45F44] rounded-full text-center font-semibold text-sm text-gray-900">2:30
                            PM</button>
                        <button
                            class="select-date bg-white px-6 py-3 border border-[#C45F44] rounded-full text-center font-semibold text-sm text-gray-900">3:30
                            PM</button>
                        <button
                            class="select-date bg-gray-300 px-6 py-3 border border-black rounded-full text-center font-semibold text-sm text-gray-800">4:30
                            PM</button>
                        <button
                            class="select-date bg-white px-6 py-3 border border-[#C45F44] rounded-full text-center font-semibold text-sm text-gray-900">5:30
                            PM</button>
                    </div>
                </div>
            </div>


        </div>
    </section>
    <div class="container mx-auto px-4">
        <!-- Submit Button -->
        <button class="w-full bg-[#D87A61] hover:bg-[#D87A61] text-white font-semibold py-2 rounded-md mt-6">
            Reschedule
        </button>
    </div>
</body>


<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

    .group-booking .cards .date-content .date {
        font-size: 11px;
        font-style: italic;
        font-family: "Open Sans", sans-serif;
        font-weight: 600;
        color: #000000;
    }

    .group-booking h2 {
        font-family: "Open Sans", sans-serif;
        font-size: 40px;
        font-weight: 700;
        color: #293148;
    }

    .group-booking .content p {
        font-family: "Open Sans", sans-serif;
        font-size: 18px;
        font-weight: 600;
    }

    .time-info .italic {
        font-family: "Open Sans", sans-serif;
        font-style: italic;
        font-size: 20px;
        font-weight: 600;
    }

    .grp-booking-body {
        border-bottom: 1px solid #C45F44;
        padding: 40px 0px;
    }

    /* section1 */
    /* section2 */
    .select-branch h3 {
        font-family: "Open Sans", sans-serif;
        font-size: 32px;
        font-weight: 700;
    }

    .select-branch .select-date {
        font-family: "Open Sans", sans-serif;
        font-size: 20px;
        font-weight: 700;

    }

    .select-branch .slot-title {
        padding: 0px !important;
    }

    @media only screen and (max-width: 700px) {
        .group-booking h2 {
            font-size: 18px;
        }

        .select-branch .slot-date {
            font-size: 14px !important;
            color: #231F204D !important;
            font-weight: 600;
            padding: 10px 10px 10px 15% !important;
        }

        input[type="date"]::-webkit-calendar-picker-indicator {
            opacity: 0;
            display: none;
        }

        .group-booking .content p {
            font-family: "Poppins", sans-serif;
            font-size: 10px;
            font-weight: 600;
        }

        .group-booking .cards .date-content .date {
            font-size: 11px;
            font-style: italic;
            font-family: "Open Sans", sans-serif;
            font-weight: 600;
            color: #000000;
        }

        .select-branch h3 {
            font-family: "Poppins", sans-serif;
            padding: 30px 0px 0px;
            font-size: 14px;
            font-weight: 500;
        }

        .select-branch .select-date {
            font-size: 10px;
            padding: 10px 5px;
            color: #000000;
        }

        .select-branch .select-date:hover {
            font-size: 10px;
            background: #C45F44;
            color: #FFFFFF;
            padding: 10px 5px;
        }

        .grp-booking-body {
            border-bottom: 0px solid #C45F44;
            padding: 40px 0px 0px;
        }

        .grp-booking-body-content {
            border-bottom: 2px solid #C45F44;
            padding: 0px 0px 20px;
        }

        .time-info .italic {
            font-size: 11px;
        }

        .select-branch-grid-content {
            padding: 0px 0px 30px;
        }

        .Participants h1 {
            font-size: 18px;
            font-weight: 700;
        }

        .Participants .head .increment-decrement {
            width: 110px;
            background: #FDEDE0;
            border-radius: 12px;
            padding: 5px;
            margin: 0px;
            justify-content: space-between;
        }

        .Participants {
            background: #FFFFFF;
        }

        .Participants .head .increment-decrement span {
            margin: 0px 5px;
            line-height: 12px;
        }

        .Participants .head .increment-decrement #decreaseBtn,
        .Participants .head .increment-decrement #increaseBtn {
            height: 23px;
            width: 23px !important;
            margin: 0px;
            font-weight: 600;
            line-height: 12px;
            padding-bottom: 3px;
        }

        .Participants #accordionContainer .rounded-md {
            border-radius: 11px;
            padding: 10px;
            font-size: 15px;
            font-weight: 600;
        }

        .form-content-title {
            font-size: 14px;
            font-weight: 400;
        }

        .form-content {
            justify-content: space-between;
        }

        .form-content input,
        .form-content select {
            border: 1px solid #6C6C6C;
            border-radius: 20px;
            background: #fff;
            padding: 13px 5px;
        }

        .form-content select {
            color: #00000033
        }

        .form-content .select-state {
            border: 1px solid #6C6C6C;
            border-radius: 20px;
            background: #fff;
            padding: 5px 10px;
        }

        .form-phone .code {
            border-top-left-radius: 20px;
            border-bottom-left-radius: 20px;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .form-phone .phone {
            border-top-right-radius: 20px;
            border-bottom-right-radius: 20px;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        .form-content .country {
            font-size: 16px;
            font-weight: 500;
            color: #344054;
            font-family: "Inter", sans-serif;
        }

        .head-sec-title {
            font-size: 18px;
            font-weight: 700;
            color: #6A9E74;
            text-transform: capitalize;
        }

        .cost-price {
            font-size: 18px;
            font-weight: 700;
            font-family: "Open Sans", sans-serif;

        }

        .cost-title {
            font-family: "Open Sans", sans-serif;
            font-size: 12px;
            font-weight: 600;
            color: #344054;
        }

        .item-left {
            font-family: "Open Sans", sans-serif;
            font-size: 12px;
            font-style: italic;
            font-weight: 600;
            color: #00AE4D;
        }

        .full-booked {
            font-family: "Open Sans", sans-serif;
            font-size: 12px;
            font-style: italic;
            font-weight: 600;
            color: #000000;
        }

        .billingCurrency-body label {
            font-size: 14px;
            font-weight: 600;
            font-family: "Open Sans", sans-serif;
            color: #000000;
        }

        .billingCurrency-body .relative select {
            font-size: 14px;
            font-weight: 700;
            font-family: "Open Sans", sans-serif;
            color: #000000;
        }

        .exc-rate .title {
            font-style: italic;
            font-size: 12px;
            font-weight: 600;
            font-family: "Open Sans", sans-serif;
            text-decoration: underline;
        }

        .exc-rate .list-rate {
            font-style: italic;
            font-size: 12px;
            font-weight: 600;
            color: #000000;
        }

        .exc-rate .Disclaimer {
            color: #888888;
            font-size: 8px;
            font-weight: 600;
            font-style: italic;
        }

        .billingCurrency-element {
            border-radius: 6px;
        }

        .billed .title {
            font-size: 14px;
            font-weight: 400;
            font-family: "Open Sans", sans-serif;
        }

        .billed .body select {
            font-size: 15px;
            color: #983419;
            font-weight: 600;
        }

        .privacy-policy p {
            color: #000000;
            font-size: 10px;
            font-family: "Poppins", sans-serif;
        }

        .privacy-policy a {
            font-weight: 500;
            color: #0F43CA;
            font-size: 10px;
            font-family: "Poppins", sans-serif;
        }

        .payment-summary button {
            font-size: 15px;
            font-weight: 700;
            color: #283148;
            font-family: "Open Sans", sans-serif;
        }

        #payment-summary-content h2 {
            font-size: 14px;
            color: #000000;
            font-weight: 700;
            font-family: "Open Sans", sans-serif;
        }

        .about-user {
            font-size: 12px;
            color: #727272;
            font-weight: 600;
            font-family: "Open Sans", sans-serif;
        }

        .cost-breakdown {
            font-style: italic;
            font-size: 14px;
            font-weight: 400;
            font-family: "Open Sans", sans-serif;
        }

        .cost-breakdown .text-red-600 {
            color: #62200E;
            font-weight: 600;
        }

        .cost-breakdown .discount-applied .text-red-600 {
            color: #FF0000;
            font-weight: 600;
        }

        .cost-breakdown .new {
            font-weight: 700;
            font-family: "Open Sans", sans-serif;
        }

        .cost-breakdown .total .text-red-600 {
            font-weight: 700;
        }

        .discount button {
            color: #479456;
        }

        .total-before-tax span {
            font-weight: 600;
            font-size: 14px;
            font-family: "Open Sans", sans-serif;
            color: #000000;
        }

        .total-before-tax .value {
            font-weight: 700;
            font-size: 16px;
            font-family: "Open Sans", sans-serif;
            color: #000000;
        }

        .Applicable-tax .value {
            font-weight: 700;
            font-size: 12px;
            font-family: "Open Sans", sans-serif;
            color: #505050;
        }
    }

    @media only screen and (max-width: 390px) {
        .Participants .head .increment-decrement span {
            margin: 0px 5px;
            line-height: 12px;
            font-size: 17px;
        }
    }
</style>

</html>