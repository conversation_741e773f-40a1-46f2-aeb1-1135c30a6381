<?php
declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;

/**
 * BookingItemsFixture
 */
class BookingItemsFixture extends TestFixture
{
    /**
     * Init method
     *
     * @return void
     */
    public function init(): void
    {
        $this->records = [
            [
                'id' => 1,
                'booking_id' => 1,
                'title' => 'Lorem ipsum dolor sit amet',
                'first_name' => 'Lorem ipsum dolor sit amet',
                'last_name' => 'Lorem ipsum dolor sit amet',
                'email' => 'Lorem ipsum dolor sit amet',
                'phone' => 'Lorem ipsum dolor sit amet',
                'age' => 1,
                'food' => 'Lorem ipsum dolor sit amet',
                'residency' => 'Lorem ipsum dolor sit amet',
                'currency' => 'Lorem ipsum dolor sit amet',
                'base_price_id' => 1,
                'base_price_name' => 'Lorem ipsum dolor sit amet',
                'base_price_amount' => 1.5,
                'base_price_currency' => 1.5,
                'hourly_rate' => 1.5,
                'exchange_rate' => 1.5,
                'discount_id' => 1,
                'discount_value' => 1.5,
                'tax_amount' => 1.5,
                'tax_rate' => 1.5,
                'sub_total' => 1.5,
                'grand_total' => 1.5,
                'status' => 'Lorem ipsum dolor sit amet',
                'created_at' => 1753185412,
                'modified_at' => 1753185412,
            ],
        ];
        parent::init();
    }
}
