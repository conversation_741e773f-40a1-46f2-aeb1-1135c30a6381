<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\ORM\Behavior\TimestampBehavior;
use Authentication\PasswordHasher\DefaultPasswordHasher;

class UsersTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('users');
        $this->setDisplayField('email');
        $this->setPrimaryKey('id');

        // For users who are partners themselves (user_type = 'Partner')
        $this->hasOne('PartnerProfile', [
            'className' => 'Partners',
            'foreignKey' => 'user_id',
            'conditions' => ['Users.user_type' => 'Partner']
        ]);

        // For users who belong to a partner (user_type = 'partner_user')
        $this->belongsTo('AssignedPartner', [
            'className' => 'Partners',
            'foreignKey' => 'partner_id',
            'joinType' => 'LEFT'
        ]);
    }

    public function findActive(Query $query, array $options)
    {
        return $query->where(['deleted_at IS' => null]); // Only fetch non-deleted users
    }
   
    /**
     * Partner creation validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationPartnerCreation(Validator $validator): Validator
    {
        $validator
            ->scalar('first_name')
            ->maxLength('first_name', 255)
            ->requirePresence('first_name', 'create')
            ->notEmptyString('first_name');

        $validator
            ->scalar('last_name')
            ->maxLength('last_name', 255)
            ->requirePresence('last_name', 'create')
            ->notEmptyString('last_name');

        $validator
            ->email('email')
            ->requirePresence('email', 'create')
            ->notEmptyString('email')
            ->add('email', 'uniquePartner', [
                'rule' => function ($value, $context) {
                    return !$this->exists([
                        'email' => $value,
                        'user_type' => 'Partner',
                        'status !=' => 'D'
                    ]);
                },
                'message' => 'This email is already registered as a partner'
            ]);

        $validator
            ->scalar('password')
            ->maxLength('password', 255)
            ->requirePresence('password', 'create')
            ->notEmptyString('password');

        $validator
            ->scalar('mobile')
            ->maxLength('mobile', 20)
            ->requirePresence('mobile', 'create')
            ->notEmptyString('mobile');

        $validator
            ->scalar('country_code')
            ->maxLength('country_code', 10)
            ->requirePresence('country_code', 'create')
            ->notEmptyString('country_code');

        $validator
            ->scalar('user_type')
            ->requirePresence('user_type', 'create')
            ->notEmptyString('user_type');

        $validator
            ->scalar('status')
            ->requirePresence('status', 'create')
            ->notEmptyString('status');

        return $validator;
    }

    /**
     * Profile update validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationProfileUpdate(Validator $validator): Validator
    {
        $validator
            ->scalar('first_name')
            ->maxLength('first_name', 100)
            ->requirePresence('first_name', 'update')
            ->notEmptyString('first_name', 'First name is required');

        $validator
            ->scalar('last_name')
            ->maxLength('last_name', 100)
            ->requirePresence('last_name', 'update')
            ->notEmptyString('last_name', 'Last name is required');

        $validator
            ->email('email')
            ->requirePresence('email', 'update')
            ->notEmptyString('email', 'Email is required')
            ->add('email', 'unique', [
                'rule' => function ($value, $context) {
                    $conditions = ['email' => $value];
                    if (!empty($context['data']['id'])) {
                        $conditions['id !='] = $context['data']['id'];
                    }
                    return !$this->exists($conditions);
                },
                'message' => 'This email is already registered'
            ]);

        $validator
            ->scalar('mobile')
            ->maxLength('mobile', 20)
            ->requirePresence('mobile', 'update')
            ->notEmptyString('mobile', 'Mobile number is required');

        $validator
            ->scalar('country_code')
            ->maxLength('country_code', 10)
            ->allowEmptyString('country_code');

        return $validator;
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    /* public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('first_name')
            ->maxLength('first_name', 100)
            ->requirePresence('first_name', 'create')
            ->notEmptyString('first_name');

        $validator
            ->scalar('last_name')
            ->maxLength('last_name', 100)
            ->requirePresence('last_name', 'create')
            ->notEmptyString('last_name');

        $validator
            ->scalar('country_code')
            ->maxLength('country_code', 10)
            ->requirePresence('country_code', 'create')
            ->notEmptyString('country_code');

        $validator
            ->scalar('mobile')
            ->maxLength('mobile', 20)
            ->requirePresence('mobile', 'create')
            ->notEmptyString('mobile');

        $validator
            ->scalar('profile_pic')
            ->maxLength('profile_pic', 255)
            ->allowEmptyString('profile_pic');

        $validator
            ->email('email')
            ->requirePresence('email', 'create')
            ->notEmptyString('email');

        $validator
            ->scalar('password')
            ->maxLength('password', 255)
            ->requirePresence('password', 'create')
            ->notEmptyString('password');

        $validator
            ->scalar('user_type')
            ->requirePresence('user_type', 'create')
            ->notEmptyString('user_type');

        $validator
            ->nonNegativeInteger('role_id')
            ->allowEmptyString('role_id');

        $validator
            ->scalar('social_provider')
            ->allowEmptyString('social_provider');

        $validator
            ->scalar('socialID')
            ->maxLength('socialID', 255)
            ->allowEmptyString('socialID');

        $validator
            ->scalar('otp_code')
            ->maxLength('otp_code', 10)
            ->allowEmptyString('otp_code');

        $validator
            ->dateTime('otp_expires_at')
            ->allowEmptyDateTime('otp_expires_at');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        $validator
            ->scalar('remember_token')
            ->maxLength('remember_token', 255)
            ->allowEmptyString('remember_token');

        $validator
            ->scalar('reset_token')
            ->maxLength('reset_token', 255)
            ->allowEmptyString('reset_token');

        $validator
            ->dateTime('reset_token_expiry')
            ->allowEmptyDateTime('reset_token_expiry');

        $validator
            ->integer('created_by')
            ->allowEmptyString('created_by');

        $validator
            ->integer('updated_by')
            ->allowEmptyString('updated_by');

        $validator
            ->dateTime('last_login_at')
            ->allowEmptyDateTime('last_login_at');

        $validator
            ->dateTime('created_at')
            ->notEmptyDateTime('created_at');

        $validator
            ->dateTime('modified_at')
            ->notEmptyDateTime('modified_at');

        $validator
            ->dateTime('deleted_at')
            ->allowEmptyDateTime('deleted_at');

        return $validator;
    } */

    /* public function validationChangePassword(Validator $validator): Validator
    {
        $validator
            ->requirePresence('current_password', 'create')
            ->notEmptyString('current_password', __('Current password is required.'));

        $validator
            ->requirePresence('new_password', 'create')
            ->notEmptyString('new_password', __('New password is required.'))
            ->add('new_password', 'passwordComplexity', [
                'rule' => function ($value) {
                    return (bool) preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*[\W_]).{8,}$/', $value);
                },
                'message' => __('Password must be at least 8 characters long and include an uppercase letter, lowercase letter, and a special character.')
            ]);

        $validator
            ->requirePresence('confirm_password', 'create')
            ->notEmptyString('confirm_password', __('Confirm password is required.'))
            ->add('confirm_password', 'match', [
                'rule' => ['compareWith', 'new_password'],
                'message' => __('Passwords do not match.')
            ]);

        return $validator;
    } */

    public function buildRules(RulesChecker $rules): RulesChecker
    {
        // $rules->add($rules->isUnique(['email']), ['errorField' => 'email']);

        return $rules;
    }

    public function beforeSave($event, $entity, $options)
    {
        if (!empty($entity->password) && $entity->isDirty('password')) {
            $entity->password = (new DefaultPasswordHasher())->hash($entity->password);
        }
    }
    
    public function findByEmail($email){
        $res = $this->find()
            ->where(['status !=' => 'D','user_type' => 'Customer', 'email' => $email])
            ->first();
        return $res;
    }
}














