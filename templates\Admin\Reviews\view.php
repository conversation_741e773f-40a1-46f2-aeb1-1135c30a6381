<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Review $review
 */
?>
<?php $this->start('style'); ?>
<style>
.rating-display i {
    font-size: 1.1rem;
}
</style>
<?php $this->end(); ?>

<div class="main-content bg-container">
    <div class="row">
        <div class="col-12 col-xl-12">
            <div class="card card-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <ul class="breadcrumb breadcrumb-style m-0">
                        <li class="breadcrumb-item">Marketing</li>
                        <li class="breadcrumb-item">
                            <a href="<?= $this->Url->build(['action' => 'index']) ?>">Reviews</a>
                        </li>
                        <li class="breadcrumb-item">View Review</li>
                    </ul>

                    <a href="javascript:void(0);" class="d-flex align-items-center category-back-button breadcrumb m-0" id="back-button-mo" onclick="history.back();">
                        <span class="rotate me-2">⤣</span>
                        <small class="fw-bold"><?= __("BACK") ?></small>
                    </a>
                </div>

                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <h4>Review Details </h4>
                        <!-- <div class="d-flex gap-2">
                            <a href="<?= $this->Url->build(['action' => 'edit', $review->id]) ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a href="<?= $this->Url->build(['action' => 'index']) ?>" class="btn btn-secondary btn-sm">
                                <i class="fas fa-list"></i> Back to List
                            </a>
                        </div> -->
                    </div>
                </div>
                <div class="card-body">
                    <!-- Status Alert -->
                    <?php
                    $alertClass = 'warning';
                    if ($review->status === 'Approved') $alertClass = 'success';
                    if ($review->status === 'Rejected') $alertClass = 'danger';
                    ?>
                    <div class="alert alert-<?= $alertClass ?> mb-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-<?= $review->status === 'Approved' ? 'check-circle' : ($review->status === 'Rejected' ? 'times-circle' : 'clock') ?> me-2"></i>
                            <div>
                                <strong>Status: <?= h($review->status) ?></strong>
                            </div>
                        </div>
                    </div>

                    <!-- Review Details -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Basic Information</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th width="40%">Review ID:</th>
                                            <td><?= $this->Number->format($review->id) ?></td>
                                        </tr>
                                        <tr>
                                            <th>Customer ID:</th>
                                            <td><?= $this->Number->format($review->customer_id) ?></td>
                                        </tr>
                                        <tr>
                                            <th>Review Type:</th>
                                            <td>
                                                <span class="badge bg-<?= $review->review_type === 'course' ? 'primary' : ($review->review_type === 'teacher' ? 'success' : 'info') ?>">
                                                    <?= ucfirst(h($review->review_type)) ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Reference ID:</th>
                                            <td><?= $this->Number->format($review->reference_id) ?></td>
                                        </tr>
                                        <tr>
                                            <th>Overall Rating:</th>
                                            <td>
                                                <div class="rating-display">
                                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                                        <?php if($i <= $review->overall_rating): ?>
                                                            <i class="fas fa-star text-warning"></i>
                                                        <?php else: ?>
                                                            <i class="far fa-star text-muted"></i>
                                                        <?php endif; ?>
                                                    <?php endfor; ?>
                                                    <span class="ms-2 fw-bold"><?= number_format($review->overall_rating,0) ?>/5</span>
                                                </div>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-calendar"></i> Additional Details</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th width="40%">Booking ID:</th>
                                            <td><?= $review->booking_id ? $this->Number->format($review->booking_id) : '<em class="text-muted">Not specified</em>' ?></td>
                                        </tr>
                                        <tr>
                                            <th>Booking Item ID:</th>
                                            <td><?= $review->booking_item_id ? $this->Number->format($review->booking_item_id) : '<em class="text-muted">Not specified</em>' ?></td>
                                        </tr>
                                        <tr>
                                            <th>Status:</th>
                                            <td>
                                                <?php
                                                $badgeClass = 'bg-warning';
                                                if ($review->status === 'Approved') $badgeClass = 'bg-success';
                                                if ($review->status === 'Rejected') $badgeClass = 'bg-danger';
                                                ?>
                                                <span class="badge <?= $badgeClass ?>"><?= h($review->status) ?></span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Created:</th>
                                            <td><?= h($review->created_at->format('M d, Y H:i:s')) ?></td>
                                        </tr>
                                        <tr>
                                            <th>Updated:</th>
                                            <td><?= h($review->updated_at->format('M d, Y H:i:s')) ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Review Comment -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-comment"></i> Review Comment</h6>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($review->comment)): ?>
                                <div class="review-comment-box p-3 bg-light rounded">
                                    <blockquote class="blockquote mb-0">
                                        <p class="mb-0"><?= nl2br(h($review->comment)) ?></p>
                                    </blockquote>
                                </div>
                            <?php else: ?>
                                <div class="text-muted text-center py-3">
                                    <i class="fas fa-info-circle"></i> No comment provided for this review.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-question-circle"></i> Review Answers</h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($review->review_answers)): ?>
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Question</th>
                                        <th>Rating</th>
                                       
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($review->review_answers as $answer): ?>
                                        <tr>
                                            <td><?= h($answer->review_question->question ?? '') ?></td>
                                            <td>

                                             <div class="rating-display">
                                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                                        <?php if($i <= $answer->rating): ?>
                                                            <i class="fas fa-star text-warning"></i>
                                                        <?php else: ?>
                                                            <i class="far fa-star text-muted"></i>
                                                        <?php endif; ?>
                                                    <?php endfor; ?>
                                                    <span class="ms-2 fw-bold"><?= $answer->rating ?>/5</span>
                                                </div>
                                                <!-- <?= $answer->rating !== null ? $answer->rating : '<em class="text-muted">N/A</em>' ?></td> -->
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php else: ?>
                            <div class="text-muted">No answers for this review.</div>
                        <?php endif; ?>
                    </div>
                </div>
                </div>
            </div>
        </div>
    </div>
</div>


