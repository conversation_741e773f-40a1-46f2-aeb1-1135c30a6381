<?php
// $this->extend('/layout/profile_layout');
$this->assign('title', $title);
$this->assign('meta_desc', 'Your saved yoga courses and favorite programs');
$this->assign('keywords', 'bookimarks, favorites, saved courses, yoga');
$this->assign('activeMenuItem', 'wishlist');
?>
<script src="<?= $this->Url->webroot('js/course.js') ?>"></script>
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');


    .group-booking .page-title {
        color: #293148;
        font-size: 24px;
        font-weight: 700;
        text-align: center;
        font-family: "Open Sans", sans-serif;
    }

    .group-booking .cards .art {
        font-family: "Poppins", sans-serif;
        font-size: 10px;
        font-weight: 600;
    }

    .group-booking .cards .card-title {
        font-weight: 700;
        color: #293148;
        font-size: 14px;
        text-decoration: underline;
    }

    .group-booking .cards {
        border-top: 1px solid #D87A61 !important;
        border: 0px;
    }

    .group-booking .cards .date-content .date {
        font-size: 12px;
        font-style: italic;
        font-family: "Open Sans", sans-serif;
        font-weight: 600;
        color: #000000;
    }

    .group-booking .cards .upcoming {
        color: #BF0A30;
        font-size: 10px;
        font-style: italic;
        font-weight: 700;
        font-family: "Open Sans", sans-serif;
    }

    .group-booking .cards .card-button .enroll {
        border-bottom-left-radius: 10px !important;
        border-radius: 0px;
        font-size: 12px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
    }

    .group-booking .cards .card-button .remove {
        border-bottom-right-radius: 10px !important;
        border-radius: 0px;
        font-size: 12px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
    }

    @media only screen and (max-width: 700px) {}

    @media only screen and (max-width: 390px) {}
</style>

 <section class="group-booking grp-booking-body-content">

        <div class="mx-auto p-4">
            <h1 class="page-title text-2xl font-bold text-center mb-4">My Bookmarks</h1>
            <div class="flex justify-center mb-6">
                <!-- <div class="flex justify-center mb-6">
                    <div class="inline-flex bg-[#FEF0EA] p-1 rounded-full space-x-2">
                        <?php
                        $filters = [
                            'all' => 'All',
                            'course' => 'Courses',
                            'class' => 'Classes',
                            'center' => 'Partners',
                            'teacher' => 'Teachers',
                        ];
                        foreach ($filters as $key => $label):
                            $active = $key === $type;
                        ?>
                        <a href="<?= $this->Url->build([
                            'controller' => 'Profile',
                            'action' => 'wishlist',
                            '?' => ['type' => $key]
                        ]) ?>"
                            class="px-4 py-2 rounded-full text-sm font-semibold <?= $active ? 'bg-[#D87A61] text-white' : 'text-black' ?>">
                                <?= h($label) ?>
                                <?php if (!empty($typeCounts[$key])): ?>
                                    (<?= h($typeCounts[$key]) ?>)
                                <?php endif; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div> -->
              </div>
            <?php foreach ($wishlistData as $item): ?>
                <div class="cards border rounded-lg mb-2 py-4">
                    <div class="flex px-4">
                        <img src="<?= h($item['image_url'] ?? 'https://media-cdn.tripadvisor.com/media/photo-s/0e/77/5a/1a/firecamp.jpg') ?>"
                            alt="Course Image" class="w-20 h-20 rounded-lg object-cover mr-4" />
                        <div>
                            <p class="text-sm text-[#D87A61] font-semibold art">@<?=h($item['partner']['name'] ?? '') ?></p>
                            <h3 class="font-bold card-title text-lg text-[#293148]"><?= h($item['name']) ?></h3>
                        </div>
                    </div>
                    <div class="flex items-center text-sm text-gray-600 mt-1 date-content px-4 py-2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M5.33398 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10"
                            stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M10.666 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path opacity="0.4" d="M2.33398 6.05957H13.6673" stroke="#C45F44" stroke-miterlimit="10"
                            stroke-linecap="round" stroke-linejoin="round" />
                        <path
                            d="M14.6673 12.6667C14.6673 13.1667 14.5273 13.64 14.2807 14.04C13.8207 14.8133 12.974 15.3333 12.0007 15.3333C11.3273 15.3333 10.714 15.0867 10.2473 14.6667C10.0407 14.4933 9.86065 14.28 9.72065 14.04C9.47399 13.64 9.33398 13.1667 9.33398 12.6667C9.33398 11.1933 10.5273 10 12.0007 10C12.8007 10 13.514 10.3533 14.0007 10.9067C14.414 11.38 14.6673 11.9933 14.6673 12.6667Z"
                            stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M10.9609 12.667L11.6209 13.327L13.0409 12.0137" stroke="#C45F44" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path
                            d="M14 5.66634V10.9063C13.5133 10.353 12.8 9.99967 12 9.99967C10.5267 9.99967 9.33333 11.193 9.33333 12.6663C9.33333 13.1663 9.47333 13.6397 9.72 14.0397C9.86 14.2797 10.04 14.493 10.2467 14.6663H5.33333C3 14.6663 2 13.333 2 11.333V5.66634C2 3.66634 3 2.33301 5.33333 2.33301H10.6667C13 2.33301 14 3.66634 14 5.66634Z"
                            stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                        <path opacity="0.4" d="M7.99764 9.13314H8.00363" stroke="#C45F44" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path opacity="0.4" d="M5.52889 9.13314H5.53488" stroke="#C45F44" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path opacity="0.4" d="M5.5293 11.1338H5.53528" stroke="#C45F44" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>
                        <span class="date pl-2">
                            <?php if (!empty($item['course_batches'])): ?>
                                <?= h($item['course_batches'][0]['start_date']->format('d M Y')) ?> - <?= h($item['course_batches'][0]['end_date']->format('d M Y')) ?>
                            <?php endif; ?>
                        </span>
                        <span class="ml-4 flex items-center date">
                            <!-- Language SVG -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path
                                d="M8.00065 14.6663C11.6825 14.6663 14.6673 11.6816 14.6673 7.99967C14.6673 4.31778 11.6825 1.33301 8.00065 1.33301C4.31875 1.33301 1.33398 4.31778 1.33398 7.99967C1.33398 11.6816 4.31875 14.6663 8.00065 14.6663Z"
                                stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                            <g opacity="0.4">
                                <path d="M5.33372 2H6.00039C4.70039 5.89333 4.70039 10.1067 6.00039 14H5.33372"
                                    stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M10 2C11.3 5.89333 11.3 10.1067 10 14" stroke="#C45F44" stroke-linecap="round"
                                    stroke-linejoin="round" />
                                <path d="M2 10.6667V10C5.89333 11.3 10.1067 11.3 14 10V10.6667" stroke="#C45F44"
                                    stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M2 6.00039C5.89333 4.70039 10.1067 4.70039 14 6.00039" stroke="#C45F44"
                                    stroke-linecap="round" stroke-linejoin="round" />
                            </g>
                        </svg>
                            <span class="pl-2"><?= h($item['language']) ?></span>
                        </span>
                    </div>
                    <p class="text-sm text-red-500 upcoming px-4">
                        Upcoming Dates:
                        <?php
                        if (!empty($item['course_batches'])) {
                            $dates = array_map(function($batch) {
                                return h($batch['start_date']->format('j M'));
                            }, array_slice($item['course_batches'], 0, 3));
                            echo implode(' | ', $dates);
                            if (count($item['course_batches']) > 3) {
                                echo ' | <a href="#" class="underline">View All</a>';
                            }
                        } else {
                            echo 'No upcoming dates';
                        }
                        ?>
                    </p>
                     <!-- <div class="grid grid-cols-2 mt-4 gap-0 card-button">
                     <a href="#" class="course-link" data-item='<?= json_encode($item) ?>'>View</a>
                    <button class="bg-[#D87A61] text-[#000000] py-2 rounded enroll">View</button>
                    <button class="bg-[#ECCEC4] text-[#000000] py-2 rounded remove delete-bookmark-btn" data-bookmark-id="<?= h($item['bookmark_id']) ?>">Remove</button>
                  
                    </div> -->
                     <div class="grid grid-cols-2 mt-4 gap-0 card-button">
                     <a href="#" class="course-link bg-[#D87A61] text-[#000000] py-2 rounded enroll text-center" data-item='<?= json_encode($item) ?>'>
                      View</a>
                    <button class="bg-[#ECCEC4] text-[#000000] py-2 rounded remove delete-bookmark-btn" data-bookmark-id="<?= h($item['bookmark_id']) ?>">Remove</button>

                    </div>
                   
                </div>
            <?php endforeach; ?>
        </div>

</section>
<?php $this->Html->script('https://cdn.jsdelivr.net/npm/sweetalert2@11', ['block' => true]); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.delete-bookmark-btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const bookmarkId = this.getAttribute('data-bookmark-id');
            const card = this.closest('.cards');
            Swal.fire({
                title: 'Are you sure?',
                text: 'Do you want to remove this bookmark?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Yes, remove it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch('<?= $this->Url->build(["controller" => "Profile", "action" => "deleteBookmark"]) ?>', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-Token': <?= json_encode($this->request->getAttribute('csrfToken')) ?>
                        },
                        body: JSON.stringify({ bookmark_id: bookmarkId })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire('Deleted!', data.message, 'success');
                            if (card) card.remove();
                        } else {
                            Swal.fire('Error', data.message, 'error');
                        }
                    })
                    .catch(() => {
                        Swal.fire('Error', 'Something went wrong.', 'error');
                    });
                }
            });
        });
    });
});
</script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.course-link').forEach(link => {
            const item = JSON.parse(link.dataset.item);
            const url = getCourseUrl(item); // This must be defined in a <script> tag
            link.href = url;
        });
    });

    function getCourseUrl(item) {
        const baseUrl = '/';
        const lang = 'en';

        const modalityNames = (item.modalities || []).map(m => m.name);
        const hasOnlineVOD = modalityNames.includes('Online VOD');
        const hasOnlineLive = modalityNames.includes('Online Live');
        const hasOnSite = modalityNames.includes('On Site');
        const hasHybrid = modalityNames.includes('Hybrid');

        if (hasOnlineVOD && !hasOnlineLive && !hasOnSite && !hasHybrid) {
            return `${baseUrl}${lang}/yoga-courses/video/${item.slug}`;
        } else if (hasOnlineLive && !hasOnlineVOD && !hasOnSite && !hasHybrid) {
            return `${baseUrl}${lang}/yoga-courses/online/${item.slug}`;
        } else {
            return getLocationBasedUrl(item, baseUrl, lang);
        }
    }

    function getLocationBasedUrl(item, baseUrl, lang) {
        const parts = [
            item.country?.name,
            item.region_name,
            item.state?.name,
            item.city?.name,
            item.locality?.name,
            item.slug
        ].filter(Boolean).map(part => part.toLowerCase().replace(/\s+/g, '-'));

        return `${baseUrl}${lang}/yoga-courses/${parts.join('/')}`;
    }
</script>