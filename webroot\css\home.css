/* Home page styles */
@font-face {
    font-family: 'cakefont';
    src: url('../font/cakedingbats-webfont.eot');
    src: url('../font/cakedingbats-webfont.eot?#iefix') format('embedded-opentype'),
        url('../font/cakedingbats-webfont.woff2') format('woff2'),
        url('../font/cakedingbats-webfont.woff') format('woff'),
        url('../font/cakedingbats-webfont.ttf') format('truetype'),
        url('../font/cakedingbats-webfont.svg#cake_dingbatsregular') format('svg');
    font-weight: normal;
    font-style: normal;
}

@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap');

body {
    max-width: 1920px;
    margin: 0 auto;
    padding: 0;
}

/* body {
    padding: 60px 0;
}
header {
    margin-bottom: 60px;
}
img {
    margin-bottom: 30px;
} */
h1 {
    font-weight: bold;
}

ul {
    list-style-type: none;
    /* margin: 0 0 30px 0;
    padding-left: 25px; */
}

a {
    color: #000;
    text-decoration: none;
}

hr {
    border-bottom: 1px solid #e7e7e7;
    border-top: 0;
    margin-bottom: 35px;
}

.text-center {
    text-align: center;
}

.links a {
    margin-right: 10px;
}

.release-name {
    color: #D33C43;
    font-weight: 400;
    font-style: italic;
}

.bullet:before {
    font-family: 'cakefont', sans-serif;
    font-size: 18px;
    display: inline-block;
    margin-left: -1.3em;
    width: 1.2em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    vertical-align: -1px;
}

.success:before {
    color: #88c671;
    content: "\0056";
}

.problem:before {
    color: #d33d44;
    content: "\0057";
}

.cake-error {
    padding: 10px;
    margin: 10px 0;
}

#url-rewriting-warning {
    display: none;
}

.nav-bar-menu .nav-gap {
    gap: 29px;
}

/* Header style */
/* .header-top{
    background-image: url('../img/mobile-leaf.png');
    background-repeat: no-repeat;
    background-position: 100% 95%;
    background-size: 47%;
} */
/* .header-top header{
    padding-bottom: 58px;
} */
[x-cloak] {
    display: none !important;
}

header nav {
    padding-top: 18px;
    margin-bottom: 17px;
    justify-content: space-between;
}

header nav img.logo {
    width: 55px;
    height: 53px;
}

header .header-content img.yoga-leaf {
    position: absolute;
    top: 205px;
    right: 0px;
    width: calc(100% - 54%);
}

a.dropdown::after {
    content: '';
    border: 2px solid #7E8C92;
    border-top: transparent;
    border-left: transparent;
    width: 8px;
    height: 8px;
    display: inline-block;
    transform: rotate(45deg);
    position: relative;
    bottom: 2px;
    left: 2px;
}

a.dropdown:hover {
    color: #000 !important;
}

.header-content {
    width: 100%;
}

.header-content h1 {
    font-size: 30px;
    line-height: normal;
    font-weight: 800;
    letter-spacing: 0px;
    color: #222222;
    margin-bottom: 5px;
    font-family: "Afacad", sans-serif;
}

.search-container h2 {
    font-size: 16px;
    line-height: normal;
    font-weight: 700;
    font-family: "DM Sans", sans-serif;
    letter-spacing: 0px;
    color: #333333;
    margin-bottom: 15px;
}

.header-content p {
    font-size: 18px;
    line-height: normal;
    font-weight: 500;
    letter-spacing: 0px;
    color: #888888;
    /* width: 345px; */
    font-family: "DM Sans", sans-serif;
}

.header-img img {
    /* width: 960px; */
    position: relative;
    right: -15px;
    /* height: auto; */
}

.overlay{
    background-color: rgba(0,0,0,.4);
    /* top:75px; */
    width: 100%;
    left: 0;
    height: 100%;
    z-index: 9;
}
.mobile-menus-container {
    z-index: 9;
    position: fixed;
    left: 0;
    top: 75px;
    width: 80%;
    height: 100%;
    /* overflow-y: scroll; */
    background-color: #fff;
}

.mobile-menus .sub-menu-items a {
    /* background: #EFEFEF; */
    /* border-bottom: 1px solid #eaeaec; */
    border-radius: 0px;
    margin: 5px;
    color: #6f6f6f;
}
.mobile-menus ul li a.dropdown{
    font-size: 16px;
    line-height: normal;
    font-family: 'Open Sans', sans-serif;
    font-weight: 400;
}
.mobile-menus ul li.partner-list a.dropdown{
    font-size: 16px;
    line-height: normal;
    font-family: 'Open Sans', sans-serif;
    font-weight: 400;
    color: #fff;
    display: flex;
    align-items: center;
}
.mobile-menus ul li.partner-list a.dropdown:hover{
    color: #fff !important;
}
.mobile-menus ul li.partner-list a.dropdown:active{
    color: #fff !important;
}
.menus {
    display: none;
}

.menus ul li .dropdown-submenu a:hover {
    background-color: #D1E1D3;
    color: #000;
}

.centers-ashrams .btn-center a {
    color: #D87A61;
    background: none;
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
    text-transform: uppercase;
}

.menus ul li a.dropdown:hover {
    color: #D87A61 !important;
    /* font-weight: 500; */
}

.mobile-menus {
    position: absolute;
    background-color: #fff;
    /* min-height: 25vh; */
    left: 0;
    /* top: 120px; */
    width: 100%;
    padding-left: 200px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    padding-right: 20px;
    z-index: 1;
}

.mobile-menus>ul {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    padding-left: 0px;
}

.mobile-menus>ul li {
    width: 100%;
}

.mobile-menus>ul li>div>a {
    display: block;
    width: 100%;
    font-weight: 500;
    border-bottom: 1px solid #eaeaec;
    border: 0px;
}

.mobile-menus>ul li>div>a.dropdown:after {
    position: absolute;
    left: auto;
    bottom: 15px;
    right: 9px;
}

.partner-list {
    display: block;
}

.partner-list button,
a {
    border-radius: 5px;
    display: flex;
    align-items: center;
}

/* .partner{
    margin-left: 50px;
    display: none;
} */
.mobile-menu span {
    display: block;
    width: 29px;
    height: 19px;
}

.mobile-menu span i {
    font-size: 29px;
    color: #D87A61;
}
.mobile-menus .partner-list .dropdown-submenu a {
    border-radius: 5px;
    background-color: transparent;
    color: #283148;
    font-size: 16px;
    line-height: normal;
    font-family: 'Open Sans', sans-serif;
    font-weight: 400;
}
.mobile-menus .partner-list .dropdown-submenu p.account-title {
    background-color: transparent;
    color: #d87a61;
    font-size: 16px;
    line-height: normal;
    font-family: 'Open Sans', sans-serif;
    font-weight: 500;
    margin-bottom: 0;
}
.mobile-menus .partner-list .login a.dropdown{
    display: flex;
    align-items: center;
}
.mobile-menus .partner-list .init-letter{
    background-color: #fff;
    color: #d87a61;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 13px;
    font-weight: 600;
    font-family: 'Open Sans', sans-serif;
}
.mobile-menus .partner-list .customer-name{
    overflow: hidden;
    text-overflow: ellipsis;
    text-wrap: nowrap;
    width: 108px;
}
.mobile-menus .partner-list .login .dropdown-submenu ul li a.text-logout{
    font-weight: 600;
}
.mobile-menus .partner-list .dropdown-submenu ul {
    padding-left: 0;
    padding-top: 0;
}
.mobile-menus .partner-list .dropdown:after {
    position: absolute;
    left: auto;
    bottom: 15px;
    right: 9px;
    border: 2px solid #fff;
    border-top: transparent;
    border-left: transparent;
}
.mobile-menus .partner-list .login{
    display: none;
}

/* Search section */
.search-container {
    /* padding-top: 15px; */
    /* padding-bottom: 29px; */
    /* margin-bottom: 30px; */
    /* height: 354px; */
    /* border-radius: 12px; */
    background-color: rgba(226, 149, 45, .2);
    /* margin-top: 58px; */
}

.search-container p {
    font-size: 16px;
    line-height: normal;
    color: #333333;
    font-weight: 700;
    letter-spacing: 0px;
    font-family: "Open Sans", sans-serif;
}

.search-wrapper {
    width: 100%;
    border-radius: 8px;
    background-color: #fff;
    padding: 20px;
    position: relative;
    height: auto;
    display: block;
}

.search-location {
    width: 100%;
}

.search-location select {
    width: 100%;
    border-bottom: 1px solid #CCCCCC;
    padding: 15px 0px;
    color: #666666;
    font-family: "DM Sans", sans-serif;
    font-size: 14px;
    font-weight: 400;
}

.search-location .dropdown {
    color: #666666;
    display: block;
    width: 100%;
    padding-top: 0 !important;
}

.search-location .dropdown:after {
    position: absolute;
    bottom: 15px;
    left: auto;
    right: 0;
}

.search-style {

    /* padding: 0px 0px 0px 20px; */

    /* display: flex; */
    width: 100%;
}

.search-style .dropdown-container {
    width: 100%;
    margin-right: 0px;
    color: #666666;
}

.search-style .special-need {
    width: 119px;
    margin-right: 0px;
    color: #666666;
}

/* .search-style .dropdown-container .dropdown{
    color: #D87A61;
} */
.search-style .btn-toggle {
    width: 100%;
    padding: 15px 0px;
    font-family: "DM Sans", sans-serif;
    font-size: 14px;
    font-weight: 400;
}

.search-style>div {
    width: 100%;
    justify-content: space-between;
    border-bottom: 1px solid #CCCCCC;
    font-family: "DM Sans", sans-serif;
    font-size: 14px;
    font-weight: 400;
}

.search-style .btn-toggle label {
    display: flex !important;
    justify-content: space-between;
    font-family: "DM Sans", sans-serif;
    font-size: 14px;
    font-weight: 400;
}

.search-style .btn-toggle label span {
    /* margin-left: 30px ; */
}

.search-style select {
    color: #aaa;
}

.search-style .yoga-option {
    height: 400px;
    overflow-y: auto;
}

.search-place {
    padding: 15px 0px;
    font-family: "DM Sans", sans-serif;
    font-size: 14px;
    font-weight: 400;
    border-bottom: 1px solid #CCCCCC;
    /* width: 300px; */
}

.search-place>input {
    width: 100%;
    font-family: "DM Sans", sans-serif;
    font-size: 14px;
    font-weight: 400;
    padding: 0px 4px;
}

.search-all {
    position: relative;
    right: 0px;
    /* margin-right: 23px; */
}

.search-all .btn-search {
    background-color: #D87A61;
    border-radius: 7px;
    width: 100%;
    height: 50px;
    color: #fff;
    font-size: 16px;
    line-height: normal;
    letter-spacing: 3%;
    font-weight: 500;
    margin-top: 26px;
}

.search-all .btn-search .fas {
    /* font-weight: 400 !important; */
}

.search-all .btn-search>img {
    display: none;
}

.search-all .btn-search>img>span {
    display: block;
}

#popular-slider {
    margin: auto;
    text-align: center;
}

/* Featured yoga section */
.featured-yoga {
    position: relative;
    /* padding-bottom: 0 !important; */
}

.featured-yoga h2 {
    font-size: 25px;
    line-height: normal;
    font-weight: 700;
    letter-spacing: -0.5px;
    font-family: "Afacad", sans-serif;
    border-left: 4px solid #D87A61;
    padding-left: 10px;
    padding-right: 0px;
    margin-bottom: 30px;
    color: #283148;
    /* width: 230px; */
}

/* .featured-yoga p {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: -0.5px;
    font-family: "Open Sans", sans-serif;
    color: #6A6A6A;
} */

/* .featured-yoga .card-container {
    box-shadow: 2px 5px 14px rgba(0, 0, 0, 0.25) !important;
} */

/* .featured-yoga .card-body p.info {
    font-size: 14px;
    line-height: 20px;
    font-weight: 600;
    letter-spacing: 0px;
    color: #D87A61;
    font-family: "Poppins", sans-serif;
    margin-bottom: 9px;
} */

/* Style for star rating */
.star {
    font-size: x-large;
    width: 18px;
    display: inline-block;
    color: #E0E8F1;
}

.star:last-child {
    margin-right: 0;
}

.star:before {
    content: '\2605';
}

.star.on {
    color: #FFC500;
}

.star.half:after {
    content: '\2605';
    color: #FFC500;
    position: absolute;
    margin-left: 0px;
    margin-top: -36px;
    width: 10px;
    overflow: hidden;
}

/* Ends here */
/* .card-body {
    padding-left: 21px;
    padding-right: 20px;
    padding-top: 20px;
    padding-bottom: 25px;
    background: #fff;
    border-radius: 10px;
} */

/* .card-body h3 {
    font-size: 17px;
    line-height: normal;
    letter-spacing: 0px;
    font-family: "Poppins", sans-serif;
    font-weight: 500;
    color: #151515;
    position: relative;
    margin-bottom: 0px;
} */

/* .card-body h3 .rating {
    background-color: #D87A61;
    padding: 6px 9px;
    border-radius: 3px;
    height: 20px;
    display: inline-block;
    /* width: 39px; */
/*    font-size: 14px;
    line-height: 11px;
    font-weight: 400;
    font-family: "Poppins", sans-serif;
    color: #fff;
    margin-top: 5px;
    margin-left: 10px;
} */

.card-body h3 i {
    font-size: 15px;
    color: #FFC500;
    /* position: absolute;
    left: auto;
    right: 20px;
    top: 0px; */
    margin-top: 5px;
}

header nav+div {
    padding-bottom: 30px !important;
}

/* .featured-yoga .card-body p {
    font-size: 14px;
    line-height: normal;
    font-weight: 400;
    letter-spacing: 0px;
    color: #1A1A1A;
    font-family: "Poppins", sans-serif;
    margin-bottom: 22px;
} */

/* .featured-yoga p.time {
    color: #1C1B1F;
    font-size: 16px;
    line-height: 22px;
    font-weight: 600;
    font-family: "DM Sans", sans-serif;
    font-optical-sizing: auto;
    letter-spacing: -0.5px;
} */

/* .featured-yoga p.mode {
    color: #1C1B1F;
    font-size: 16px;
    line-height: 22px;
    font-weight: 600;
    font-family: "Open Sans", sans-serif;
    font-optical-sizing: auto;
    letter-spacing: -0.5px;
} */

/* .courses-view {
    display: none;
} */

/* .featured-yoga p.lang {
    color: #1C1B1F;
    font-size: 16px;
    line-height: 22px;
    font-weight: 600;
    font-family: "Open Sans", sans-serif;
    font-optical-sizing: auto;
    letter-spacing: -0.5px;
} */

/* .time i {
    color: #D87A61;
    width: 18px;
    margin-right: 10px;
} */

/* .mode i {
    color: #D87A61;
    width: 18px;
    margin-right: 10px;
} */

/* .lang i {
    color: #D87A61;
    width: 18px;
    margin-right: 10px;
} */

#center-logo-slider .logo-img {
    border-radius: 10px !important;
}

.courses-view>button {
    font-size: 20px;
    line-height: normal;
    font-weight: 700;
    font-family: "Open Sans", sans-serif;
    letter-spacing: -0.5px;
    text-transform: uppercase !important;
}

.featured-slider .indicator {
    z-index: 9999;
    bottom: -45px;
    margin-top: 29px;
    width: 100px;
    opacity: 1;
}

.featured-slider .indicator button {
    border-radius: 50%;
}
.featured-yoga .courses-view>button:hover {
    box-shadow: 0px 7px 12px -7px #000;
    border: 1px solid #000;
    color: #c3644a;
    border-radius: 5px;
}

/* Most popular yoga destination styles */
.yoga-destination {
    margin-top: 20px;
    background-image: url("../img/destination-background.png");
    background-repeat: no-repeat;
    background-size: cover;
    padding-top: 20px;
    padding-bottom: 20px;
}

.yoga-destination h2 {
    margin-bottom: 20px;
    font-size: 25px;
    line-height: normal;
    font-weight: 700;
    letter-spacing: 0px;
    font-family: "Afacad", sans-serif;
    color: #333333;
    width: 220px;
    /* padding-right: 197px; */
    border-left: 5px solid #D87A61;
    padding-left: 20px
}

.yoga-destination button.prev {
    right: 30px;
    top: 0;
    padding: 0 5px !important;
    /* display: none */
}

.yoga-destination button.next {
    right: 0px;
    top: 0;
    padding: 0 5px !important;
    /* display: none */
}

.yoga-destination .card-body {
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 0;
    background: none;
}

.yoga-destination .courses-view {
    margin-top: 6px;
}

.yoga-destination #popular-slider .card-img {
    border-radius: 10px;
}

.yoga-destination #popular-slider .card-img img {
    border-radius: 18px;
}

.yoga-destination .card-img .overlay {
    background: linear-gradient(0deg, #000000 25%, #D9D9D900 100%);
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 37px 0 20px;
    color: #fff;
    text-align: center;
    font-size: 28px;
    line-height: normal;
    font-weight: 600;
    letter-spacing: -0.5px;
    border-bottom-left-radius: 18px;
    border-bottom-right-radius: 18px;
}

.yoga-destination .card-body p {
    font-size: 16px;
    line-height: 20px;
    font-weight: 400;
    color: #6A6A6A;
    letter-spacing: -0.5px;
    font-family: "Open Sans", sans-serif;
    margin-bottom: 11px;
}

.yoga-destination .card-body button {
    font-size: 14px;
    line-height: 12px;
    font-weight: 700;
    font-family: "Open Sans", sans-serif;
    text-transform: uppercase !important;
}

/* .yoga-destination .destination-view {
    display: none;
} */

.yoga-destination .destination-view button {
    font-size: 20px;
    line-height: normal;
    font-weight: 700;
    font-family: "Open Sans", sans-serif;
    text-transform: uppercase !important;
    letter-spacing: -0.5px;
}
.yoga-destination .destination-view button:hover {
    box-shadow: 0px 7px 12px -7px #000;
    border: 1px solid #000;
    color: #c3644a;
    border-radius: 5px;
}

/* centers and ashrams style */
.centers-ashrams {
    background-color: #EAE0C7;
    padding-top: 20px;
    padding-bottom: 20px;
}

.centers-ashrams h2 {
    font-size: 25px;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 0px;
    margin-bottom: 20px;
    font-family: "Afacad", sans-serif;
    font-optical-sizing: auto;
    color: #333333;
    border-left: 5px solid #D87A61;
    padding-left: 20px;
    /* padding-right: 110px; */
}

.logo-div-img {
    padding: 0px;
    margin: auto;
    text-align: center;
    width: 81px;
    height: 81px;
}

.footer .social-media {
    margin: 20px 0px 30px !important;
}

.footer-social-icons-content .footer-title-partner {
    margin-bottom: 15px !important;
}

.footer-social-icons-content li {
    margin-bottom: 0px !important
}

.logo-div-img .logo-img {
    background-size: cover;
}

.centers-ashrams .r-img {
    margin-left: 0px;
    position: relative;
    left: 15px;
}

.centers-ashrams .logo-content {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.centers-ashrams .logo-content img {
    margin-right: 20px;
    width: 89px;
    height: 89px;
}

/* .centers-ashrams .r-img .ashram-img{
    position: relative;
    top: 0;
    left: 32%;
    z-index: 1111;
} */
.centers-ashrams .r-img #default-carousel1 {
    bottom: 85px;
}

.centers-ashrams .r-img #default-carousel1 .slide-buttons button {
    border-radius: 50% !important;
    margin-top: 29px;
}

.centers-ashrams .r-img #default-carousel1 .slide-buttons button[aria-current="true"] {
    background-color: #D87A61;
}

.centers-ashrams .r-img #default-carousel1 .slide-buttons button[aria-current="false"] {
    background-color: #D87A61;
    opacity: .4;
}

.centers-ashrams .yoga-center p {
    font-size: 14px;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0;
    font-family: "Poppins", sans-serif;
    color: #1A1A1A;
}

.heading {
    font-size: 22px;
    line-height: normal;
    font-weight: 500;
    letter-spacing: -0.5px;
    margin-top: 0px;
    margin-bottom: 0px;
    font-family: "Afacad", sans-serif;
    color: #293148;
}

.centers-ashrams .content {
    font-size: 14px;
    line-height: 21px;
    font-weight: 400;
    font-family: "Poppins", sans-serif;
    letter-spacing: -0.5px;
    color: #1A1A1A;
    margin-bottom: 0;
    margin-top: 60px;
}

.centers-ashrams .btn-center {
    text-align: center;
}

.ashram-slider {
    bottom: 0px;
    margin-bottom: 20px;
}

.ashram-slider .prev {
    display: block;
    width: 40px;
    height: 40px;
    background: #ffffff;
    opacity: .9;
    border-radius: 50%;
}

.ashram-slider .next {
    display: block;
    width: 40px;
    height: 40px;
    background: #ffffff;
    opacity: .9;
    border-radius: 50%;
}

.ashram-slider .indicator {
    z-index: 9999;
    bottom: -45px;
    margin-top: 29px;
    width: 100px;
    opacity: 1;
    display: none;
}

.ashram-slider .indicator button {
    border-radius: 50%;
}

hr.divider {
    height: 3px !important;
    margin-top: 33px;
    margin-bottom: 40px;
    display: none;
}

.center-logo figcaption {
    font-size: 30px;
    font-weight: 500;
    line-height: normal;
    text-align: left;
    letter-spacing: -0.5px;
    color: #293148;
    font-family: "Afacad", sans-serif;
}

/* Featured Teachers style */
.featured-teachers {
    background-image: url("../img/flower.png");
    background-repeat: no-repeat;
    background-position: 103% -125%;
    background-size: 50%;
}

.featured-teachers h2 {
    font-size: 25px;
    line-height: normal;
    font-weight: 700;
    letter-spacing: 0px;
    /* margin-top: 32px; */
    padding-top: 0px;
    padding-bottom: 0px;
    margin-bottom: 20px;
    font-family: "Afacad", sans-serif;
    border-left: 5px solid #D87A61;
    padding-left: 20px;
}

/* .featured-teachers .carousel{
    padding: 0 14px;
}
.featured-teachers .carousel:first-child{
    padding-right:15px;
}
.featured-teachers .carousel:last-child{
    padding-left:15px;
}
.featured-teachers .carousel .teacher-name{
    font-size: 30px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: -0.5px;
    margin-bottom: 0;
    font-family: "Afacad", sans-serif;
}
.featured-teachers .carousel .professtion{
    font-size: 18px;
    font-weight: 400;
    line-height: 23px;
    letter-spacing: -0.5px;
    font-family: "Open Sans", sans-serif;
    color:#666666;
    margin-top: 10px;
    margin-bottom: 17px;
}
.featured-teachers .carousel .btn-view{
    font-size: 14px;
    font-weight: 700;
    line-height: normal;
    letter-spacing: -0.5px;
    font-family: "Open Sans", sans-serif;
    text-transform: uppercase;
}
.featured-teachers #indicators-carousel .btn-indicate{
    left: 50%;
    margin-left: -50px;
    margin-top: 22px;
}
.featured-teachers #indicators-carousel .btn-indicate button[aria-current="true"]{
    background-color: #A3C4A9;
}
.featured-teachers #indicators-carousel .btn-indicate button[aria-current="false"]{
    background-color: #A3C4A9;
    opacity: .4;
}
.featured-teachers #indicators-carousel button.rounded-full{
    border-radius: 50% !important;
} */
.teacher-container {
    overflow: hidden;
    position: relative;
    width: 100%;
}

.feature-teacher-container {
    width: 120px;
}
.featured-teachers button.prev{
    border-radius: 50% !important;
    height: 40px;
    width: 40px;
    margin-left: 0px;
    border: 1px solid #000;
    border-radius: 50%;
}
.featured-teachers button.prev img{
    position: relative;
    right: -2px;
}
.featured-teachers button.next{
    border-radius: 50% !important;
    height: 40px;
    width: 40px;
    margin-left: 0px;
    border: 1px solid #000;
    border-radius: 50%;
}
.featured-teachers button.next img{
    position: relative;
    right: -5px;
}

.round-image {
    width: 100%;
    /* height: 120px; */
    object-fit: cover;
    border-radius: 50%;
    margin: auto;
}

.teacher-carousel {
    display: flex;
    transition: transform 0.5s ease-in-out;
}

.teacher-carousel-item {
    flex: 0 0 calc(100% / 5);
    /* 3 items per view */
    padding: 10px;
    text-align: center;
}

/* .teacher-carousel-item img {
    height: 160px;
} */
 .featured-teachers .feature-carousel-container .teacher-name {
    font-size: 30px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: -0.5px;
    margin-bottom: 0;
    font-family: "Afacad", sans-serif;
}

.featured-teachers .feature-carousel-container .profession {
    font-size: 18px;
    font-weight: 400;
    line-height: 23px;
    letter-spacing: -0.5px;
    font-family: "Open Sans", sans-serif;
    color: #666666;
    margin-top: 10px;
    margin-bottom: 17px;
}
.featured-teachers .teacher-carousel .teacher-name {
    font-size: 30px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: -0.5px;
    margin-bottom: 0;
    font-family: "Afacad", sans-serif;
}

.featured-teachers .teacher-carousel .professtion {
    font-size: 18px;
    font-weight: 400;
    line-height: 23px;
    letter-spacing: -0.5px;
    font-family: "Open Sans", sans-serif;
    color: #666666;
    margin-top: 10px;
    margin-bottom: 17px;
}

.featured-teachers .destination-view>button {
    font-size: 14px;
    line-height: normal;
    font-weight: 700;
    font-family: "Open Sans", sans-serif;
    letter-spacing: -0.5px;
    text-transform: uppercase !important;
    color: #F9F9F9;
    background-color: #D87A61;
    width: 111px;
    height: 36px;
    border-radius: 3px;
    border: 1px solid #D87A61;
    margin-top: 20px;
}
.featured-teachers .destination-view button:hover {
    box-shadow: 0px 7px 12px -7px #000;
    border: 1px solid #000;
    color: #c3644a;
    border-radius: 5px;
}

.featured-teachers .btn-view {
    font-size: 14px;
    line-height: normal;
    font-weight: 700;
    font-family: "Open Sans", sans-serif;
    letter-spacing: -0.5px;
    color: #D87A61;
    text-transform: uppercase;
    display: block;
}
.featured-teachers .btn-view:hover{
    box-shadow: 0px 7px 12px -7px #000;
    border: 1px solid #000;
    color: #c3644a;
    border-radius: 5px;
    width: 127px;;
    margin: 0 auto;
}
.btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    border: none;
    padding: 10px;
    cursor: pointer;
    z-index: 10;
}

.btn.left {
    left: 10px;
}

.btn.right {
    right: 10px;
}

.featured-teachers .indicators {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 10px;
}

.featured-teachers .indicators .indicator {
    width: 15px;
    height: 15px;
    background: #A3C4A9;
    border-radius: 50%;
    cursor: pointer;
    opacity: .4;
}

.featured-teachers .indicators .indicator.active {
    background: #A3C4A9;
    opacity: 1;
}

/* Discover Yoga */
.discover-yoga {
    background-image: url(../img/bg-discover-yoga.png);
    background-repeat: no-repeat;
    background-size: cover;
    /* margin-top: 20px; */
    padding-top: 20px;
    padding-bottom: 20px;
}

.discover-yoga h2 {
    font-size: 25px;
    line-height: normal;
    font-weight: 700;
    letter-spacing: -0.5px;
    margin-bottom: 20px;
    font-family: "Afacad", sans-serif;
    border-left: 5px solid #D87A61;
    text-align: left !important;
    padding-left: 20px;
}

.discover-yoga a.explore {
    background-color: transparent;
    width: 170px;
    border-radius: 29px;
    border: 2px solid #000000;
    color: #000000;
    /* margin: 9px auto 0; */
    text-transform: uppercase;
    font-size: 14px;
    line-height: normal;
    letter-spacing: -0.5px;
    font-weight: 700;
    font-family: "Open Sans", sans-serif;
    display: flex;
    justify-content: center;
}

.discover-yoga .discover-carousel {
    margin-left: 0px;
    margin-top: 20px;
    margin-bottom: 20px;
}

.discover-yoga .discover-carousel img {
    border-radius: 10px;
}

.btn-discover {
    text-align: center;
    margin-top: 19px;
}

.mobile-view {
    display: block;
}

.desktop-view {
    display: none;
}

.discover-carousel .prev {
    display: block;
    width: 40px;
    height: 40px;
    background: #ffffff;
    /* opacity: .9; */
    border-radius: 50%;
    border: 1px solid #000;
}

.discover-carousel .next {
    display: block;
    width: 40px;
    height: 40px;
    background: #ffffff;
    /* opacity: .9; */
    border-radius: 50%;
    border: 1px solid #000;
}

.discover-yoga .indicator {
    display: none;
}

/* .discover-yoga #default-carousel .slide-buttons{
    margin-top: 29px;
    left: 50%;
    margin-left: -50px;
    width: 50%;
}
.discover-yoga #default-carousel .slide-buttons button.rounded-full{
    border-radius: 50%;
}
.discover-yoga #default-carousel .slide-buttons button[aria-current="true"]{
    background-color: #A3C4A9;
}
.discover-yoga #default-carousel .slide-buttons button[aria-current="false"]{
    background-color: #A3C4A9;
    opacity: .4;
} */
.discover-yoga .discover-carousel .indicator {
    opacity: 1;
    bottom: -39px;
    z-index: 9999;
    width: 170px;
    margin-top: 29px;
}

.discover-yoga .discover-carousel .indicator button {
    border-radius: 50%;
}

/* Yoga Masters section style */
.yoga-master {
    margin-top: 0px;
}

.yoga-master h2 {
    font-size: 25px;
    line-height: normal;
    font-weight: 700;
    letter-spacing: 0;
    margin-bottom: 0px;
    font-family: "Afacad", sans-serif;
    color: #333333;
    border-left: 5px solid #D87A61;
    padding-left: 14px;
    text-align: left !important;
}

.yoga-master ul {
    padding-left: 0;
}

.yoga-master ul li {
    border-bottom: 2px solid rgba(0, 0, 0, .1);
}

.yoga-master .trainer-name {
    font-size: 17px;
    line-height: normal;
    font-weight: 500;
    letter-spacing: -0.5px;
    margin-top: 20px;
    margin-bottom: 20px;
    font-family: "Afacad", sans-serif;
}

.yoga-master .content {
    font-size: 18px;
    line-height: 27px;
    font-weight: 400;
    letter-spacing: -0.5px;
    color: #666666;
    font-family: "Open Sans", sans-serif;
    margin-bottom: 20px;
}

.yoga-master a {
    display: block;
    color: #fff;
    font-size: 14px;
    line-height: normal;
    font-weight: 700;
    font-family: "Open Sans", sans-serif;
    width: 130px;
    margin: 0 auto;
    border-radius: 3px;
    padding: 10px 15px;
}
.yoga-master a:hover{
    box-shadow: 0px 7px 12px -7px #000;
    border: 1px solid #000;
    color: #c3644a;
    border-radius: 5px;
}

.yoga-master .transform {
    display: flex;
    align-items: center;
}

.yoga-master .read-master {
    margin-bottom: 30px;
}

.tab-active-img {
    display: none;
}

.yoga-master .transform .tab-active-img {
    display: flex;
}

.yoga-master .yoga-master-container .desktop-view {
    display: none
}

.yoga-master .yoga-master-container .mobile-view {
    display: block;
}

.featured-yoga .footer-flower {
    height: 140px;
    width: 185px;
    position: absolute;
    right: 0;
    top: -30px;
}

.yoga-master .yoga-master-container .mobile-view .trainer-name {
    font-size: 20px;
    font-weight: 700;
    line-height: normal;
    letter-spacing: -0.5px;
    font-family: "Afacad", sans-serif;
    color: #293148;
}

.yoga-master .yoga-master-container .mobile-view .content {
    font-size: 14px;
    font-weight: 400;
    line-height: normal;
    letter-spacing: -0.5px;
    font-family: "Poppins", sans-serif;
    margin-top: 30px;
    color: #1A1A1A;
}

.yoga-master .yoga-master-container .mobile-view .read-master button {
    width: 111px;
    height: 36px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 700;
    line-height: normal;
    letter-spacing: -0.5px;
    font-family: "Open Sans", sans-serif;
    margin-bottom: 30px;
    color: #fff;
    text-transform: uppercase;
}

.yoga-master .yoga-master-container .mobile-view .prev {
    top: 255px;
    position: absolute;
    left: 4%;
}

.yoga-master .yoga-master-container .mobile-view .next {
    top: 255px;
    left: auto;
    right: 4%;
    position: absolute;
}

.yoga-master .yoga-master-container {
    overflow-y: visible;
    /* height: -webkit-fill-available; */
}

/* .yoga-master .transform .content{
    width: 515px !important;
} */
/* width */
.yoga-master-container::-webkit-scrollbar {
    width: 22px;
}

/* Track */
.yoga-master-container::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px #A3C4A9;
    border-radius: 11px;
    background-color: #A3C4A9;
}

/* Handle */
.yoga-master-container::-webkit-scrollbar-thumb {
    background: #F4F8FA;
    border-radius: 10px;
    border: 5px solid transparent;
    background-clip: content-box;
}

/* Handle on hover */
.yoga-master-container::-webkit-scrollbar-thumb:hover {
    background: #F4F8FA;
}

.discover-yoga .grid .bg-transparent .heading {
    font-size: 20px;
    font-weight: 700;
    color: #293148;
    /* font-family: "DM Sans", sans-serif; */
    font-family: "Afacad", sans-serif;
}

.discover-yoga .grid .bg-transparent p {
    font-size: 14px;
    font-weight: 400;
    color: #1A1A1A;
    font-family: "Poppins", sans-serif;
}

/* Subscribe section style */
.subscribe {

    position: relative;
}

.subscribe .img-right {
    position: absolute;
    right: 0;
    top: 0;
    opacity: 0.5;
    width: 80px;
    z-index: 1;
}

.subscribe .left-content form {
    text-align: center;
}

.subscribe .left-img {
    position: absolute;
    bottom: 0;
    left: 0;
    display: none;
}

.subscribe h2.small-font {
    margin-top: 0px;
    margin-bottom: 17px;
    font-size: 25px;
    z-index: 3;
    line-height: normal;
    position: relative;
    font-weight: 700;
    color: #333333;
    font-family: "Afacad", sans-serif;
    letter-spacing: 0px;
    border-left: 5px solid #D87A61;
    padding-left: 14px;
}

.subscribe p {
    font-size: 18px;
    line-height: normal;
    font-weight: 500;
    letter-spacing: 0.5px;
    margin-bottom: 17px;
    font-family: "Afacad", sans-serif;
    color: #666666;
}

.subscribe .left-content form input {
    border-radius: 29px;
    border: 1px solid #fff;
    height: 60px;
    background-color: #fff;
    width: 100%;
    padding: 0 20px;
    margin-bottom: 26px;
    font-size: 20px;
    font-family: 'Open Sans', sans-serif;
}

.subscribe .left-content form input::placeholder {
    opacity: .5;
}

.subscribe .right-img {
    margin-bottom: 30px;
}

.btn-subscribe {
    border-radius: 29px;
    width: 100%;
    font-size: 20px;
    line-height: normal;
    font-weight: 600;
    font-family: 'Open Sans', sans-serif;
    letter-spacing: -0.5px;
    padding: 15px 50px;
}

/* Footer style */
.footer {
    padding-top: 50px;
    padding-bottom: 76px;
    position: relative;
}

.footer .footer-flower {
    height: 114px;
    width: 117px;
    position: absolute;
    right: 0;
    top: -10px;
    display: none;
}

.footer ul {
    padding-left: 0;
}

.footer ul li {
    margin-bottom: 15px;
}

.footer ul li:last-child {
    margin-bottom: 0px;
}

.footer .go-to ul {
    margin-bottom: 0;
}

.footer .quick-links {
    margin-top: 40px;
}

.footer .quick-links ul {
    margin-bottom: 0px;
}

.footer .contact {
    margin-top: 40px;
}

.footer ul li:not(.social-media) a {
    font-size: 18px;
    line-height: 1.2;
    color: #212121;
    opacity: .5;
    font-family: "Outfit", sans-serif;
    text-wrap: nowrap;
}

.footer .contact ul li a {
    /* font-size: 18px;
    line-height: 1.2;
    color: #212121;
    opacity: 1;
    font-family: "Outfit", sans-serif; */
}

/* .footer .contact ul li a>span {
    opacity: .5;
} */

.footer .contact ul li.social-media a {
    margin-right: 21px;
}

/* .footer .contact ul li a img {
    width: 24px;
    height: 24px;
} */

/* .footer .contact .fb>img {
    width: 20px;
    height: 20px;
} */

/* .footer .contact .fb>img {
    width: 23px;
    height: 23px;
} */

/* .footer .contact .insta>img {
    width: 21px;
    height: 21px;
} */

/* .footer .contact .youtube>img {
    width: 30px;
    height: 21px;
} */

/* .footer .contact .twitter>img {
    width: 23px;
    height: 23px;
} */

/* .footer .contact .linkedin>img {
    width: 23px;
    height: 23px;
} */

.footer .footer-logo {
    width: 117px;
    height: 114px;
}

.footer .go-to h4 {
    margin-bottom: 14px;
    font-size: 28px;
    line-height: 34px;
    font-weight: 400;
    font-family: 'Open Sans', sans-serif;
    color: #212121;
}

.footer .quick-links h4 {
    margin-bottom: 14px;
    font-size: 28px;
    line-height: 34px;
    font-weight: 400;
    font-family: 'Open Sans', sans-serif;
}

.footer .contact h4 {
    margin-bottom: 14px;
    font-size: 28px;
    line-height: 34px;
    font-weight: 400;
    font-family: 'Open Sans', sans-serif;
}

.bottom-footer .copy-right {
    text-align: center;
}

.bottom-footer {
    position: relative;
    top: 0px;
    bottom: 0px;
    width: 100%;
}

.bottom-footer .chat {
    position: fixed;
    right: 6px;
    bottom: 0px;
    z-index: 1;
}

.bottom-footer .copy-right span {
    font-size: 14px;
    font-weight: 400;
    line-height: 34px;
    font-family: "Outfit", sans-serif;
    color: #000;
}

.yogain-logo {
    width: 60px;
}

.bottom-footer .copy-right>a>span {
    font-size: 14px;
    font-weight: 400;
    line-height: 34px;
    font-family: "Outfit", sans-serif;
    color: #000;
}

.bottom-footer .copy-right a{
    display: inline-block;
}
/* Translae style */
.skiptranslate span {
    display: none;
}

.skiptranslate.goog-te-gadget {
    color: transparent;
    padding: 20px;
    position: fixed;
    bottom: 0;
    left: 95px;
    background-color: #fff;
    height: 106px;
    border-radius: 8px;
}

.goog-te-gadget .goog-te-combo {
    /* font-size: 18px; */
    border: 1px solid #aaa;
    border-radius: 5px;
    color: #000;
    padding: 20px;
}

/* .custom-dropdown{
    width: 87px;
    position: relative;
    position: relative;
    bottom: 200px;
    left: 316px;
} */
.dropdown-toggle {
    white-space: nowrap;
    display: flex;
    align-items: center;
    /* border: 2px solid #000; */
    border-radius: 50%;
    padding: 15px;
    background-color: #fff;
    width: 50px;
    height: 50px;
}

.dropdown-toggle::after {
    margin-left: .655em;
}

.dropdown-toggle>div {
    display: flex;
    align-items: center;
}

/* .dropdown-toggle img {
    margin-right: 10px;
}
.dropdown-menu{
    overflow-y: scroll;
    height: 105px;
    bottom: 58px;
}
.dropdown-menu > div{
    display: flex;
    align-items: center;
}
.dropdown-menu > div img{
    margin-right: 10px;
}
.flag-icon {
    width: 20px;
    height: 15px;
} */
.custom-dropdown {
    width: 100%;
    position: fixed;
    bottom: 0;
    right: 0;
    left: auto;
    z-index: 1;
}

.custom-dropdown .dropdown-toggle {
    width: 85px;
    height: 48px;
    position: absolute;
    left: 6px;
    bottom: 5px;
    border-radius: 8px;
    padding: 10px 10px;
    box-shadow: 0px 2px 13px -3px #000;
}

.dropdown-toggle img.flag-icon {
    margin-right: 10px;
    width: 20px;
    height: 20px;
}

.custom-dropdown .dropdown-toggle>div {
    font-size: 16px;
    font-weight: 600;
    line-height: normal;
    font-size: "Open Sans", sans-serif;
    letter-spacing: -0.5px;
}

.custom-dropdown .dropdown-menu {
    overflow-y: scroll;
    height: 130px;
    bottom: 60px;
    left: 20px;
    padding-left: 10px;
    min-width: 6rem;
}

.custom-dropdown .dropdown-menu img.flag-icon {
    width: 30px;
    height: 30px;
    margin-bottom: 10px;
    border: 1px solid #aaa;
    margin-right: 10px;
}

.custom-dropdown .dropdown-menu>div {
    display: flex;
    align-items: normal;
}

.custom-dropdown .dropdown-toggle::after {
    margin-left: 1.2em;
}

.custom-dropdown .dropdown-menu {
    min-width: 6rem;
}

/* .featured-yoga .card-container img {
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
} */

.footer .social-media a img {
    width: 24px;
}

.footer .social-media a img:hover {
    box-shadow: rgba(0, 0, 0, 0.07) 0px 1px 2px, rgba(0, 0, 0, 0.07) 0px 2px 4px, rgba(0, 0, 0, 0.07) 0px 4px 8px, rgba(0, 0, 0, 0.07) 0px 8px 16px, rgba(0, 0, 0, 0.07) 0px 16px 32px, rgba(0, 0, 0, 0.07) 0px 32px 64px;
}

/* Media query */
@media screen and (max-width:940px) {
    #mobile-menu-id {
        height: 100%;
    }

    header nav {
        padding: 10px 24px;
        background: #fff;
    }

    .header-top .header-div {
        padding: 0px 30px;
    }

    .nav-bar-menu .partner-list {
        margin: 20px 0px;
    }

    .mobile-menus {
        top: 0px;
        overflow-y: scroll;
        height: 81vh;
        padding: 20px;
    }

    .nav-bar-menu .partner-list {
        padding: 0px 0px 20px 0px;
    }

    .centers-ashrams .content {
        color: #1A1A1A;
        margin-top: 30px;
    }

    .footer .contact ul li.social-media a {
        margin-right: 10px;
    }
}

@media screen and (max-width:350px) {
    .search-style .btn-toggle label span {
        margin-left: 5px !important;
        font-size: 13px;
    }

    .footer .contact ul li.social-media a {
        margin-right: 21px;
    }

    .centers-ashrams .content {
        margin-top: 30px;
    }

    .search-location select {
        font-size: 13px;
    }

    .search-style .special-need {
        width: 100px;
        font-size: 13px;
    }

    .search-style .dropdown-container {
        width: 100px;
        font-size: 13px;
    }

    .search-place>input {
        font-size: 13px;
    }

}

@media screen and (min-width:768px) {
    /* popuplar yoga style */
    /* .yoga-destination button.prev{
        right:80px;
    }
    .yoga-destination button.next{
        right:30px;
    } */
}

@media screen and (min-width: 992px) {
    /* .featured-yoga .card-body p.info {
        font-size: 12px;
    } */

    .centers-ashrams .content {

        margin-top: 0px;
    }

    /* .featured-yoga .card-body p {
        font-size: 12px;
        margin-bottom: 15px;
        color: #5D6476;
    } */

    /* Header style */
    .menus {
        display: block;
    }

    .mobile-menus {
        display: none;
    }

    .mobile-menus>ul li>div>a {
        font-weight: 400;
    }

    .yoga-destination button.prev {
        right: 50px;
        top: 25px;
        padding: 0;
        display: block;
    }

    .yoga-destination button.next {
        right: 0px;
        top: 25px;
        padding: 0;
        display: block;
    }

    .menus>ul {
        padding-left: 0px;
    }

    .header-top {
        background-image: none;
    }

    header nav img.logo {
        width: auto;
        height: auto;
    }

    .fixed-top {
        top: 0px;
        position: fixed;
        background-color: rgb(255, 255, 255);
        z-index: 99999;
        left: 0px;
        padding-left: 80px;
        padding-right: 80px;
        box-shadow: 0px 0px 8px -2px #000;
        justify-content: center;
    }

    header .header-content img.yoga-leaf {
        position: absolute;
        top: -6px;
        right: -55px;
        width: 370px;
        z-index: -1;
    }

    .header-content p {
        font-size: 20px;
        line-height: 1.4;
        font-weight: 400;
        letter-spacing: -0.5px;
        color: #888888;
        width: auto;
    }


    header nav+div {
        display: flex;
        align-items: center;
        padding-bottom: 70px;
    }

    /* Search section style */
    .search-container {
        /* padding-top: 7px;
        padding-bottom: 0px;
        margin-bottom: 0px;
        height: 180px; */
        background-color: #A3C4A9;
    }

    .search-container h2 {
        font-size: 22px;
        line-height: normal;
        font-weight: 500;
        font-family: "Afacad", sans-serif;
        letter-spacing: 0px;
        color: #FFFFFF;
    }

    .search-location {
        margin-right: 0px;
        width: 200px;
    }

    .search-location select {
        color: #666666;
        font-size: 15px;
        line-height: normal;
        font-weight: 400;
        font-family: 'Open Sans', sans-serif;
        letter-spacing: -0.5px;
        width: 155px;
        border-bottom: 0px solid #CCCCCC;
        padding: 10px 0px;
    }

    /* .search-location .dropdown{
        color:#666666;
        padding-top: .5rem !important;
    }
    .search-location .dropdown:after{
        position: relative;
        bottom: 2px;
        left: 2px;
    } */
    .search-container p {
        font-size: 22px;
        line-height: 1.2;
        color: #fff;
        font-weight: 600;
        letter-spacing: -0.5px;
        font-family: "Open Sans", sans-serif;
    }


    .search-wrapper {
        width: 100%;
        border-radius: 50px;
        background-color: #fff;
        padding: 5px 50px 5px 15px;
        position: relative;
        /* height: 76px; */
        display: flex;
        justify-content: space-evenly;
    }

    /* .courses-view {
        display: block;
    } */

    .search-style>div {
        font-size: 18px;
        line-height: normal;
        font-weight: 400;
        font-family: 'Open Sans', sans-serif;
        letter-spacing: -0.5px;
        color: #666666;
        width: 100%;
        margin: auto;
        border-bottom: 0px;
    }

    .search-style .dropdown-container {
        width: 100%;
        margin-right: 54px;
        color: #666666;
        font-size: 14px;
        line-height: normal;
        font-weight: 400;
        font-family: 'Open Sans', sans-serif;
        letter-spacing: -0.5px;
    }

    .search-place>input {
        font-size: 14px;
        line-height: normal;
        font-weight: 400;
        font-family: 'Open Sans', sans-serif;
        letter-spacing: -0.5px;
    }

    .search-style .dropdown-container .special-need {
        /* width: 125px; */
        margin-right: 54px;
        color: #666666;
        font-size: 15px;
        line-height: normal;
        font-weight: 400;
        font-family: 'Open Sans', sans-serif;
        letter-spacing: -0.5px;
    }

    .search-style .btn-toggle label span {
        font-size: 14px;
        line-height: normal;
        font-weight: 400;
        font-family: 'Open Sans', sans-serif;
        letter-spacing: -0.5px;
    }

    .yoga-master .yoga-master-container .mobile-view .content {
        font-family: "Afacad", sans-serif;
    }

    .search-style .special-need {
        /* width: 125px; */
        margin-right: 54px;
        color: #666666;
    }

    #center-logo-slider .logo-img {
        /* width: auto;
        height: 100%; */
    }

    .discover-yoga .grid .bg-transparent .heading {
        font-size: 30px;
        font-weight: 500;
        color: #666666;
        font-family: "Afacad", sans-serif;
    }

    .featured-teachers {

        background-position: 103% -107%;

    }

    .search-style .btn-toggle {

        padding: 0px 15px 0px 0px;

    }

    .search-place {
        border-left: 2px solid #DDDDDD;
        border-bottom: 0px;
        display: flex;
        width: 325px;
        padding: 10px 0px 10px 10px;
    }

    .search-all {
        position: absolute;
        right: 7px;
        margin-right: 0px;
    }

    .search-all .btn-search {
        background-color: #D87A61;
        border-radius: 50%;
        width: auto;
        height: auto;
        color: #fff;
        font-size: 23px;
        margin-top: 0px;
        padding: 12px;
    }

    .featured-yoga .footer-flower {
        display: none;
    }

    .search-all .btn-search>img {
        display: block;
    }

    .search-all .btn-search>span {
        display: none;
    }

    .mobile-menu {
        display: none;
    }

    .menus {
        position: relative;
        background-color: transparent;
        min-height: auto;
        left: auto;
        top: auto;
        width: auto;
    }

    .menus>ul {
        flex-direction: row;
        align-items: center;
        width: auto;
    }

    .menus>ul li {
        width: auto;
    }

    .menus>ul li>div>a {
        display: flex;
        align-items: center;
        width: auto;
        padding-right: 10px;
        font-size: 16px;
        line-height: normal;
        font-family: 'Open Sans', sans-serif;
        font-weight: 400;
    }

    .menus>ul li>div>a.dropdown:after {
        position: relative;
        left: 6px;
        bottom: 2px;
    }

    .menus .partner-list {
        margin-left: 20px;

    }
    .menus .partner-list .group .dropdown-submenu > p{
        margin-bottom: 0;
    }
    .menus .partner-list a {
        border-radius: 5px;
        background-color: #D87A61;
        color: #fff;
    }

    .menus ul li.partner-list a.dropdown:hover {
        border-radius: 5px;
        background-color: #A3C4A9;
        color: #fff !important;
        box-shadow: rgba(255, 255, 255, 0.1) 0px 1px 1px 0px inset, rgba(50, 50, 93, 0.25) 0px 50px 100px -20px, rgba(0, 0, 0, 0.3) 0px 30px 60px -30px;
    }
    .menus .partner-list a.dropdown::after{
        margin-left: 10px;
        left: 0;
        border: 2px solid #fff;
        border-top: transparent;
        border-left: transparent;
    }
    .menus .partner-list .dropdown-submenu ul {
        padding-left: 0;
    }
    .menus .partner-list .dropdown-submenu a {
        border-radius: 5px;
        background-color: transparent;
        color: #283148;
        font-size: 16px;
        line-height: normal;
        font-family: 'Open Sans', sans-serif;
        font-weight: 400;
    }
    .menus .partner-list .dropdown-submenu p.account-title {
        background-color: transparent;
        color: #d87a61;
        font-size: 16px;
        line-height: normal;
        font-family: 'Open Sans', sans-serif;
        font-weight: 500;
    }
    .menus .partner-list .login .init-letter{
        background-color: #fff;
        color: #d87a61;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 5px;
        font-size: 13px;
        font-weight: 600;
        font-family: 'Open Sans', sans-serif;
    }
    .menus .partner-list .login .customer-name{
        overflow: hidden;
        text-overflow: ellipsis;
        text-wrap: nowrap;
        width: 108px;
    }
    .menus .partner-list .login .dropdown-submenu ul li a.text-logout{
        font-weight: 600;
    }
    /* .menus .partner-list .login{
        display: none;
    } */
    .menus ul li .dropdown-submenu > a{
        font-size: 16px;
        line-height: normal;
        font-family: 'Open Sans', sans-serif;
        font-weight: 400;
    }
    /* header nav{
        justify-content: normal;
    } */
    .partner {
        margin-left: 50px;
        display: block;
    }

    /* Featured yoga style */
    .mobile-menus .sub-menu-items a {
        background: #fff;
        border-radius: 5px;
        margin: 0px;
    }

    .featured-yoga .desktop-view {
        display: grid;
    }

    .featured-yoga h2 {
        width: 100%;
        font-size: 50px;
        line-height: normal;
        font-weight: 500;
        letter-spacing: -0.5px;
        font-family: "Afacad", sans-serif;
        border-left: 0px solid #D87A61;
        padding-left: 0px;
        padding-right: 0px;
        margin-bottom: 20px;
        color: #293148;
    }

    /* .featured-yoga p {
        font-size: 18px;
        line-height: 28px;
        font-weight: 400;
        letter-spacing: -0.5px;
        color: #666666;
    } */

    /* .featured-yoga .card-body p:last-child {
        margin-bottom: 0;
    } */

    /* .featured-yoga .card-body .rating {
        background-color: #D87A61;
        padding: 6px 9px;
        border-radius: 3px;
        height: 20px;
        display: inline-block;
        /* width: 39px; */
    /*    font-size: 14px;
        line-height: 11px;
        font-weight: 400;
        font-family: "Poppins", sans-serif;
        color: #fff;
        margin-left: 0px;
    } */

    /* .featured-yoga .card-body h3.yoga-name {
        font-size: 17px;
        line-height: 28px;
        font-weight: 600;
        letter-spacing: -0.5px;
        color: #283148;
        margin-top: 0px;
        margin-bottom: 9px;
        padding-left: 0px;
        padding-right: 0px;
        height: 84px;
    } */

    /* .featured-yoga .card-body h3 i {
        font-size: 15px;
        color: #FFC500;
        position: relative;
        left: 0px;
        top: 0px;
        margin-top: 0;
    } */

    /* .featured-yoga .card-body .info {
        font-size: 14px;
        line-height: normal;
        font-weight: 600;
        font-family: "Poppins", sans-serif;
        letter-spacing: 0;
        color: #D87A61;
        margin-bottom: 9px;
        padding-right: 18px;
        height: 40px;
    } */

    /* .featured-yoga p.time {
        color: #1C1B1F;
        font-size: 12px;
        line-height: 22px;
        font-weight: 600;
    } */

    /* .featured-yoga p.lang {
        color: #1C1B1F;
        font-size: 12px;
        line-height: 22px;
        font-weight: 600;

    } */

    /* .card-body {
        padding-left: 15px;
        padding-right: 15px;
        padding-top: 20px;
        padding-bottom: 15px;
    } */

    /* .featured-yoga .time i {
        color: #D87A61;
        width: 18px;
        margin-right: 5px;
    } */

    /* .featured-yoga .mode i {
        color: #D87A61;
        width: 18px;
        margin-right: 5px;
    } */

    /* .featured-yoga p.mode {
        color: #1C1B1F;
        font-size: 12px;
        line-height: 22px;
        font-weight: 600;

    } */

    /* .featured-yoga .lang i {
        color: #D87A61;
        width: 18px;
        margin-right: 5px;
    } */
    .featured-yoga .feature-yoga-home{
        height: 635px;
    }
    .featured-yoga .courses-view>button {
        font-size: 20px;
        line-height: normal;
        font-weight: 700;
        font-family: "Open Sans", sans-serif;
        letter-spacing: -0.5px;
        text-transform: uppercase !important;
        margin-top: 63px !important;
    }
    .featured-yoga .courses-view>button:hover {
        box-shadow: 0px 7px 12px -7px #000;
        border: 1px solid #000;
        color: #c3644a;
        border-radius: 5px;
    }

    .search-style .btn-toggle label span {
        /* margin-left: auto ; */
    }

    /* .featured-teachers .feature-carousel-container>div {
        margin: auto;
    } */

    .featured-teachers .feature-carousel-container img {
        border-radius: 50%;
        /* width: 200px;
        height: 200px; */
    }

    /* Most popular destination style */
    .yoga-destination {
        margin-top: 0px;
        padding-top: 30px;
        padding-bottom: 30px;
    }

    /* .yoga-destination .destination-view {
        display: block;
    } */

    .yogain-logo {
        width: 70px;
    }
    .fixed-top .yogain-logo {
        position: relative;
        right: 12px;
    }
    .yoga-destination h2 {
        margin-bottom: 23px;
        font-size: 50px;
        line-height: normal;
        font-weight: 500;
        letter-spacing: -0.5px;
        font-family: "Afacad", sans-serif;
        padding-right: 0px;
        border-left: 0px solid #D87A61;
        padding-left: 0px;
        width: auto;
        color: #293148;
        width: auto;
    }

    .yoga-destination .card-body {
        padding-left: 0;
        padding-right: 0;
        padding-bottom: 0;
    }

    .yoga-destination .courses-view {
        margin-top: 6px;
    }
    .yoga-destination #popular-slider .card-body button:hover{
        box-shadow: 0px 7px 12px -7px #000;
        border: 1px solid #000;
        color: #c3644a;
        border-radius: 5px;
    }
    .yoga-destination .card-img .overlay {
        background: linear-gradient(0deg, #000000 25%, #D9D9D900 100%);
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 85px;
        padding: 37px 0 20px;
        color: #fff;
        text-align: center;
        font-size: 20px;
        line-height: normal;
        font-weight: 600;
        letter-spacing: -0.5px;
        display: flex;
        align-items: flex-end;
        justify-content: center;
    }

    .yoga-destination .card-body p {
        font-size: 16px;
        line-height: 20px;
        font-weight: 400;
        color: #6A6A6A;
        letter-spacing: -0.5px;
        font-family: "Open Sans", sans-serif;
        text-align: left;
        padding-right: 25px;
    }

    .yoga-destination .card-body button {
        font-size: 14px;
        line-height: 12px;
        font-weight: 700;
        font-family: "Open Sans", sans-serif;
        text-transform: uppercase !important;
    }

    /* .yoga-destination .destination-view{
        display: none;
    } */
    .yoga-destination .destination-view button {
        font-size: 20px;
        line-height: normal;
        font-weight: 700;
        font-family: "Open Sans", sans-serif;
        text-transform: uppercase !important;
        letter-spacing: -0.5px;
    }

    .destination-view>button {
        font-size: 20px;
        line-height: normal;
        font-weight: 700;
        font-family: "Open Sans", sans-serif;
        letter-spacing: -0.5px;
        text-transform: uppercase !important;
        margin-top: 35px !important;
    }

    .yoga-destination .slide-buttons {
        display: none;
    }

    /* Centers and Ashrams styles */

    /* .featured-yoga .centers-ashrams.desktop-view {
        display: block;
    } */
    .centers-ashrams{
        background-color: transparent;
        padding-bottom: 0;
    }
    .centers-ashrams #featured-carousel{
        padding-left: 17px;
        position: relative;
        left: -17px;
    }
    .centers-ashrams .left-content {
        width: 100%;
    }
    .centers-ashrams .centers-ashrams-wrapper{
        padding-left: 15px;
        padding-right: 15px;
        padding-bottom: 20px;
    }
    .subscribe .left-content form {
        text-align: left;
    }

    .centers-ashrams h2 {
        font-size: 50px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: -0.5px;
        margin-bottom: 23px;
        font-family: "Afacad", sans-serif;
        color: #293148;
        border-left: 0px solid #D87A61;
        padding-left: 0px;
        padding-right: 0px;
    }

    .centers-ashrams p.heading {
        font-size: 30px;
        line-height: normal;
        letter-spacing: -0.5px;
        font-weight: 500;
        font-family: "afacad", sans-serif;
        color: #293148;
    }

    .centers-ashrams .btn-center a {
        color: #fff;
        background: #293148;
        width: 144px
    }

    /* .centers-ashrams a {
        text-transform: uppercase;
    } */

    .centers-ashrams .r-img {
        margin-left: 34px;
        position: relative;
        left: 15px;
    }

    .centers-ashrams .r-img .ashram-img {
        position: relative;
        top: 0;
        left: 13%;
        z-index: 1111;
    }

    .centers-ashrams .logo-content {
        display: block;
        margin-bottom: 0px;
    }

    .centers-ashrams .logo-content img {
        margin-right: 0;
        width: auto;
        height: auto;
    }

    .centers-ashrams .r-img #default-carousel1 {
        bottom: 85px;
    }

    .centers-ashrams .r-img #default-carousel1 .slide-buttons button {
        border-radius: 50% !important;
        margin-top: 29px;
    }

    .centers-ashrams .r-img #default-carousel1 .slide-buttons button[aria-current="true"] {
        background-color: #D87A61;
    }

    .centers-ashrams .r-img #default-carousel1 .slide-buttons button[aria-current="false"] {
        background-color: #D87A61;
        opacity: .4;
    }

    .centers-ashrams .btn-center {
        text-align: left;
    }

    .heading {
        font-size: 22px;
        line-height: normal;
        font-weight: 500;
        letter-spacing: -0.5px;
        margin-top: 13px;
        margin-bottom: 17px;
        font-family: "Afacad", sans-serif;
    }
    .featured-yoga .courses-view > button.view {
        margin-top: 0px !important;
    }
    .featured-yoga .center-indicator button.prev{
        border-radius: 50% !important;
        height: 40px;
        width: 40px;
        left: -40px;
        margin-left: 0px;
    }
    .featured-yoga .center-indicator button.prev img{
        width: 13px;
        margin-left: 4px;
    }
    .featured-yoga .center-indicator button.next{
        border-radius: 50% !important;
        height: 40px;
        width: 40px;
        right: -40px;
        margin-left: 0px;
    }
    .featured-yoga .center-indicator button.next img{
        width: 13px;
        margin-left: 5px;
    }
    .centers-ashrams .content {
        font-size: 18px;
        line-height: 23px;
        font-weight: 400;
        font-family: "Open Sans", sans-serif;
        letter-spacing: -0.5px;
        color: #293148;
        margin-bottom: 0;
    }

    .ashram-slider.mobile-view {
        display: none;
    }

    .ashram-slider {
        bottom: 70px;
        margin-bottom: 0px;
    }

    .ashram-slider .indicator {
        z-index: 9999;
        bottom: -45px;
        margin-top: 29px;
        width: 100px;
        opacity: 1;
        display: flex;
    }

    .ashram-slider .indicator button {
        border-radius: 50%;
    }

    hr.divider {
        height: 3px !important;
        margin-top: 33px;
        margin-bottom: 40px;
        display: block;
    }

    .center-logo figcaption {
        font-size: 25px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: -0.5px;
        color: #293148;
        font-family: "Afacad", sans-serif;
    }

    /* Featured Teachers style */
    .featured-teachers h2 {
        font-size: 50px;
        font-weight: 500;
        letter-spacing: -0.5px;
        color: #293148;
        font-family: "Afacad", sans-serif;
        border-left: 0px solid #D87A61;

    }

    /* .featured-teachers #featured-slider .carousel{
        margin-right: 30px;
    }
    .featured-teachers #featured-slider .carousel figure figcaption{
        text-align: center;
    } */
    .featured-teachers .feature-carousel-container {
        margin-bottom: 38px;
    }

    .featured-teachers p.teacher-name {
        font-size: 30px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: -0.5px;
        font-family: "afacad", sans-serif;
        color: #283148;
        margin-top: 10px;
    }

    .featured-teachers p.profession {
        font-size: 18px;
        font-weight: 400;
        line-height: 23px;
        letter-spacing: -0.5px;
        font-family: "Open Sans", sans-serif;
        color: #666666;
        margin-right: 0px;
    }

    .featured-teachers {
        background-position: top -100% right;
    }

    .featured-teachers .destination-view>button {
        font-size: 20px;
        line-height: normal;
        font-weight: 700;
        font-family: "Open Sans", sans-serif;
        letter-spacing: -0.5px;
        text-transform: uppercase !important;
        width: auto;
        height: auto;
        border-radius: 0px;
        background-color: transparent;
        border: 0px solid #D87A61;
        margin-top: 61px;
        color: #D87A61
    }

    .featured-teachers a.btn-view {
        font-size: 14px;
        font-weight: 700;
        line-height: normal;
        letter-spacing: -0.5px;
        font-family: "Open Sans", sans-serif;
        color: #D87A61;
        display: block;
        cursor: pointer;
    }
    .featured-teachers .btn-view:hover{
        box-shadow: 0px 7px 12px -7px #000;
        border: 1px solid #000;
        color: #c3644a;
        border-radius: 5px;
        width: 127px;
        margin: 0 auto;
    }

    .featured-teachers .btn-indicate {
        bottom: 4px;
        margin-top: 22px;
    }
    .featured-teachers button.prev{
        border-radius: 50% !important;
        height: 40px;
        width: 40px;
        margin-left: 0px;
        border: 1px solid #000;
        border-radius: 50%;
    }
    .featured-teachers button.prev img{
        position: relative;
        right: -2px;
    }
    .featured-teachers button.next{
        border-radius: 50% !important;
        height: 40px;
        width: 40px;
        margin-left: 0px;
        border: 1px solid #000;
        border-radius: 50%;
    }
    .featured-teachers button.next img{
        position: relative;
        right: -5px;
    }

    .btn-indicate button {
        border-radius: 50%;
    }

    /* .btn-indicate button[aria-current="true"]{
        background-color: #A3C4A9;
    }
    .btn-indicate button[aria-current="false"]{
        background-color: rgba(163, 196, 169, .4);
    } */
    /* Discover yoga style */
    .discover-yoga {
        margin-bottom: 30px;
        padding-top: 30px;
        padding-bottom: 40px;
    }

    .discover-yoga .discover-carousel {
        margin-left: 34px;
        margin-bottom: 0px;
    }

    .discover-yoga h2 {
        font-size: 50px;
        line-height: normal;
        font-weight: 500;
        letter-spacing: -0.5px;
        margin-bottom: 43px;
        font-family: "Afacad", sans-serif;
        border-left: 0px solid #D87A61;
        color: #000000;
        text-align: center !important;
    }

    .discover-yoga a.explore {
        border-radius: 50px;
        border: 2px solid #000;
        margin-top: 20px;
        font-size: 14px;
        line-height: normal;
        font-weight: 700;
        font-family: "Open Sans", sans-serif;
        display: flex;
        width: 170px;
        height: 46px;
        align-items: center;
        justify-content: center;
        background-color: transparent;
        color: #000;
    }
    .discover-yoga a.explore:hover {
        box-shadow: 0px 7px 12px -7px #000;
    }
    .mobile-view {
        display: none;
    }

    .desktop-view {
        display: block;
    }

    .discover-yoga .discover-carousel {
        margin-left: 35px;
        margin-top: 34px;
        margin-bottom: 20px;
    }

    .discover-carousel .prev {
        display: none;
    }

    .discover-carousel .next {
        display: none;
    }

    .discover-yoga .indicator {
        display: block;
    }

    .btn-discover {
        text-align: left;
        margin-top: 0px;
    }

    /* Yoga master style */
    .yoga-master {
        margin-bottom: 30px;
    }

    .yoga-master h2 {
        font-size: 50px;
        line-height: normal;
        font-weight: 500;
        letter-spacing: -0.5px;
        margin-bottom: 65px;
        font-family: "Afacad", sans-serif;
        text-align: center !important;
        border-left: 0px;
    }

    .yoga-master .trainer-name {
        font-size: 40px;
        line-height: 1;
        font-weight: 500;
        letter-spacing: -0.5px;
        margin-top: 30px;
        margin-bottom: 13px;
        font-family: "Afacad", sans-serif;
    }

    .yoga-master .yoga-master-container .yoga-master-wrapper {
        width: 60%;
        margin-right: 44px;
    }

    .yoga-master .yoga-master-container .yoga-master-wrapper>div {
        width: 100%;
    }

    .yoga-master .yoga-master-container .yoga-master-wrapper>div .tab-content {
        width: 89% !important;
    }

    .yoga-master a {
        display: block;
        color: #D87A61;
        font-size: 14px;
        line-height: normal;
        font-weight: 700;
        letter-spacing: -0.5px;
        font-family: "Open Sans", sans-serif;
        width: 112px;
        margin: 20px 0px;
        padding: 10px 0px;

    }

    /* Sunscribe section style */
    .subscribe p {
        font-size: 40px;
        line-height: 1.1;
        font-weight: 500;
        letter-spacing: 0.5px;
        margin-bottom: 26px;
        font-family: "Afacad", sans-serif;
        color: #666666;
    }


    .subscribe .left-content form input {
        border-radius: 29px;
        border: 1px solid #fff;
        height: 58px;
        background-color: #fff;
        width: 100%;
        padding: 0 20px;
        margin-bottom: 20px;
    }

    .subscribe h2.small-font {
        margin-top: 0px;
        margin-bottom: 20px;
        font-size: 50px;
        line-height: 1.1;
        font-weight: 500;
        color: #000000;
        font-family: "Afacad", sans-serif;
        letter-spacing: -0.5px;
        border-left: 0px solid #D87A61;
        padding-left: 0px;
    }

    .btn-subscribe {
        border-radius: 29px;
        width: auto;
        font-size: 20px;
        line-height: normal;
        font-weight: 600;
        font-family: 'Open Sans', sans-serif;
        letter-spacing: -0.5px;
        padding: 15px 50px;
    }
    .btn-subscribe:hover{
        box-shadow: 0px 7px 12px -7px #000;
    }

    .subscribe .left-img {
        position: absolute;
        bottom: 19px;
        left: 0;
        display: block;
        width: 78px;
        height: 79px;
    }

    .subscribe .right-img {
        margin-bottom: 0px;
    }

    .subscribe .right-img img {
        height: 100%;
    }

    .subscribe .img-right {
        position: absolute;
        right: 0;
        top: 9px;
        opacity: 1;
        width: auto
    }

    .footer .footer-flower {
        height: 114px;
        width: 117px;
        position: absolute;
        right: 0;
        top: -10px;
    }

    /* Footer style */
    .footer {
        padding-top: 150px;
        padding-bottom: 76px;
        position: relative;

    }

    .footer .social-media {
        margin: 25px 0px !important;
    }

    .footer .contact ul li {
        margin-bottom: 10px;
    }

    .footer .contact .footer-title-partner {
        margin-bottom: 20px !important;
    }

    .footer .footer-flower {
        width: 276px;
        height: 209px;
        position: absolute;
        right: 0;
        top: -13px;
        display: block;
    }

    .footer ul li {
        margin-bottom: 30px;
    }

    .footer ul li:last-child {
        margin-bottom: 0px;
    }

    .footer .go-to ul {
        margin-bottom: 0;
    }

    .footer .quick-links ul {
        margin-bottom: 0px;
    }

    .footer .footer-logo {
        width: 219px;
        height: 214px;
    }

    .footer .go-to h4 {
        margin-bottom: 41px;
        font-size: 28px;
        line-height: 34px;
        font-weight: 400;
        font-family: 'Open Sans', sans-serif;
        color: #212121;
    }

    .footer .quick-links {
        margin-top: 0px;
    }

    .footer .quick-links h4 {
        margin-bottom: 41px;
        font-size: 28px;
        line-height: 34px;
        font-weight: 400;
        font-family: 'Open Sans', sans-serif;
    }

    .footer .contact {
        margin-top: 0px;
    }

    .footer .contact h4 {
        margin-bottom: 41px;
        font-size: 28px;
        line-height: 34px;
        font-weight: 400;
        font-family: 'Open Sans', sans-serif;
    }

    .bottom-footer {
        position: relative;
        top: auto;
        bottom: 30px;
        width: 77%;
    }

    /* Translate dropdown style */
    .custom-dropdown {
        width: 100%;
        position: fixed;
        bottom: 0;
        right: 0;
        left: auto;
        z-index: 9999;
    }

    .custom-dropdown .dropdown-toggle {
        width: 140px;
        height: 50px;
        position: absolute;
        left: 6px;
        bottom: 70px;
        border-radius: 8px;
        padding: 10px;
    }

    .dropdown-toggle img.flag-icon {
        margin-right: 10px;
        width: 30px;
        height: 30px;
    }

    .custom-dropdown .dropdown-menu {
        overflow-y: scroll;
        height: 130px;
        bottom: 120px;
        left: 5px;
        padding-left: 10px;
        min-width: 6rem;
        width: 15%;
    }

    .custom-dropdown .dropdown-menu img.flag-icon {
        width: 30px;
        height: 30px;
        margin-bottom: 10px;
        border: 1px solid #aaa;
    }

    .custom-dropdown .dropdown-menu>div {
        display: flex;
        align-items: normal;
    }

    /* Chat style */
    .bottom-footer .chat {
        position: fixed;
        right: 6px;
        bottom: 70px;
        z-index: 1111;
    }

    .header-content h1 {
        font-size: 60px;
        line-height: 1.1;
        font-weight: 500;
        color: #222222;
        letter-spacing: -0.5px;
        margin-bottom: 23px;
        font-family: "Afacad", sans-serif;
    }

    /* .menus>ul li>div>a {
        padding-right: 10px;
        font-size: 16px;
        line-height: normal;
        font-family: 'Open Sans', sans-serif;
        font-weight: 400;
    } */

    header nav {
        margin-bottom: 45px;
    }

    /* .featured-yoga .card-container img {
        height: 135px;
    } */

    /* .featured-yoga .card-container {
        height: 520px;
    } */

    .yoga-destination #popular-slider .card-img img {
        height: 100%;
        border-radius: 18px;
    }

    .search-location select {

        font-size: 14px;
    }

    .logo-div-img {
        margin: auto;
        width: 200px;
        height: 200px;
        padding: 0px;
        text-align: center;
    }

    .logo-div-img .logo-img {
        background-size: cover;
    }

    .breadcrumb-nav ol li a {
        color: #364153;
    }
}

@media screen and (min-width:1024px) {
    .nav-bar-menu .nav-gap {
        gap: 16px;
        margin: 0;
    }

    .search-location select {
        color: #666666;
        font-size: 14px;
        line-height: normal;
        font-weight: 400;
        font-family: 'Open Sans', sans-serif;
        letter-spacing: -0.5px;
        width: 120px;
        border-bottom: 0px solid #CCCCCC;
    }

    /* header style */
    header .header-content img.yoga-leaf {
        position: absolute;
        top: 0px;
        right: 0px;
        width: 430px;
        z-index: -1;
    }

    .header-img img {
        width: 960px;
        position: relative;
        right: 0px;
    }

    .menus {
        padding-left: 0px;
    }

    /* Search style */
    .search-style .dropdown-container {
        margin-right: 0;
        width: 183px;
    }

    .search-style {
        border-left: 2px solid #DDDDDD;
        padding: 10px 0px 10px 20px;
        padding-bottom: auto;
        border-top: 0px solid #000;
        width: 390px;
    }


    /* Centers and Ashrams section style */
    .centers-ashrams .r-img .ashram-img {
        position: relative;
        top: 0;
        left: 6%;
        z-index: 1111;
    }

    /* popuplar yoga style */
    /* .yoga-destination button.prev{
        right:130px;
    }
    .yoga-destination button.next{
        right:80px;
    } */
    /* Yoga master style */
    .yoga-master .yoga-master-container .desktop-view {
        display: block;
    }

    .yoga-master .yoga-master-container .mobile-view {
        display: none;
    }

    .yoga-master .yoga-master-container {
        overflow-y: scroll;
    }


}

@media screen and (min-width:1152px) {
    .search-style {
        border-left: 2px solid #DDDDDD;
        padding: 10px 0px 10px 20px;
        padding-bottom: auto;
        border-top: 0px solid #000;
        width: 430px;
    }

    .search-location select,
    .search-style .dropdown-container,
    .search-style .btn-toggle label span,
    .search-place>input {
        font-size: 15px
    }

    .yoga-destination button.prev {
        right: 74px;
        top: 38px;
        padding: 0;
        display: block;
    }

    .yoga-destination button.next {
        right: 24px;
        top: 38px;
        padding: 0;
        display: block;
    }

    .featured-yoga-center button.prev {
        right: 50px;
        top: 38px;
        padding: 0;
        display: block;
    }

    .featured-yoga-center button.next {
        right: 0px;
        top: 38px;
        padding: 0;
        display: block;
    }

    .yoga-destination .card-img .overlay {
        font-size: 28px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
    }

    .destination-view>button {
        margin-top: 35px !important;
    }

    .yoga-destination #popular-slider .card-img img {
        height: 340px;
    }

    .center-logo figcaption {
        font-size: 30px;
    }

    /* .featured-yoga .card-container {
        height: 620px;
    } */

    /* .featured-yoga .card-container img {
        height: 223px;
    } */

    /* .featured-yoga p.lang {
        font-size: 16px;
    } */

    /* .featured-yoga p.mode {
        font-size: 16px
    } */

    /* .featured-yoga p.time {
        font-size: 16px;
    } */

    /* .featured-yoga .card-body p.info {
        font-size: 16px;
        margin-bottom: 15px;
    } */

    /* .featured-yoga .card-body p {
        font-size: 15px;
        margin-bottom: 20px;
    } */

    /* .featured-yoga .card-body h3 {
        font-size: 22px;
    } */

    .search-place>input {
        font-size: 18px;
    }

    .search-style .btn-toggle label span {
        font-size: 18px;
    }

    .search-style .dropdown-container {
        font-size: 18px;
        width: 100%;
    }

    .search-location select {
        padding: 0px 0px;
        font-size: 18px;
    }

    .search-wrapper {
        width: 100%;
        border-radius: 50px;
        background-color: #fff;
        padding: 15px 53px 15px 20px;
        position: relative;
        /* height: 76px; */
        display: flex;
        justify-content: space-evenly;
    }

    .search-all .btn-search {
        background-color: #D87A61;
        border-radius: 50%;
        color: #fff;
        font-size: 18px;
        margin-top: 0px;
        padding: 13px;
    }

    .header-content h1 {
        font-size: 60px;
        line-height: 1.1;
        font-weight: 500;
        color: #222222;
        letter-spacing: -0.5px;
        margin-bottom: 23px;
        font-family: "Afacad", sans-serif;
    }

    .nav-bar-menu .nav-gap {
        gap: 19px;
    }

    .header-content {
        width: 75%;
    }

    .menus {
        padding-left: 100px;
    }

    .header-content p {
        font-size: 24px;
        line-height: 1.4;
        font-weight: 400;
        letter-spacing: -0.5px;
        color: #888888;
        width: auto;
    }

    .search-style {
        border-left: 2px solid #DDDDDD;
        padding: 0px 0px 0px 20px;
        border-top: 0px solid #000;
        border-bottom: 0px solid #000;
        display: flex;
    }

    .search-place {
        border-left: 2px solid #DDDDDD;
        padding: 0px 0px 0px 20px;
        border-top: 0px solid #000;
        border-bottom: 0px solid #000;
        display: flex;
    }

    .featured-yoga h2 {
        font-size: 50px;
        line-height: normal;
        font-weight: 500;
        letter-spacing: -0.5px;
        font-family: "Afacad", sans-serif;
        border-left: 0px solid #D87A61;
        padding-left: 0px;
        padding-right: 0px;
        color: #283148;
    }

    /* Yoga Destination style */
    .yoga-destination h2 {
        margin-bottom: 23px;
        font-size: 50px;
        line-height: normal;
        font-weight: 500;
        letter-spacing: -0.5px;
        font-family: "Afacad", sans-serif;
        padding-right: 0px;
        border-left: 0px solid #D87A61;
        padding-left: 0px;
        width: auto;
    }

    .centers-ashrams h2 {
        font-size: 50px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: -0.5px;
        font-family: "Afacad", sans-serif;
        color: #222222;
        border-left: 0px solid #D87A61;
    }

    .centers-ashrams .r-img .ashram-img {
        position: relative;
        top: 0;
        left: 25%;
        z-index: 1111;
    }

    .centers-ashrams .r-img .ashram-img {
        position: relative;
        top: 0;
        left: 32%;
        z-index: 1111;
    }

    /* Featured Teachers style */
    .featured-teachers h2 {
        font-size: 50px;
        font-weight: 500;
        letter-spacing: -0.5px;
        font-family: "Afacad", sans-serif;
        border-left: 0px solid #D87A61;
        margin-bottom: 35px;
    }

    /* Discover Yoga style */
    .discover-yoga h2 {
        font-size: 50px;
        line-height: normal;
        font-weight: 500;
        letter-spacing: -0.5px;
        margin-bottom: 43px;
        font-family: "Afacad", sans-serif;
        border-left: 0px solid #D87A61;
        text-align: center;
    }

    /* Yoga Master section style */
    .yoga-master h2 {
        font-size: 50px;
        line-height: normal;
        font-weight: 500;
        letter-spacing: -0.5px;
        margin-bottom: 65px;
        font-family: "Afacad", sans-serif;
        border-left: 0px solid #D87A61;

        text-align: center !important;
        color: #000000;
    }

    .yoga-master .transform .tab-active-img {
        display: flex;
        justify-content: center;
    }

    /* Subscribe section style */
    .subscribe p {
        font-size: 40px;
        line-height: 1.1;
        font-weight: 400;
        letter-spacing: -0.5px;
        margin-bottom: 26px;
        font-family: "Afacad", sans-serif;
        color: #666666;
    }



    .subscribe .left-content {
        margin-right: 35px;
        margin-top: 10%;
    }
}

@media screen and (min-width:1280px) {
    /* .featured-yoga .card-container {
        height: 650px;
    } */

    /* header style */
    .menus {
        padding-left: 64px;
    }

    /* .featured-yoga p.time,
    .featured-yoga p.mode,
    .featured-yoga p.lang {
        font-size: 16px !important;
        font-family: "Open Sans", sans-serif;
    } */

    /* .featured-yoga .card-body p {
        font-size: 16px;
    } */

    .nav-bar-menu .nav-gap {
        gap: 28px;
    }

    /* .header-top {
        background-image: url(../img/leaf.png);
        background-repeat: no-repeat;
        background-position: 100% 143%;
        background-size: 36%;
    } */
    header .header-content img.yoga-leaf {
        position: absolute;
        top: -35px;
        right: 0px;
        width: 450px;
        z-index: -1;
    }

    /* Centers and Ashrams style */
    .centers-ashrams .r-img .ashram-img {
        position: relative;
        top: 0;
        left: 23%;
        z-index: 1111;
    }

    /* Featured Teacher style */
    .featured-teachers .feature-carousel-container img {
        border-radius: 50%;
        /* width: 200px;
        height: 190px; */
    }

    /* Yoga master style */
    .yoga-master .transform .tab-active-img {
        display: flex;
        margin-left: 30px;
    }

    .yoga-master .yoga-master-wrapper {
        width: 550px;
        margin-right: 46px;
    }

    /* .yoga-master .yoga-image-wrapper{
        width: 300px;
    } */
    .yoga-master .yoga-master-container>section {
        padding-right: 45px;
    }

    .yoga-master .transform .content {
        width: 490px !important;
    }
}

@media screen and (min-width:1440px) {
    .featured-teachers {

        background-position: 103% -200px;

    }

    .featured-yoga p.time,
    .featured-yoga p.mode,
    .featured-yoga p.lang {
        font-size: 18px !important;
    }

    /* .header-top {
        background-image: url(../img/leaf.png);
        background-repeat: no-repeat;
        background-position: 115% 200%;
        background-size: 43%;
    } */

    /* .search-location select {
        width: 300px;
    } */
    .header-content {
        width: 68%;
    }

    .header-img img {
        /* width: 1130px; */
        position: relative;
        right: 0px;
        /* height: 414px; */
    }

    .menus {
        padding-left: 243px;
    }

    header .header-content img.yoga-leaf {
        position: absolute;
        top: -15px;
        right: 0px;
        width: 470px;
        z-index: -1;
    }


    /* Centers and Ashrams style */
    .centers-ashrams .r-img .ashram-img {
        position: relative;
        top: 0;
        left: 33%;
        z-index: 1111;
    }


    /* Yoga master style */
    .yoga-master .yoga-master-wrapper {
        width: 654px;
        margin-right: 46px;
    }

    /* .yoga-master .yoga-image-wrapper{
        width: 354px;
    } */
    .yoga-master .yoga-master-container>section {
        padding-right: 45px;
    }

    .yoga-master .transform .content {
        width: 530px !important;
    }

    .subscribe .left-content {
        margin-right: 0px;
        margin-top: 12%;
    }

    .subscribe .img-right {
        position: absolute;
        right: 0;
        top: 9px;
        margin-left: 30px;
        width: auto;
    }

    .subscribe .right-img {
        margin-bottom: 0px;
        width: auto;
        margin-left: 36px;
    }
}

@media screen and (min-width:1536px) {


    header .header-content img.yoga-leaf {
        position: absolute;
        top: -85px;
        right: 0px;
        width: 540px;
        z-index: -1;
    }

    .header-content {
        width: 71%;
    }

    .menus {
        padding-left: 84px;
    }

    .header-img img {
        /* width: 1200px; */
        position: relative;
        right: -15px;
        /* height: 414px; */
    }

    /* Centers and Ashrams style */
    .centers-ashrams .r-img .ashram-img {
        position: relative;
        top: 0;
        left: 37%;
        z-index: 1111;
    }

}

@media screen and (min-width: 1600px) {
    .menus>ul li>div>a {
        font-size: 20px;
    }

    /* .header-top {
        background-position: 107% 150%;
        background-size: 38%;
    } */
    .centers-ashrams .r-img #default-carousel1 {
        bottom: 257px;
    }

    /* .search-location select {
        width: 400px;
    } */
    .nav-bar-menu .nav-gap {
        gap: 40px;
    }

    .centers-ashrams .r-img .ashram-img {
        width: 650px;
    }

    .subscribe .left-content {

        margin-top: 17%;
    }
}

@media screen and (min-width: 1920px) {


    .nav-bar-menu .nav-gap {
        gap: 45px;
    }

    .header-content {
        width: 45%;
    }

    .header-img img {
        /* width: 1200px; */
        position: relative;
        right: -20px;
        /* height: 414px; */
    }

    .menus {
        padding-left: 355px;
    }

    /* Search style */
    /* .search-container > div{
        padding-left: 320px;
        padding-right: 320px;
    } */
    /* Featured yoga style */
    /* .featured-yoga{
        padding-left: 320px;
        padding-right: 320px;
    } */
    /* Yoga destination style */
    /* .yoga-destination{
        padding-left: 320px;
        padding-right: 320px;
    } */
    /* .yoga-destination button.prev{
        right:350px;
    }
    .yoga-destination button.next{
        right:300px;
    } */
    /* Centers and ashrams style */
    /* .centers-ashrams{
        padding-left: 320px;
        padding-right: 320px;
    } */
    .centers-ashrams .r-img .ashram-img {
        width: 440px;
        height: 270px;
        position: relative;
        top: 0;
        left: 33%;
        z-index: 1111;
    }

    .centers-ashrams .r-img #default-carousel1 {
        bottom: 135px;
    }

    /* Featured Teacher style */
    /* .featured-teachers{
        padding-left: 320px;
        padding-right: 320px;
    } */
    /* .featured-teachers {
        background-position: 103% 155%;
    } */

    /* Discover yoga style */
    /* .discover-yoga{
        padding-left: 320px;
        padding-right: 320px;
    } */
    .discover-yoga .discover-carousel {
        margin-left: 35px;
        margin-top: 0;
        margin-bottom: 20px;
    }

    /* Yoga Master style */
    /* .yoga-master{
        padding-left: 320px;
        padding-right: 320px;
    } */
    /* Subscribe style */
    /* .subscribe{
        padding-left: 320px;
        padding-right: 320px;
    } */
    .subscribe .right-img {
        margin-bottom: 0px;
        width: auto;
        margin-left: 36px;
    }

    /* Footer style */
    /* .footer{
        padding-left: 320px;
        padding-right: 320px;
    } */
    .footer .grid {
        gap: 375px;
    }
}

@media screen and (min-width: 2028px) {
    .menus {
        padding-left: 300px;
    }
}

.group-booking h2 {
    font-family: "Open Sans", sans-serif;
    font-size: 40px;
    font-weight: 700;
    color: #293148;
}

.group-booking .content p {
    font-family: "Open Sans", sans-serif;
    font-size: 18px;
    font-weight: 600;
}

.time-info .italic {
    font-family: "Open Sans", sans-serif;
    font-style: italic;
    font-size: 20px;
    font-weight: 600;
}

.grp-booking-body {
    border-bottom: 1px solid #C45F44;
    padding: 40px 0px;
}

/* section1 */
/* section2 */
.select-branch h3 {
    font-family: "Open Sans", sans-serif;
    font-size: 32px;
    font-weight: 700;
}

.select-branch .select-date {
    font-family: "Open Sans", sans-serif;
    font-size: 20px;
    font-weight: 700;

}