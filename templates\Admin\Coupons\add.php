<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Coupon $coupon
 */
?>
<?php $this->append('style'); ?>
<style>
    .custom-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-size: 1rem;
    resize: vertical;
    min-height: 45px;
    transition: border-color 0.3s;
}

.custom-textarea:focus {
    border-color: #007bff;
    outline: none;
}

/* Remove validation styling */
.form-control:valid,
.form-control:invalid,
.form-select:valid,
.form-select:invalid {
    border-color: #ced4da !important;
    background-image: none !important;
}

.was-validated .form-control:valid,
.was-validated .form-control:invalid,
.was-validated .form-select:valid,
.was-validated .form-select:invalid {
    border-color: #ced4da !important;
    background-image: none !important;
}

.valid-feedback,
.invalid-feedback {
    display: none !important;
}

.nav-tabs .nav-link.active {
    background: #fff;
    border-bottom: 2px solid #007bff;
    color: #007bff;
}
.tab-content {
    padding-top: 1.5rem;
}
.form-label {
    font-weight: 600;
    color: #495057;
}
.required {
    color: #dc3545 !important;
}
.form-control {
    height: 45px;
}
.form-control:not(textarea):not(select) {
    height: 45px;
}
textarea.form-control {
    min-height: 100px;
}
.row.g-3 .col-12, .row.g-3 .col-md-6 {
    margin-bottom: 1rem;
}
.is-invalid {
    border-color: #dc3545 !important;
}
.invalid-feedback {
    display: block !important;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
</style>
<?php $this->end(); ?>

<div class="main-content bg-container">
    <div class="row">
        <div class="col-12 col-xl-12">
            <div class="card card-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <ul class="breadcrumb breadcrumb-style m-0">
                        <li class="breadcrumb-item">Marketing</li>
                        <li class="breadcrumb-item">
                            <a href="<?= $this->Url->build(['action' => 'index']) ?>">Coupons</a>
                        </li>
                        <li class="breadcrumb-item">Add Coupon</li>
                    </ul>

                    <a href="javascript:void(0);" class="d-flex align-items-center category-back-button breadcrumb m-0" id="back-button-mo" onclick="history.back();">
                        <span class="rotate me-2">⤣</span>
                        <small class="fw-bold"><?= __("BACK") ?></small>
                    </a>
                </div>

                <div class="card-header">
                    <h4>Add New Coupon</h4>
                </div>
                <div class="card-body">
                    <?= $this->Form->create($coupon, ['id' => 'add-coupon-form']) ?>

                    <ul class="nav nav-tabs" id="couponTab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="basic-tab" data-bs-toggle="tab" href="#basic" role="tab" aria-controls="basic" aria-selected="true">
                                Basic Info
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="discount-tab" data-bs-toggle="tab" href="#discount" role="tab" aria-controls="discount" aria-selected="false">
                                Discount Details
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="limits-tab" data-bs-toggle="tab" href="#limits" role="tab" aria-controls="limits" aria-selected="false">
                                Usage & Validity
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content p-3 border border-top-0" id="couponTabContent">
                        <!-- Basic Information Tab -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <?= $this->Form->control('code', [
                                                'label' => '<span class="form-label fw-bold text-dark">Coupon Code <span class="required">*</span></span>',
                                                'class' => 'form-control',
                                                'placeholder' => 'e.g., SAVE20, WELCOME10',
                                                'required' => true,
                                                'minlength' => 3,
                                                'maxlength' => 20,
                                                'pattern' => '[A-Z0-9]+',
                                                'title' => 'Coupon code must contain only uppercase letters and numbers (3-20 characters)',
                                                'escape' => false,
                                                'style' => 'text-transform: uppercase;'
                                            ]) ?>
                                        </div>
                                        <div class="col-12">
                                            <?= $this->Form->control('title', [
                                                'label' => '<span class="form-label fw-bold text-dark">Title</span>',
                                                'class' => 'form-control',
                                                'placeholder' => 'e.g., Welcome Discount, Summer Sale',
                                                'maxlength' => 100,
                                                'escape' => false
                                            ]) ?>
                                        </div>
                                       
                                    </div>
                                </div>
                               
                                <div class="col-md-6 d-flex flex-column justify-content-between">
                                    <div class="mb-3">
                                        <?= $this->Form->control('description', [
                                            'label' => '<span class="form-label fw-bold text-dark">Description</span>',
                                            'class' => 'custom-textarea',
                                            'placeholder' => 'Describe the coupon and its terms...',
                                            'rows' => 4,
                                            'escape' => false,
                                            'type' => 'textarea',
                                            'style'=>'height:44px',
                                        ]) ?>
                                    </div>
                                    <div class="mb-3">
                                        <?= $this->Form->control('status', [
                                            'label' => '<span class="form-label fw-bold text-dark">Status <span class="required">*</span></span>',
                                            'type' => 'select',
                                            'options' => ['Active' => 'Active', 'Inactive' => 'Inactive'],
                                            'class' => 'form-select',
                                            'default' => 'Active',
                                            'required' => true,
                                            'escape' => false
                                        ]) ?>
                                    </div>
                                </div>

                            </div>
                        </div>
                        

                        <!-- Discount Settings Tab -->
                        <div class="tab-pane fade" id="discount" role="tabpanel" aria-labelledby="discount-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <?= $this->Form->control('discount_type', [
                                                'label' => '<span class="form-label fw-bold text-dark">Discount Type <span class="required">*</span></span>',
                                                'type' => 'select',
                                                'options' => [
                                                    'percentage' => 'Percentage (%)',
                                                    'fixed' => 'Fixed Amount'
                                                ],
                                                'class' => 'form-select',
                                                'id' => 'discount-type',
                                                'required' => true,
                                                'escape' => false
                                            ]) ?>
                                        </div>
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-12">
                                                    <?= $this->Form->control('discount_value', [
                                                        'label' => '<span class="form-label fw-bold text-dark">Discount Value <span class="required">*</span></span>',
                                                        'type' => 'text',
                                                        'class' => 'form-control',
                                                        'placeholder' => '0.00',
                                                        'required' => true,
                                                        'escape' => false
                                                    ]) ?>
                                                </div>
                                                <!-- <div class="col-4" id="currency-field">
                                                    <?= $this->Form->control('currency_code', [
                                                        'label' => '<span class="form-label fw-bold text-dark">Currency</span>',
                                                        'class' => 'form-control',
                                                        'placeholder' => 'USD',
                                                        'maxlength' => 10,
                                                        'value' => 'USD',
                                                        'escape' => false
                                                    ]) ?>
                                                </div> -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <?= $this->Form->control('min_cart_value', [
                                                'label' => '<span class="form-label fw-bold text-dark">Minimum Booking Value</span>',
                                                'type' => 'text',
                                                'class' => 'form-control',
                                                'placeholder' => '0.00',
                                                'escape' => false
                                            ]) ?>
                                        </div>
                                       <div class="col-12" id="currency-field">
                                            <?= $this->Form->control('currency_code', [
                                                'label'=> '<span class="form-label fw-bold text-dark">Currancy</span>',
                                                'type' => 'select',
                                                'options' => [
                                                    'INR' => 'INR',
                                                    'USD' => 'USD',
                                                    'EURO' => 'EURO'
                                                ],
                                              
                                                'class' => 'form-control',
                                                'escape' => false,
                                                'default' => 'USD',
                                                'empty' => false, // Removes the "Choose an option" empty default
                                            ]) ?>
                                        </div>

                                        <!-- <div class="col-12" id="max-discount-field">
                                            <?= $this->Form->control('max_discount_value', [
                                                'label' => '<span class="form-label fw-bold text-dark">Maximum Discount Amount</span>',
                                                'type' => 'number',
                                                'step' => '0.01',
                                                'min' => '0',
                                                'class' => 'form-control',
                                                'placeholder' => '0.00',
                                                'escape' => false
                                            ]) ?>
                                        </div> -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Usage & Validity Tab -->
                        <div class="tab-pane fade" id="limits" role="tabpanel" aria-labelledby="limits-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <h6 class="fw-bold text-dark mb-3">Usage Limits</h6>
                                        </div>
                                        <div class="col-12">
                                            <?= $this->Form->control('usage_limit', [
                                                'label' => '<span class="form-label fw-bold text-dark">Total Usage Limit</span>',
                                                'type' => 'text',
                                                'class' => 'form-control',
                                                'placeholder' => 'Leave empty for unlimited',
                                                'escape' => false
                                            ]) ?>
                                            <small class="form-text text-muted">Maximum number of times this coupon can be used in total</small>
                                        </div>
                                        <div class="col-12">
                                            <?= $this->Form->control('per_user_limit', [
                                                'label' => '<span class="form-label fw-bold text-dark">Per User Limit</span>',
                                                'type' => 'text',
                                                'class' => 'form-control',
                                                'placeholder' => 'Leave empty for unlimited',
                                                'escape' => false
                                            ]) ?>
                                            <small class="form-text text-muted">Maximum number of times a single user can use this coupon</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <h6 class="fw-bold text-dark mb-3">Validity Period</h6>
                                        </div>
                                        <div class="col-12">
                                            <?= $this->Form->control('start_date', [
                                                'label' => '<span class="form-label fw-bold text-dark">Start Date & Time</span>',
                                                'type' => 'datetime-local',
                                                'class' => 'form-control',
                                                'escape' => false
                                            ]) ?>
                                            <small class="form-text text-muted">Leave empty if the coupon should be active immediately</small>
                                        </div>
                                        <div class="col-12">
                                            <?= $this->Form->control('end_date', [
                                                'label' => '<span class="form-label fw-bold text-dark">End Date & Time</span>',
                                                'type' => 'datetime-local',
                                                'class' => 'form-control',
                                                'min' => date('Y-m-d\TH:i'),
                                                'escape' => false
                                            ]) ?>
                                            <small class="form-text text-muted">Leave empty if the coupon should never expire</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between mt-4">
                        <?= $this->Html->link(__('Cancel'), ['action' => 'index'], ['class' => 'btn btn-secondary']) ?>
                        <div>
                            <!-- <?= $this->Form->button(__('Save Draft'), [
                                'type' => 'submit',
                                'name' => 'status',
                                'value' => 'Inactive',
                                'class' => 'btn btn-outline-primary me-2'
                            ]) ?> -->
                            <?= $this->Form->button(__('Save & Validate'), [
                                'type' => 'submit',
                                'name' => 'status',
                                'value' => 'Active',
                                'class' => 'btn btn-primary',
                                'onclick' => 'return validateForm()'
                            ]) ?>
                        </div>
                    </div>

                    <?= $this->Form->end() ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const discountTypeSelect = document.getElementById('discount-type');
    const currencyField = document.getElementById('currency-field');
    const maxDiscountField = document.getElementById('max-discount-field');

    // function toggleFields() {
    //     const isPercentage = discountTypeSelect.value === 'percentage';

    //     // Show/hide currency field based on discount type
    //     if (isPercentage) {
    //         currencyField.style.display = 'none';
    //       //  maxDiscountField.style.display = 'block';
    //     } else {
    //         currencyField.style.display = 'block';
    //        // maxDiscountField.style.display = 'none';
    //     }

    //     // Update placeholder and label for discount value
    //     const discountValueInput = document.querySelector('input[name="discount_value"]');

    //     if (isPercentage) {
    //         discountValueInput.placeholder = 'e.g., 20 (for 20%)';
    //         discountValueInput.max = '100';
    //     } else {
    //         discountValueInput.placeholder = 'e.g., 10.00';
    //         discountValueInput.removeAttribute('max');
    //     }
    // }

    // Initialize on page load
    //toggleFields();

    // Update when discount type changes
    //discountTypeSelect.addEventListener('change', toggleFields);

    // Generate coupon code suggestion
    const codeInput = document.querySelector('input[name="code"]');
    const titleInput = document.querySelector('input[name="title"]');

    if (titleInput && codeInput) {
        titleInput.addEventListener('blur', function() {
            if (!codeInput.value && this.value) {
                // Generate a simple code from title
                const code = this.value
                    .toUpperCase()
                    .replace(/[^A-Z0-9]/g, '')
                    .substring(0, 10);
                if (code.length >= 3) {
                    codeInput.value = code;
                }
            }
        });
    }

    // Set minimum date for end date field
    const endDateInput = document.querySelector('input[name="end_date"]');
    if (endDateInput) {
        const now = new Date();
        const minDateTime = now.toISOString().slice(0, 16);
        endDateInput.min = minDateTime;
    }

    // Setup real-time validation
    setupRealTimeValidation();
});

function validateForm() {
    let isValid = true;

    // Clear previous validation errors and tab indicators
    document.querySelectorAll('.invalid-feedback').forEach(el => el.remove());
    document.querySelectorAll('.nav-link .text-danger').forEach(el => el.remove());
    document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));

    // Define field validation rules
    const validationRules = [
        { name: 'code', label: 'Coupon Code', tab: 'basic-tab', required: true },
        { name: 'discount_type', label: 'Discount Type', tab: 'discount-tab', required: true },
        { name: 'discount_value', label: 'Discount Value', tab: 'discount-tab', required: true, numeric: true },
        { name: 'usage_limit', label: 'Total Usage Limit', tab: 'limits-tab', required: false, numeric: true },
        { name: 'per_user_limit', label: 'Per User Limit', tab: 'limits-tab', required: false, numeric: true },
        { name: 'min_cart_value', label: 'Minimum Booking Value', tab: 'discount-tab', required: false, numeric: true }
    ];

    let tabsWithErrors = new Set();

    validationRules.forEach(rule => {
        const input = document.querySelector(`[name="${rule.name}"]`);
        if (!input) return;

        const value = input.value.trim();
        let errorMessage = '';

        // Check required fields
        if (rule.required && !value) {
            errorMessage = `${rule.label} is required.`;
            isValid = false;
            tabsWithErrors.add(rule.tab);
        }
        // Check numeric fields
        else if (rule.numeric && value && !/^\d*\.?\d*$/.test(value)) {
            errorMessage = `${rule.label} must be a valid number.`;
            isValid = false;
            tabsWithErrors.add(rule.tab);
        }
        // Check discount value specific rules
        else if (rule.name === 'discount_value' && value) {
            const discountType = document.querySelector('[name="discount_type"]')?.value;
            if (discountType === 'percentage' && parseFloat(value) > 100) {
                errorMessage = 'Percentage discount cannot exceed 100%.';
                isValid = false;
                tabsWithErrors.add(rule.tab);
            }
        }

        if (errorMessage) {
            showFieldError(input, errorMessage);
        }
    });

    // Check end date is not in the past
    const endDateInput = document.querySelector('input[name="end_date"]');
    if (endDateInput && endDateInput.value) {
        const endDate = new Date(endDateInput.value);
        const now = new Date();
        if (endDate < now) {
            showFieldError(endDateInput, 'End date cannot be in the past.');
            isValid = false;
            tabsWithErrors.add('limits-tab');
        }
    }

    // Add red asterisk to tabs with errors
    tabsWithErrors.forEach(tabId => {
        const tab = document.getElementById(tabId);
        if (tab && !tab.querySelector('.text-danger')) {
            tab.innerHTML += ' <span class="text-danger">*</span>';
        }
    });

    // Focus on first tab with error
    if (!isValid && tabsWithErrors.size > 0) {
        const firstErrorTab = document.getElementById(Array.from(tabsWithErrors)[0]);
        if (firstErrorTab) {
            firstErrorTab.click();
        }
    }

    return isValid;
}

function showFieldError(input, message) {
    input.classList.add('is-invalid');

    // Create error message element
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;

    // Insert after the input
    input.parentNode.appendChild(errorDiv);
}

// Real-time validation to remove errors and asterisks when fields are corrected
function setupRealTimeValidation() {
    const validationRules = [
        { name: 'code', tab: 'basic-tab', required: true },
        { name: 'discount_type', tab: 'discount-tab', required: true },
        { name: 'discount_value', tab: 'discount-tab', required: true, numeric: true },
        { name: 'usage_limit', tab: 'limits-tab', required: false, numeric: true },
        { name: 'per_user_limit', tab: 'limits-tab', required: false, numeric: true },
        { name: 'min_cart_value', tab: 'discount-tab', required: false, numeric: true }
    ];

    validationRules.forEach(rule => {
        const input = document.querySelector(`[name="${rule.name}"]`);
        if (input) {
            input.addEventListener('input', function() {
                // Clear previous error for this field
                this.classList.remove('is-invalid');
                const errorDiv = this.parentNode.querySelector('.invalid-feedback');
                if (errorDiv) {
                    errorDiv.remove();
                }

                // Validate current input
                const value = this.value.trim();
                let isValid = true;

                if (rule.required && !value) {
                    isValid = false;
                } else if (rule.numeric && value && !/^\d*\.?\d*$/.test(value)) {
                    isValid = false;
                    showFieldError(this, `${rule.label || rule.name} must be a valid number.`);
                } else if (rule.name === 'discount_value' && value) {
                    const discountType = document.querySelector('[name="discount_type"]')?.value;
                    if (discountType === 'percentage' && parseFloat(value) > 100) {
                        isValid = false;
                        showFieldError(this, 'Percentage discount cannot exceed 100%.');
                    }
                }

                // Check if all fields in this tab are now valid
                if (isValid) {
                    const tabFields = validationRules.filter(f => f.tab === rule.tab);
                    const allValid = tabFields.every(f => {
                        const inp = document.querySelector(`[name="${f.name}"]`);
                        if (!inp) return true;
                        const val = inp.value.trim();
                        if (f.required && !val) return false;
                        if (f.numeric && val && !/^\d*\.?\d*$/.test(val)) return false;
                        return true;
                    });

                    if (allValid) {
                        const tab = document.getElementById(rule.tab);
                        const asterisk = tab?.querySelector('.text-danger');
                        if (asterisk) {
                            asterisk.remove();
                        }
                    }
                }
            });

            // Restrict input for numeric fields
            if (rule.numeric) {
                input.addEventListener('keypress', function(e) {
                    // Allow: backspace, delete, tab, escape, enter, decimal point
                    if ([46, 8, 9, 27, 13, 110, 190].indexOf(e.keyCode) !== -1 ||
                        // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                        (e.keyCode === 65 && e.ctrlKey === true) ||
                        (e.keyCode === 67 && e.ctrlKey === true) ||
                        (e.keyCode === 86 && e.ctrlKey === true) ||
                        (e.keyCode === 88 && e.ctrlKey === true)) {
                        return;
                    }
                    // Ensure that it is a number and stop the keypress
                    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                        e.preventDefault();
                    }
                    // Only allow one decimal point
                    if (e.keyCode === 190 && this.value.indexOf('.') !== -1) {
                        e.preventDefault();
                    }
                });
            }
        }
    });
}
</script>
<?php $this->end(); ?>
