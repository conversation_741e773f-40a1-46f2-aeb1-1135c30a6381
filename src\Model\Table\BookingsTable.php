<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * Bookings Model
 *
 * @property \App\Model\Table\CustomersTable&\Cake\ORM\Association\BelongsTo $Customers
 * @property \App\Model\Table\CoursesTable&\Cake\ORM\Association\BelongsTo $Courses
 * @property \App\Model\Table\ClassesTable&\Cake\ORM\Association\BelongsTo $Classes
 * @property \App\Model\Table\ClassTimeSlotsTable&\Cake\ORM\Association\BelongsTo $ClassTimeSlots
 * @property \App\Model\Table\PartnersTable&\Cake\ORM\Association\BelongsTo $Partners
 * @property \App\Model\Table\DiscountsTable&\Cake\ORM\Association\BelongsTo $Discounts
 * @property \App\Model\Table\BookingAddonsTable&\Cake\ORM\Association\HasMany $BookingAddons
 * @property \App\Model\Table\BookingDetailsTable&\Cake\ORM\Association\HasMany $BookingDetails
 * @property \App\Model\Table\CancellationsTable&\Cake\ORM\Association\HasMany $Cancellations
 * @property \App\Model\Table\PartnerSettlementsTable&\Cake\ORM\Association\HasMany $PartnerSettlements
 * @property \App\Model\Table\PaymentsTable&\Cake\ORM\Association\HasMany $Payments
 *
 * @method \App\Model\Entity\Booking newEmptyEntity()
 * @method \App\Model\Entity\Booking newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Booking> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Booking get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Booking findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Booking patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Booking> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Booking|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Booking saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Booking>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Booking>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Booking>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Booking> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Booking>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Booking>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Booking>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Booking> deleteManyOrFail(iterable $entities, array $options = [])
 */
class BookingsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('bookings');
        $this->setDisplayField('title');
        $this->setPrimaryKey('id');

        $this->belongsTo('Customers', [
            'foreignKey' => 'customer_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Courses', [
            'foreignKey' => 'course_id',
        ]);
        // $this->belongsTo('Classes', [
        //     'foreignKey' => 'class_id',
        // ]);
        // $this->belongsTo('ClassTimeSlots', [
        //     'foreignKey' => 'class_time_slot_id',
        // ]);
        $this->belongsTo('Partners', [
            'foreignKey' => 'partner_id',
            'joinType' => 'INNER',
        ]);
        // $this->belongsTo('Discounts', [
        //     'foreignKey' => 'discount_id',
        // ]);
        $this->hasMany('BookingAddons', [
            'foreignKey' => 'booking_id',
        ]);
        // $this->hasMany('BookingDetails', [
        //     'foreignKey' => 'booking_id',
        // ]);
        $this->hasMany('BookingItems', [
            'foreignKey' => 'booking_id',
        ]);
        $this->hasMany('Cancellations', [
            'foreignKey' => 'booking_id',
        ]);
        $this->hasMany('PartnerSettlements', [
            'foreignKey' => 'booking_id',
        ]);
        $this->hasMany('Payments', [
            'foreignKey' => 'booking_id',
        ]);

        $this->belongsTo('CourseBatches', [
            'foreignKey' => 'batch_id'
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('group_booking_id')
            ->allowEmptyString('group_booking_id');

        $validator
            ->nonNegativeInteger('customer_id')
            ->notEmptyString('customer_id');

        $validator
            ->nonNegativeInteger('course_id')
            ->allowEmptyString('course_id');

        $validator
            ->nonNegativeInteger('partner_id')
            ->notEmptyString('partner_id');

        $validator
            ->nonNegativeInteger('batch_id')
            ->allowEmptyString('batch_id');

        $validator
            ->date('booking_date')
            ->requirePresence('booking_date', 'create')
            ->notEmptyDate('booking_date');

        $validator
            ->scalar('booking_status')
            ->notEmptyString('booking_status');

        $validator
            ->scalar('billing_currency')
            ->maxLength('billing_currency', 255)
            ->allowEmptyString('billing_currency');

        $validator
            ->decimal('tax_amount')
            ->allowEmptyString('tax_amount');

        $validator
            ->decimal('tax_rate')
            ->allowEmptyString('tax_rate');

        $validator
            ->decimal('sub_total')
            ->requirePresence('sub_total', 'create')
            ->notEmptyString('sub_total');

        $validator
            ->decimal('grand_total')
            ->allowEmptyString('grand_total');

        $validator
            ->scalar('payment_status')
            ->allowEmptyString('payment_status');

        $validator
            ->dateTime('created_at')
            ->notEmptyDateTime('created_at');

        $validator
            ->dateTime('modified_at')
            ->notEmptyDateTime('modified_at');

        return $validator;
    }

    
    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['customer_id'], 'Customers'), ['errorField' => 'customer_id']);
        $rules->add($rules->existsIn(['course_id'], 'Courses'), ['errorField' => 'course_id']);
        // $rules->add($rules->existsIn(['class_id'], 'Classes'), ['errorField' => 'class_id']);
        // $rules->add($rules->existsIn(['class_time_slot_id'], 'ClassTimeSlots'), ['errorField' => 'class_time_slot_id']);
        $rules->add($rules->existsIn(['partner_id'], 'Partners'), ['errorField' => 'partner_id']);
        // $rules->add($rules->existsIn(['discount_id'], 'Discounts'), ['errorField' => 'discount_id']);

        return $rules;
    }
}