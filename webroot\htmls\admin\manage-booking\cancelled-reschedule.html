<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>

<body>
    <section class="group-booking grp-booking-body-content">
        <div class="container mx-auto px-4">
            <div class="flex items-center space-x-4 p-6 bg-[] grp-booking-body">
                <!-- Image -->
                <img src="https://www.shutterstock.com/shutterstock/videos/1074813608/thumb/1.jpg" alt="Yoga Group"
                    class="w-24 h-24 rounded-lg object-cover" />

                <!-- Text Content -->
                <div class="content">
                    <!-- Organizer -->
                    <p class="text-sm text-[#D87A61] font-medium">@The Art of Living</p>

                    <!-- Title -->
                    <h2 class="text-xl md:text-2xl font-bold text-gray-800">
                        200-Hour Yoga Teacher Training in India
                    </h2>

                    <!-- Info Row -->
                    <div class="flex items-center mt-2 space-x-6 text-sm text-gray-700 time-info hidden sm:block">
                        <!-- Time -->
                        <div class="flex items-center space-x-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                                fill="none">
                                <path
                                    d="M14.6673 7.99967C14.6673 11.6797 11.6807 14.6663 8.00065 14.6663C4.32065 14.6663 1.33398 11.6797 1.33398 7.99967C1.33398 4.31967 4.32065 1.33301 8.00065 1.33301C11.6807 1.33301 14.6673 4.31967 14.6673 7.99967Z"
                                    stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                                <path opacity="0.4"
                                    d="M10.4739 10.1202L8.40724 8.88684C8.04724 8.6735 7.75391 8.16017 7.75391 7.74017V5.00684"
                                    stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <span><span class="italic font-semibold">Mon - Fri : 10:00 AM to 6:00 PM</span></span>
                        </div>

                        <!-- Language -->
                        <div class="flex items-center space-x-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                                fill="none">
                                <path
                                    d="M8.00065 14.6663C11.6825 14.6663 14.6673 11.6816 14.6673 7.99967C14.6673 4.31778 11.6825 1.33301 8.00065 1.33301C4.31875 1.33301 1.33398 4.31778 1.33398 7.99967C1.33398 11.6816 4.31875 14.6663 8.00065 14.6663Z"
                                    stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                                <g opacity="0.4">
                                    <path d="M5.33372 2H6.00039C4.70039 5.89333 4.70039 10.1067 6.00039 14H5.33372"
                                        stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                                    <path d="M10 2C11.3 5.89333 11.3 10.1067 10 14" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path d="M2 10.6667V10C5.89333 11.3 10.1067 11.3 14 10V10.6667" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path d="M2 6.00039C5.89333 4.70039 10.1067 4.70039 14 6.00039" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                </g>
                            </svg>
                            <span class="italic font-semibold">English</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content block sm:hidden ">
                <!-- Info Row -->
                <div class="flex items-center text-sm text-gray-600 mt-1 date-content justify-between py-2">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M5.33398 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10"
                                stroke-linecap="round" stroke-linejoin="round" />
                            <path d="M10.666 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10"
                                stroke-linecap="round" stroke-linejoin="round" />
                            <path opacity="0.4" d="M2.33398 6.05957H13.6673" stroke="#C45F44" stroke-miterlimit="10"
                                stroke-linecap="round" stroke-linejoin="round" />
                            <path
                                d="M14.6673 12.6667C14.6673 13.1667 14.5273 13.64 14.2807 14.04C13.8207 14.8133 12.974 15.3333 12.0007 15.3333C11.3273 15.3333 10.714 15.0867 10.2473 14.6667C10.0407 14.4933 9.86065 14.28 9.72065 14.04C9.47399 13.64 9.33398 13.1667 9.33398 12.6667C9.33398 11.1933 10.5273 10 12.0007 10C12.8007 10 13.514 10.3533 14.0007 10.9067C14.414 11.38 14.6673 11.9933 14.6673 12.6667Z"
                                stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path d="M10.9609 12.667L11.6209 13.327L13.0409 12.0137" stroke="#C45F44"
                                stroke-linecap="round" stroke-linejoin="round" />
                            <path
                                d="M14 5.66634V10.9063C13.5133 10.353 12.8 9.99967 12 9.99967C10.5267 9.99967 9.33333 11.193 9.33333 12.6663C9.33333 13.1663 9.47333 13.6397 9.72 14.0397C9.86 14.2797 10.04 14.493 10.2467 14.6663H5.33333C3 14.6663 2 13.333 2 11.333V5.66634C2 3.66634 3 2.33301 5.33333 2.33301H10.6667C13 2.33301 14 3.66634 14 5.66634Z"
                                stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path opacity="0.4" d="M7.99764 9.13314H8.00363" stroke="#C45F44" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path opacity="0.4" d="M5.52889 9.13314H5.53488" stroke="#C45F44" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path opacity="0.4" d="M5.5293 11.1338H5.53528" stroke="#C45F44" stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                        <span class="date pl-2 italic font-bold text-[#000000]">06 Mar 2025 - 06 Apr 2025</span>
                    </div>
                    <span class="ml-4 flex items-center date">
                        <span class="px-2 text-[#00AE4D] text-[10px] border border-[#00AE4D] rounded-full font-bold">2
                            Days to Go</span>
                    </span>
                </div>
            </div>
        </div>
    </section>

    <section class="select-branch bg-[] select-classes">
        <div class="container mx-auto  px-4">
            <!-- Feedback Text Area -->
            <div class="mb-4">
                <label class="block mb-1 font-medium sub-title pt-5 pb-2 text-[#231F20]">Cancellation Remarks</label>
                <textarea class="w-full border border-[#E6E7EA] rounded-md p-2" rows="3"
                    placeholder="Add feedback"></textarea>
            </div>
        </div>
    </section>

<div class="container mx-auto px-4">
        <!-- Submit Button -->
        <button class="w-full bg-[#D87A61] hover:bg-[#D87A61] text-white font-semibold py-2 rounded-md mt-6">
            Cancel
        </button>
    </div>
</body>


<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

    .group-booking .cards .date-content .date {
        font-size: 11px;
        font-style: italic;
        font-family: "Open Sans", sans-serif;
        font-weight: 600;
        color: #000000;
    }

    .group-booking h2 {
        font-family: "Open Sans", sans-serif;
        font-size: 40px;
        font-weight: 700;
        color: #293148;
    }

    .group-booking .content p {
        font-family: "Open Sans", sans-serif;
        font-size: 18px;
        font-weight: 600;
    }

    .time-info .italic {
        font-family: "Open Sans", sans-serif;
        font-style: italic;
        font-size: 20px;
        font-weight: 600;
    }

    .grp-booking-body {
        border-bottom: 1px solid #C45F44;
        padding: 40px 0px;
    }

    /* section1 */
    /* section2 */
    .select-branch h3 {
        font-family: "Open Sans", sans-serif;
        font-size: 32px;
        font-weight: 700;
    }

    .select-branch .select-date {
        font-family: "Open Sans", sans-serif;
        font-size: 20px;
        font-weight: 700;

    }

    .select-branch .slot-title {
        padding: 0px !important;
    }

    @media only screen and (max-width: 700px) {
        .group-booking h2 {
            font-size: 18px;
        }
.select-classes .select-branch-grid-content{
    padding-bottom: 5px;
}
        .select-branch .slot-date {
            font-size: 14px !important;
            color: #231F204D !important;
            font-weight: 600;
            padding: 10px 10px 10px 15% !important;
        }

        input[type="date"]::-webkit-calendar-picker-indicator {
            opacity: 0;
            display: none;
        }

        .group-booking .content p {
            font-family: "Poppins", sans-serif;
            font-size: 10px;
            font-weight: 600;
        }

        .group-booking .cards .date-content .date {
            font-size: 11px;
            font-style: italic;
            font-family: "Open Sans", sans-serif;
            font-weight: 600;
            color: #000000;
        }

        .select-branch h3 {
            font-family: "Poppins", sans-serif;
            padding: 30px 0px 0px;
            font-size: 14px;
            font-weight: 500;
        }

        .select-branch .select-date {
            font-size: 10px;
            padding: 10px 5px;
            color: #000000;
        }

        .select-branch .select-date:hover {
            font-size: 10px;
            background: #C45F44;
            color: #FFFFFF;
            padding: 10px 5px;
        }

        .grp-booking-body {
            border-bottom: 0px solid #C45F44;
            padding: 40px 0px 0px;
        }

        .grp-booking-body-content {
            border-bottom: 2px solid #C45F44;
            padding: 0px 0px 20px;
        }

        .time-info .italic {
            font-size: 11px;
        }

        .select-branch-grid-content {
            padding: 0px 0px 30px;
        }

        .Participants h1 {
            font-size: 18px;
            font-weight: 700;
        }

        .Participants .head .increment-decrement {
            width: 110px;
            background: #FDEDE0;
            border-radius: 12px;
            padding: 5px;
            margin: 0px;
            justify-content: space-between;
        }

        .Participants {
            background: #FFFFFF;
        }

        .Participants .head .increment-decrement span {
            margin: 0px 5px;
            line-height: 12px;
        }

        .Participants .head .increment-decrement #decreaseBtn,
        .Participants .head .increment-decrement #increaseBtn {
            height: 23px;
            width: 23px !important;
            margin: 0px;
            font-weight: 600;
            line-height: 12px;
            padding-bottom: 3px;
        }

        .Participants #accordionContainer .rounded-md {
            border-radius: 11px;
            padding: 10px;
            font-size: 15px;
            font-weight: 600;
        }

        .form-content-title {
            font-size: 14px;
            font-weight: 400;
        }

        .form-content {
            justify-content: space-between;
        }

        .form-content input,
        .form-content select {
            border: 1px solid #6C6C6C;
            border-radius: 20px;
            background: #fff;
            padding: 13px 5px;
        }

        .form-content select {
            color: #00000033
        }

        .form-content .select-state {
            border: 1px solid #6C6C6C;
            border-radius: 20px;
            background: #fff;
            padding: 5px 10px;
        }

        .form-phone .code {
            border-top-left-radius: 20px;
            border-bottom-left-radius: 20px;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .form-phone .phone {
            border-top-right-radius: 20px;
            border-bottom-right-radius: 20px;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        .form-content .country {
            font-size: 16px;
            font-weight: 500;
            color: #344054;
            font-family: "Inter", sans-serif;
        }

        .head-sec-title {
            font-size: 18px;
            font-weight: 700;
            color: #6A9E74;
            text-transform: capitalize;
        }

        .cost-price {
            font-size: 18px;
            font-weight: 700;
            font-family: "Open Sans", sans-serif;

        }

        .cost-title {
            font-family: "Open Sans", sans-serif;
            font-size: 12px;
            font-weight: 600;
            color: #344054;
        }

        .item-left {
            font-family: "Open Sans", sans-serif;
            font-size: 12px;
            font-style: italic;
            font-weight: 600;
            color: #00AE4D;
        }

        .full-booked {
            font-family: "Open Sans", sans-serif;
            font-size: 12px;
            font-style: italic;
            font-weight: 600;
            color: #000000;
        }

        .billingCurrency-body label {
            font-size: 14px;
            font-weight: 600;
            font-family: "Open Sans", sans-serif;
            color: #000000;
        }

        .billingCurrency-body .relative select {
            font-size: 14px;
            font-weight: 700;
            font-family: "Open Sans", sans-serif;
            color: #000000;
        }

        .exc-rate .title {
            font-style: italic;
            font-size: 12px;
            font-weight: 600;
            font-family: "Open Sans", sans-serif;
            text-decoration: underline;
        }

        .exc-rate .list-rate {
            font-style: italic;
            font-size: 12px;
            font-weight: 600;
            color: #000000;
        }

        .exc-rate .Disclaimer {
            color: #888888;
            font-size: 8px;
            font-weight: 600;
            font-style: italic;
        }

        .billingCurrency-element {
            border-radius: 6px;
        }

        .billed .title {
            font-size: 14px;
            font-weight: 400;
            font-family: "Open Sans", sans-serif;
        }

        .billed .body select {
            font-size: 15px;
            color: #983419;
            font-weight: 600;
        }

        .privacy-policy p {
            color: #000000;
            font-size: 10px;
            font-family: "Poppins", sans-serif;
        }

        .privacy-policy a {
            font-weight: 500;
            color: #0F43CA;
            font-size: 10px;
            font-family: "Poppins", sans-serif;
        }

        .payment-summary button {
            font-size: 15px;
            font-weight: 700;
            color: #283148;
            font-family: "Open Sans", sans-serif;
        }

        #payment-summary-content h2 {
            font-size: 14px;
            color: #000000;
            font-weight: 700;
            font-family: "Open Sans", sans-serif;
        }

        .about-user {
            font-size: 12px;
            color: #727272;
            font-weight: 600;
            font-family: "Open Sans", sans-serif;
        }

        .cost-breakdown {
            font-style: italic;
            font-size: 14px;
            font-weight: 400;
            font-family: "Open Sans", sans-serif;
        }

        .cost-breakdown .text-red-600 {
            color: #62200E;
            font-weight: 600;
        }

        .cost-breakdown .discount-applied .text-red-600 {
            color: #FF0000;
            font-weight: 600;
        }

        .cost-breakdown .new {
            font-weight: 700;
            font-family: "Open Sans", sans-serif;
        }

        .cost-breakdown .total .text-red-600 {
            font-weight: 700;
        }

        .discount button {
            color: #479456;
        }

        .total-before-tax span {
            font-weight: 600;
            font-size: 14px;
            font-family: "Open Sans", sans-serif;
            color: #000000;
        }

        .total-before-tax .value {
            font-weight: 700;
            font-size: 16px;
            font-family: "Open Sans", sans-serif;
            color: #000000;
        }

        .Applicable-tax .value {
            font-weight: 700;
            font-size: 12px;
            font-family: "Open Sans", sans-serif;
            color: #505050;
        }
    }

    @media only screen and (max-width: 390px) {
        .Participants .head .increment-decrement span {
            margin: 0px 5px;
            line-height: 12px;
            font-size: 17px;
        }
    }
</style>

</html>