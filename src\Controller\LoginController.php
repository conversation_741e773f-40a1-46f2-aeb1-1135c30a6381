<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Event\EventInterface;
use Cake\Utility\Security;
use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\Http\Cookie\Cookie;
use Cake\Log\Log;
use Cake\Routing\Router;
use DateTime;
use Cake\Core\Configure;
use Cake\Validation\Validation;

class LoginController extends AppController
{
    protected $UsersTable;

    public function initialize(): void
    {
        parent::initialize();
        $this->loadComponent('Flash');
        $this->UsersTable = $this->fetchTable('Users');
        $this->loadComponent('Global');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions([
            'index', 'login', 'socialLogin', 'socialLoginCallback', 'customerLogin', 'verifyOtp', 'sendOtp', 'verifyOtpForgot',
        ]);
    }

    public function index()
    {
        $identity = $this->Authentication->getResult();
        if ($identity && $identity->isValid()) {
            return $this->redirect($this->referer());
        }

        if ($this->request->is('post')) {
            $result = $this->Authentication->getResult();
            if ($result && $result->isValid()) {
                $redirect = $this->request->getQuery('redirect');
                if ($redirect) {
                    return $this->redirect(urldecode($redirect));
                }
                // Default redirect after login
                return $this->redirect(['controller' => 'Home', 'action' => 'index']);
            }
            $this->Flash->error(__('Invalid username or password, try again'));
        }

        $qryRedirect = $this->request->getQuery('redirect');
        $this->viewBuilder()->setLayout('webauth');
        $login = $this->UsersTable->newEmptyEntity();
        $this->set(compact('login', 'qryRedirect'));
    }

    public function login()
    {
        // Redirect to customerLogin for all login requests
        return $this->redirect(['action' => 'customerLogin']);
    }

    public function customerLogin()
    {
        $this->viewBuilder()->setLayout('webauth');
        $isAjax = $this->isAjaxRequest();

        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $input = trim($data['email_or_mobile'] ?? '');
            $password = $data['password'] ?? '';
            $rememberMe = !empty($data['remember_me']);

            $response = [
                'success' => false,
                'message' => 'Invalid login credentials'
            ];

            if (empty($input) || empty($password)) {
                $response['message'] = 'Please enter email/mobile and password.';
                return $this->renderLoginResponse($response, $isAjax);
            }

            $query = $this->UsersTable->find('all', [
                'conditions' => ['status' => 'A', 'user_type' => 'Customer']
            ]);

            if (filter_var($input, FILTER_VALIDATE_EMAIL)) {
                $query->where(['email' => $input]);
            } else {
                $query->where(['mobile' => $input]);
            }

            $user = $query->first();

            if ($user) {
                $defaultHasherCheck = (new DefaultPasswordHasher())->check($password, $user->password);
                $passwordVerifyCheck = password_verify($password, $user->password);

                if ($defaultHasherCheck || $passwordVerifyCheck) {
                    $this->request->getSession()->write('Auth', $user);

                    if ($rememberMe) {
                        $encryptedPassword = Security::encrypt($password, Security::getSalt());
                        $cookie = new Cookie(
                            'remember_customer',
                            json_encode([
                                'email_or_mobile' => $input,
                                'password' => base64_encode($encryptedPassword)
                            ]),
                            new DateTime('+30 days'),
                            '/'
                        );
                        $this->response = $this->response->withCookie($cookie);
                    }

                    $redirectUrl = Router::url(['controller' => 'Home', 'action' => 'index'], true);
                    if (!empty($data['reqdirect_url'])) {
                        $redirectUrl = Router::url($data['reqdirect_url']);
                    }

                    $response = [
                        'success' => true,
                        'message' => 'Login successful!',
                        'redirect' => $redirectUrl
                    ];
                } else {
                    $response['message'] = 'Invalid email/mobile or password.';
                }
            } else {
                $response['message'] = 'The provided email address or mobile number is not registered with us.';
            }

            return $this->renderLoginResponse($response, $isAjax);
        }

        // If "Remember Me" cookie is set
        $remember = $this->request->getCookie('remember_customer');
        if ($remember) {
            $rememberData = json_decode($remember, true);
            if (is_array($rememberData)) {
                $this->set('rememberData', [
                    'email_or_mobile' => $rememberData['email_or_mobile'] ?? '',
                    'password' => $rememberData['password'] ?? ''
                ]);
            }
        }

        // Set qryRedirect for the template
        $qryRedirect = $this->request->getQuery('redirect');
        $this->set('qryRedirect', $qryRedirect);

        return $this->render('index');
    }

    private function renderLoginResponse(array $response, bool $isAjax)
    {
        if ($isAjax) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        if ($response['success']) {
            return $this->redirect($response['redirect']);
        }

        $this->Flash->error($response['message']);
        return $this->redirect(['action' => 'index']);
    }

    public function socialLogin()
    {
        $this->request->allowMethod(['get']);
        
        $provider = $this->request->getQuery('provider');
        
        if (empty($provider) || !in_array($provider, ['google', 'facebook'])) {
            $this->Flash->error('Invalid social provider.');
            return $this->redirect(['action' => 'index']);
        }

        // Get site URL from settings
        $siteUrl = Configure::read('Settings.SITE_URL');
        $redirectUri = rtrim($siteUrl, '/') . '/login/social/' . $provider . '/callback';
        
        if ($provider === 'google') {
            $clientId = Configure::read('Settings.SOCIAL_AUTH_GOOGLE_CLIENT_ID');
            $clientSecret = Configure::read('Settings.SOCIAL_AUTH_GOOGLE_CLIENT_SECRET');
            
            if (empty($clientId) || empty($clientSecret)) {
                $this->Flash->error('Google OAuth is not configured properly.');
                return $this->redirect(['action' => 'index']);
            }
            
            $authUrl = 'https://accounts.google.com/o/oauth2/auth?' . http_build_query([
                'client_id' => $clientId,
                'redirect_uri' => $redirectUri,
                'scope' => 'email profile',
                'response_type' => 'code',
                'access_type' => 'offline',
                'prompt' => 'consent'
            ]);
            
            return $this->redirect($authUrl);
        }
        
        if ($provider === 'facebook') {
            $clientId = Configure::read('Settings.SOCIAL_AUTH_FB_CLIENT_ID');
            $clientSecret = Configure::read('Settings.SOCIAL_AUTH_FB_CLIENT_SECRET');
            
            if (empty($clientId) || empty($clientSecret)) {
                $this->Flash->error('Facebook OAuth is not configured properly.');
                return $this->redirect(['action' => 'index']);
            }
            
            $authUrl = 'https://www.facebook.com/v18.0/dialog/oauth?' . http_build_query([
                'client_id' => $clientId,
                'redirect_uri' => $redirectUri,
                'scope' => 'email',
                'response_type' => 'code'
            ]);
            
            return $this->redirect($authUrl);
        }

        $this->Flash->error('Social login provider not supported.');
        return $this->redirect(['action' => 'index']);
    }

    public function socialLoginCallback($provider = null)
    {
        // Try to get provider from route params if not passed directly
        if (empty($provider)) {
            $provider = $this->request->getParam('provider');
        }
        
        // Also try getting from URL path parsing as fallback
        if (empty($provider)) {
            $path = $this->request->getUri()->getPath();
            if (preg_match('/\/login\/social\/(\w+)\/callback/', $path, $matches)) {
                $provider = $matches[1];
            }
        }
        
        if (empty($provider) || !in_array($provider, ['google', 'facebook'])) {
            $this->Flash->error('Invalid social provider.');
            return $this->redirect(['action' => 'index']);
        }

        $code = $this->request->getQuery('code');
        $error = $this->request->getQuery('error');

        if ($error || empty($code)) {
            $this->Flash->error('Social login was cancelled or failed.');
            return $this->redirect(['action' => 'index']);
        }

        try {
            // Get site URL from settings
            $siteUrl = Configure::read('Settings.SITE_URL');
            $redirectUri = rtrim($siteUrl, '/') . '/login/social/' . $provider . '/callback';
            
            if ($provider === 'google') {
                $userInfo = $this->handleGoogleCallback($code, $redirectUri);
            } else {
                $userInfo = $this->handleFacebookCallback($code, $redirectUri);
            }

            if (!$userInfo) {
                $this->Flash->error('Failed to get user information from ' . ucfirst($provider) . '.');
                return $this->redirect(['action' => 'index']);
            }

            // Check if user already exists
            $existingUser = $this->UsersTable->find()
                ->where(['email' => $userInfo['email'], 'user_type' => 'Customer', 'status !=' => 'D'])
                ->first();

            $connection = $this->UsersTable->getConnection();
            $connection->begin();

            try {
                if ($existingUser) {
                    $user = $existingUser;
                } else {
                    // Create new user
                    $userData = [
                        'first_name' => $userInfo['first_name'] ?? '',
                        'last_name' => $userInfo['last_name'] ?? '',
                        'email' => $userInfo['email'],
                        'password' => (new DefaultPasswordHasher())->hash(Security::randomString(16)),
                        'social_provider' => $provider,
                        'socialID' => $userInfo['id'],
                        'profile_pic' => $userInfo['picture'] ?? null,
                        'role_id' => 2,
                        'status' => 'A',
                        'user_type' => 'Customer'
                    ];

                    $user = $this->UsersTable->newEmptyEntity();
                    $user = $this->UsersTable->patchEntity($user, $userData);

                    if (!$this->UsersTable->save($user)) {
                        throw new \Exception('Failed to create user account: ' . json_encode($user->getErrors()));
                    }

                    // Create customer record
                    $customersTable = $this->fetchTable('Customers');
                    $customer = $customersTable->newEmptyEntity();
                    $customer = $customersTable->patchEntity($customer, [
                        'user_id' => $user->id,
                        'status' => 'A'
                    ]);

                    if (!$customersTable->save($customer)) {
                        throw new \Exception('Failed to create customer record: ' . json_encode($customer->getErrors()));
                    }
                }

                $connection->commit();
                
                // Set user identity for authentication
                $this->Authentication->setIdentity($user);
                $this->request->getSession()->write('Auth', $user);

                $this->Flash->success('Successfully logged in with ' . ucfirst($provider) . '!');
                return $this->redirect(['controller' => 'Home', 'action' => 'index']);
                
            } catch (\Exception $e) {
                $connection->rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            $this->Flash->error('Social login failed: ' . $e->getMessage());
            return $this->redirect(['action' => 'index']);
        }
    }

    private function handleGoogleCallback($code, $redirectUri)
    {
        $clientId = Configure::read('Settings.SOCIAL_AUTH_GOOGLE_CLIENT_ID');
        $clientSecret = Configure::read('Settings.SOCIAL_AUTH_GOOGLE_CLIENT_SECRET');

        // Exchange code for access token
        $tokenUrl = 'https://oauth2.googleapis.com/token';
        $tokenData = [
            'client_id' => $clientId,
            'client_secret' => $clientSecret,
            'code' => $code,
            'grant_type' => 'authorization_code',
            'redirect_uri' => $redirectUri
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $tokenUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($tokenData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
        
        $tokenResponse = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        if ($curlError || $httpCode !== 200) {
            return false;
        }

        $tokenData = json_decode($tokenResponse, true);
        if (!isset($tokenData['access_token'])) {
            return false;
        }

        // Get user info
        $userInfoUrl = 'https://www.googleapis.com/oauth2/v2/userinfo?access_token=' . $tokenData['access_token'];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $userInfoUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $userResponse = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($curlError || $httpCode !== 200) {
            return false;
        }

        $userInfo = json_decode($userResponse, true);
        
        return [
            'id' => $userInfo['id'],
            'email' => $userInfo['email'],
            'first_name' => $userInfo['given_name'] ?? '',
            'last_name' => $userInfo['family_name'] ?? '',
            'picture' => $userInfo['picture'] ?? null
        ];
    }

    private function handleFacebookCallback($code, $redirectUri)
    {
        $clientId = Configure::read('Settings.SOCIAL_AUTH_FB_CLIENT_ID');
        $clientSecret = Configure::read('Settings.SOCIAL_AUTH_FB_CLIENT_SECRET');

        // Exchange code for access token
        $tokenUrl = 'https://graph.facebook.com/v18.0/oauth/access_token?' . http_build_query([
            'client_id' => $clientId,
            'client_secret' => $clientSecret,
            'code' => $code,
            'redirect_uri' => $redirectUri
        ]);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $tokenUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $tokenResponse = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            return false;
        }

        $tokenData = json_decode($tokenResponse, true);
        if (!isset($tokenData['access_token'])) {
            return false;
        }

        // Get user info
        $userInfoUrl = 'https://graph.facebook.com/v18.0/me?fields=id,name,email,first_name,last_name,picture&access_token=' . $tokenData['access_token'];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $userInfoUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $userResponse = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            return false;
        }

        $userInfo = json_decode($userResponse, true);
        
        return [
            'id' => $userInfo['id'],
            'email' => $userInfo['email'] ?? '',
            'first_name' => $userInfo['first_name'] ?? '',
            'last_name' => $userInfo['last_name'] ?? '',
            'picture' => $userInfo['picture']['data']['url'] ?? null
        ];
    }
    /**
     * Helper to detect AJAX or JSON request
     */
    private function isAjaxRequest(): bool
    {
        return $this->request->is('ajax') ||
            $this->request->getHeader('X-Requested-With') === ['XMLHttpRequest'] ||
            $this->request->getHeader('Accept') === ['application/json'];
    }

    /**
     * Send OTP for login
     */
    public function sendOtp()
    {

        $this->request->allowMethod(['post']);


        if ($this->request->is('ajax')) {
            $this->viewBuilder()->setLayout('ajax');
            $response = ['success' => false, 'message' => '', 'input_type' => ''];

            $data = $this->request->getData();

            $emailOrMobile = trim($data['email_or_mobile'] ?? '');

            if (empty($emailOrMobile)) {
                $response['message'] = 'Email or mobile number is required.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Determine if input is email or mobile
            $isEmail = filter_var($emailOrMobile, FILTER_VALIDATE_EMAIL);
            $isMobile = preg_match('/^[0-9]{10}$/', $emailOrMobile);

            if (!$isEmail && !$isMobile) {
                $response['message'] = $isEmail === false && strpos($emailOrMobile, '@') !== false
                    ? 'Please enter a valid email address.'
                    : 'Please enter a valid 10-digit mobile number.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            try {
                $usersTable = \Cake\ORM\TableRegistry::getTableLocator()->get('Users');
                $conditions = $isEmail ? ['email' => $emailOrMobile, 'user_type' => 'Customer']
                    : ['mobile' => $emailOrMobile, 'country_code' => '91', 'user_type' => 'Customer'];

                $user = $usersTable->find()->where($conditions)->first();
                if (!$user) {
                    $response['message'] = 'No account found with this ' . ($isEmail ? 'email address' : 'mobile number') . '.';
                    return $this->response->withType('application/json')
                        ->withStringBody(json_encode($response));
                }

                $otp = sprintf('%04d', mt_rand(0, 9999));
                if ($isMobile) {
                    try {
                        $twoFactorService = new \App\Service\TwoFactorService();
                        $sendResult = $twoFactorService->sendOtp($emailOrMobile);

                        if ($sendResult['success']) {
                            $this->request->getSession()->write('login_otp', [
                                'email_or_mobile' => $emailOrMobile,
                                'user_id' => $user->id,
                                'session_id' => $sendResult['session_id'],
                                'expires' => time() + 300 // 5 minutes expiry
                            ]);

                            $response['success'] = true;
                            $response['message'] = 'OTP has been sent to your mobile number.';
                            $response['input_type'] = 'mobile';
                        } else {
                            $response['message'] = 'Failed to send OTP: ' . ($sendResult['message'] ?? 'Unknown error');
                        }
                    } catch (\Exception $e) {
                        $response['message'] = 'Failed to send OTP: ' . $e->getMessage();
                    }
                } else {
                    $this->request->getSession()->write('login_otp', [
                        'email_or_mobile' => $emailOrMobile,
                        'user_id' => $user->id,
                        'otp' => $otp,
                        'expires' => time() + 300 // 5 minutes expiry
                    ]);

                    // Try both email sending methods
                    $emailResult = $this->sendEmailOtp($emailOrMobile, $otp);

                    if ($emailResult) {
                        $response['success'] = true;
                        $response['message'] = 'OTP has been sent to your email address.';
                        $response['input_type'] = 'email';
                    } else {
                        $response['message'] = 'Failed to send OTP email. Please try again.';
                    }
                }
            } catch (\Exception $e) {
                $response['message'] = 'An error occurred: ' . $e->getMessage();
            }
            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        return $this->redirect(['action' => 'index']);
    }


    public function verifyOtp()
    {
        $this->request->allowMethod(['post']);
        if (
            $this->request->is('ajax') ||
            $this->request->getHeader('X-Requested-With') === ['XMLHttpRequest'] ||
            $this->request->getHeader('Accept') === ['application/json']
        ) {

            $this->viewBuilder()->setLayout('ajax');
            $response = ['success' => false, 'message' => ''];

            try {
                $data = $this->request->getData();
                $otp = $data['otp'] ?? '';
                $emailOrMobile = $data['email_or_mobile'] ?? '';


                if (empty($otp) || empty($emailOrMobile)) {
                    $response['message'] = 'All fields are required.';
                    return $this->response->withType('application/json')
                        ->withStringBody(json_encode($response));
                }

                // Get stored OTP data from session
                $storedOtpData = $this->request->getSession()->read('login_otp');

                if (!$storedOtpData || $storedOtpData['email_or_mobile'] !== $emailOrMobile || time() > $storedOtpData['expires']) {
                    $response['message'] = 'Invalid or expired OTP session. Please try again.';
                    return $this->response->withType('application/json')
                        ->withStringBody(json_encode($response));
                }

                $isVerified = false;

                // Determine if we're verifying via 2Factor API or local OTP
                if (isset($storedOtpData['session_id'])) {
                    // Verify via 2Factor API
                    $twoFactorService = new \App\Service\TwoFactorService();
                    $verifyResult = $twoFactorService->verifyOtp($storedOtpData['session_id'], $otp);
                    $isVerified = $verifyResult['success'];
                } else {
                    // Verify local OTP
                    $isVerified = $storedOtpData['otp'] === $otp;
                }

                if (!$isVerified) {
                    $response['message'] = 'Invalid OTP. Please try again.';
                    return $this->response->withType('application/json')
                        ->withStringBody(json_encode($response));
                }

                // OTP is valid, login user
                $usersTable = \Cake\ORM\TableRegistry::getTableLocator()->get('Users');
                $user = $usersTable->get($storedOtpData['user_id']);
                // Set user identity for authentication
                $this->Authentication->setIdentity($user);

                // Clear the OTP session data
                $this->request->getSession()->delete('login_otp');

                $response['success'] = true;
                $response['message'] = 'Login successful!';
                $response['redirect'] = Router::url(['controller' => 'Home', 'action' => 'index'], true);

                if ($data['reqdirect_url']) {
                    $response['redirect'] = Router::url($data['reqdirect_url']);
                }

                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            } catch (\Exception $e) {

                $response['message'] = 'An error occurred: ' . $e->getMessage();
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }
        }

        // For non-AJAX requests, redirect
        $this->Flash->error('Invalid request method.');
        return $this->redirect(['action' => 'index']);
    }


    private function sendEmailOtp($emailOrMobile, $otp)
    {
        try {

            // Try using the Global component if available
            if (method_exists($this, 'loadComponent') && !isset($this->Global)) {
                $this->loadComponent('Global');
            }

            if (isset($this->Global) && method_exists($this->Global, 'send_email')) {

                $viewVars = [
                    'otp' => $otp,
                    'datetime' => date('Y-m-d H:i:s')
                ];

                $result = $this->Global->send_email(
                    $emailOrMobile,
                    Configure::read('Settings.FROM_EMAIL'),
                    'Your OTP for Yoga.in Login',
                    'otp_email_login',
                    $viewVars
                );

                return $result;
            } else {

                $email = new \Cake\Mailer\Mailer('default');
                $email->setFrom([Configure::read('Settings.FROM_EMAIL') => 'Yoga.in'])
                    ->setTo($emailOrMobile)
                    ->setSubject('Your OTP for Yoga.in Login')
                    ->setEmailFormat('html')  // Changed from 'both' to 'html'
                    ->setViewVars(['otp' => $otp])
                    ->viewBuilder()
                    ->setTemplate('otp_email_login');

                $result = $email->deliver();
                return $result;
            }
        } catch (\Exception $e) {
            return false;
        }
    }


    public function logout()
    {
        // Clear all authentication-related sessions
        $this->request->getSession()->delete('Auth');

        // For CakePHP Authentication plugin compatibility
        $result = $this->Authentication->getResult();
        if ($result && $result->isValid()) {
            $this->Authentication->logout();
        }

        // Completely destroy the session for a clean logout
        $this->request->getSession()->destroy();

        // Destroy the remember me cookie if it exists
        $cookie = new Cookie(
            'remember_customer',
            '',
            new DateTime('-1 day'),
            '/'
        );
        $this->response = $this->response->withCookie($cookie);

        // Flash message (optional)
        // $this->Flash->success('You have been logged out successfully.');

        // Redirect to home page or login page
        return $this->redirect(['controller' => 'Home', 'action' => 'index']);
    }

     // Forgot Password 
    public function forgotPassword()
    {
        $this->viewBuilder()->setLayout('auth');
        $isMobile = false;
        $input = '';
        
        if ($this->request->is('post')) {
            $userTable = $this->UsersTable;
            $input = $this->request->getData('email_mobile');
            $user = null;
         
            // Detect if input is email or mobile
            if (Validation::email($input)) {
                $user = $userTable->find()
                    ->where(['email' => $input, 'user_type' => 'Customer'])
                    ->first();

            } elseif (preg_match('/^[0-9]{10}$/', $input)) {
                $isMobile = true;
                $user = $userTable->find()
                    ->where(['mobile' => $input, 'user_type' => 'Customer'])
                    ->first();
            } else {
                $message = 'Enter a valid email or 10-digit mobile number.';
                $messageKey = 'error';
            }

             if (!isset($message)) {
                // if forgot password with mobile then it should send OTP //
                if ($user) {
                    if($isMobile){
                        try {
                            $twoFactorService = new \App\Service\TwoFactorService();
                            $sendResult = $twoFactorService->sendOtp($input);

                            if ($sendResult['success']) {
                                $this->request->getSession()->write('login_otp', [
                                    'email_or_mobile' => $input,
                                    'user_id' => $user->id,
                                    'session_id' => $sendResult['session_id'],
                                    'expires' => time() + 300 // 5 minutes expiry
                                ]);

                                $this->Flash->set(__('OTP has been sent to your mobile number.'), [
                                    'element' =>'success'
                                ]);
                              
                                //return $this->redirect($this->request->getRequestTarget());

                            } else {
                                $message = 'Failed to send OTP: ' . ($sendResult['message'] ?? 'Unknown error');
                                $this->Flash->set(__($message), [
                                    'element' =>'error'
                                ]);
                                //return $this->redirect($this->request->getRequestTarget());
                            }
                          
                        } catch (\Exception $e) {
                            $message = 'Failed to send OTP: ' . $e->getMessage();
                            $messageKey = 'error';   
                        }

                    } else {

                        $result = $this->Global->forgotPassword($user, $userTable);

                        if(!empty($result['message'])){
                            $this->Flash->set(__($result['message']), [
                                'element' => $result['messageKey']
                            ]);
                        }
                    }
                    
                } else {
                    $isMobile = false;
                    $message = 'Entered email/mobile is not registered with us.';
                    $messageKey = 'error';
                }
            }
            if(isset($message) && isset($messageKey)){
                $this->Flash->set(__($message), [
                    'element' => $messageKey
                ]);
            }
        }
        
        $this->set(compact('isMobile', 'input'));
    }

    public function verifyOtpForgot(){
        $data = $this->request->getData();
        $mobile = trim($data['email_mobile']);
        $otp = trim($data['otp']);
        // Get stored OTP data from session
        $storedOtpData = $this->request->getSession()->read('login_otp');
    
        if (!$storedOtpData || $storedOtpData['email_or_mobile'] !== $mobile || time() > $storedOtpData['expires']) {
            $this->Flash->set(__('Invalid or expired OTP session. Please try again.'), [
                'element' => 'error'
            ]);
            //return $this->redirect($this->request->getRequestTarget());
        }

        $isVerified = false;

        // Determine if we're verifying via 2Factor API or local OTP
        if (isset($storedOtpData['session_id'])) {
          
            // Verify via 2Factor API
            $twoFactorService = new \App\Service\TwoFactorService();
            $verifyResult = $twoFactorService->verifyOtp($storedOtpData['session_id'], $otp);
            $isVerified = $verifyResult['success'];
          
            $user = $this->UsersTable->find()
                    ->where(['mobile' => $mobile, 'user_type' => 'Customer'])
                    ->first();

            $token = Security::hash(Security::randomBytes(25));
            $user->reset_token = $token;
            $user->reset_token_expiry = date('Y-m-d H:i:s');

            if ($this->UsersTable->save($user)) {
                $resetLink = Router::url(['controller' => 'Login', 'action' => 'resetPassword', $token, 'lang' => false ], true);
                return  $this->redirect($resetLink);

            } else {
             
                $this->Flash->set(__('Unable to process your request. Please try again.'), [
                    'element' => 'error'
                ]);
            }

        } else {
            // Verify local OTP
            $isVerified = $storedOtpData['otp'] === $otp;
        }

        if (!$isVerified) {
            $this->Flash->set(__('Invalid OTP. Please try again.'), [
                'element' => 'error'
            ]);
          //  return $this->redirect($this->request->getRequestTarget());
        }
    }

    // Reset Password
    public function resetPassword($token = null)
    {
        $this->viewBuilder()->setLayout('auth');
        $userTable = $this->UsersTable;
 
        $result = $this->Global->resetPassword($token, $this->request, $userTable);

        if(!empty($result['message'])){
            $this->Flash->set(__($result['message']), [
                'element' => $result['messageKey']
            ]);
        }

        if(!empty($result['redirect'])){
            if(!$result['adminUser'] && $result['redirect'] == 'login'){
                $this->redirect(['controller' => 'Login', 'action' => 'index']);
            }

            $this->redirect(['action' => $result['redirect']]);
        }

        $this->set(compact('token'));
    }
}
