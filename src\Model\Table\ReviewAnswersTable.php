<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\ORM\TableRegistry;

class ReviewAnswersTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('review_answers');
        $this->setPrimaryKey('id');
        $this->addBehavior('Timestamp');

        $this->belongsTo('Reviews');
        $this->belongsTo('ReviewQuestions', [
            'foreignKey' => 'question_id',
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('rating')
            ->allowEmptyString('rating');

        $validator
            ->allowEmptyString('comment');

        return $validator;
    }
}
