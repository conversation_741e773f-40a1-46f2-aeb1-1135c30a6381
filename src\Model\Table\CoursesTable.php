<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\I18n\DateTime;
use Cake\Database\Expression\QueryExpression;
use Cake\Database\Query;
use Cake\I18n\Date;

/**
 * Courses Model
 *
 * @property \App\Model\Table\CitiesTable&\Cake\ORM\Association\BelongsTo $Cities
 * @property \App\Model\Table\StatesTable&\Cake\ORM\Association\BelongsTo $States
 * @property \App\Model\Table\CountriesTable&\Cake\ORM\Association\BelongsTo $Countries
 * @property \App\Model\Table\PartnerTypesTable&\Cake\ORM\Association\BelongsTo $PartnerTypes
 * @property \App\Model\Table\BookingsTable&\Cake\ORM\Association\HasMany $Bookings
 * @property \App\Model\Table\CartItemsTable&\Cake\ORM\Association\HasMany $CartItems
 * @property \App\Model\Table\CourseYogaStylesTable&\Cake\ORM\Association\HasMany $CourseYogaStyles
 * @property \App\Model\Table\WishlistItemsTable&\Cake\ORM\Association\HasMany $WishlistItems
 * @property \App\Model\Table\CourseBatchesTable&\Cake\ORM\Association\HasMany $CourseBatches
 *
 * @method \App\Model\Entity\Course newEmptyEntity()
 * @method \App\Model\Entity\Course newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Course> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Course get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Course findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Course patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Course> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Course|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Course saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Course>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Course>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Course>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Course> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Course>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Course>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Course>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Course> deleteManyOrFail(iterable $entities, array $options = [])
 */
class CoursesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('courses');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->belongsTo('CourseTypes', [
            'foreignKey' => 'course_type_id',
        ]);
        // $this->belongsTo('SpecialNeeds', [
        //     'foreignKey' => 'special_need_id',
        // ]);

        $this->hasMany('CourseYogaStyles', [
            'foreignKey' => 'course_id',
        ]);

        $this->belongsToMany('YogaStyleMasterData', [
            'className' => 'MasterData',
            'joinTable' => 'course_yoga_styles',
            'foreignKey' => 'course_id',
            'targetForeignKey' => 'yoga_style_id',
            'conditions' => ['YogaStyleMasterData.type' => 'yoga_style']
        ]);
        
        $this->hasMany('CourseSpecialNeeds', [
            'foreignKey' => 'course_id',
        ]);

        $this->belongsToMany('SpecialNeedMasterData', [
            'className' => 'MasterData',
            'joinTable' => 'course_special_needs',
            'foreignKey' => 'course_id',
            'targetForeignKey' => 'special_need_id',
            'conditions' => ['SpecialNeedMasterData.type' => 'special_need']
        ]);
        
        $this->belongsToMany('SpecialNeeds', [
            'foreignKey' => 'course_id',
            'targetForeignKey' => 'special_need_id',
            'joinTable' => 'course_special_needs', // adjust if your table is named differently
        ]);

        $this->belongsTo('Cities', [
            'foreignKey' => 'city_id',
        ]);
        $this->belongsTo('States', [
            'foreignKey' => 'state_id',
        ]);
        $this->belongsTo('Countries', [
            'foreignKey' => 'country_id',
        ]);
        $this->belongsTo('Localities', [
            'foreignKey' => 'locality_id',
        ]);
        $this->belongsTo('Partners', [
            'foreignKey' => 'partner_id',
        ]);
        // $this->belongsTo('PartnerTypes', [
        //     'foreignKey' => 'partner_type_id',
        // ]);
        $this->hasMany('Bookings', [
            'foreignKey' => 'course_id',
        ]);
        $this->hasMany('CartItems', [
            'foreignKey' => 'course_id',
        ]);
    
        $this->hasMany('WishlistItems', [
            'foreignKey' => 'course_id',
        ]);
        $this->hasMany('CourseGalleries', [
            'foreignKey' => 'course_id',
            'dependent' => true
        ]);
        $this->hasMany('ReviewRatings', [
            'foreignKey'=> 'course_id'
        ]);
        // remove after all updates with master data //
        $this->belongsToMany('YogaStyles', [
            'foreignKey' => 'course_id',
            'targetForeignKey' => 'yoga_style_id',
            'joinTable' => 'course_yoga_styles',
        ]);

        $this->hasMany('CourseBatches', [
            'foreignKey' => 'course_id',
            'dependent' => true,
            'cascadeCallbacks' => true,
        ]);
    
        $this->hasMany('CourseAddons', [
            'foreignKey' => 'course_id',
            'saveStrategy' => 'replace',
            'dependent' => true,
        ]);

        $this->hasMany('CourseModalities', [
            'foreignKey' => 'course_id',
            'saveStrategy' => 'replace',
            'dependent' => true,
        ]);
        // $this->CourseAddons->hasMany('CourseAddonPricing', [
        //     'foreignKey' => 'course_addon_id',
        //     'saveStrategy' => 'replace',
        //     'dependent' => true,
        // ]);
        
        $this->belongsToMany('Modalities', [
            'joinTable' => 'courses_modalities'
        ]);

        $this->hasMany('CourseBasePrices', [
            'foreignKey' => 'course_id',
            'saveStrategy' => 'replace', // or append, depending on your logic
            'dependent' => true,
            'cascadeCallbacks' => true
        ]);

        $this->belongsToMany('Teachers', [
            'foreignKey' => 'course_id',
            'targetForeignKey' => 'teacher_id',
            'joinTable' => 'course_teachers',
        ]);      

        // Has many course-teachers entries
        // $this->hasMany('CourseTeachers', [
        //     'foreignKey' => 'course_id',
        //     'dependent' => true,
        //     'cascadeCallbacks' => true,
        // ]);

        $this->hasMany('MasterData', [
            'foreignKey' => 'yoga_style_id',
        ]);
    }

    // Basic validation
    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
         $validator
            ->scalar('name')
            ->maxLength('name', 255)
            ->requirePresence('name', 'create')
            ->notEmptyString('name');
            
        $validator
            ->scalar('slug')
            ->maxLength('slug', 255)
            ->requirePresence('slug', 'create')
            ->notEmptyString('slug')
            ->add('slug', 'unique', [
                'rule' => 'validateUnique',
                'provider' => 'table',
                'message' => 'This slug is already in use. Please choose another one.'
            ]);

        $validator
            ->nonNegativeInteger('partner_id')
            ->requirePresence('partner_id', 'create', 'Partner is required.');

        $validator
            ->nonNegativeInteger('course_type_id')
            ->requirePresence('course_type_id', 'create', 'Course Type is required.');
       
        $validator
            ->scalar('short_description')
            ->requirePresence('short_description', 'create')
            ->notEmptyString('short_description')
            ->minLength('short_description', 80, __('Short description must be at least 80 characters'))
            ->maxLength('short_description', 180, __('Short description must not exceed 180 characters'));

        $validator
            ->scalar('desc1_label')
            ->allowEmptyString('desc1_label');

        $validator
            ->scalar('desc1_text')
            ->allowEmptyString('desc1_text');
        
        $validator
            ->scalar('desc2_label')
            ->allowEmptyString('desc2_label');

        $validator
            ->scalar('desc2_text')
            ->allowEmptyString('desc2_text');

        $validator
            ->scalar('faq')
            ->allowEmptyString('faq');

        $validator
            ->scalar('refund_policy')
            ->allowEmptyString('refund_policy');

        $validator
            ->scalar('latitude')
            ->allowEmptyString('latitude');

        $validator
            ->scalar('longitude')
            ->allowEmptyString('longitude');

        $validator
            ->scalar('language')
            ->maxLength('language', 255)
            ->allowEmptyString('language');

        $validator
            ->email('email')
            ->allowEmptyString('email');

        $validator
            ->scalar('phone')
            ->maxLength('phone', 50)
            ->allowEmptyString('phone');

        $validator
            ->scalar('whatsapp')
            ->maxLength('whatsapp', 50)
            ->allowEmptyString('whatsapp');

        $validator
            ->scalar('banner_image')
            ->maxLength('banner_image', 255)
            ->allowEmptyFile('banner_image');

        $validator
            ->scalar('address')
            ->maxLength('address', 255)
            ->allowEmptyString('address');

        $validator
            ->boolean('is_venue_different')
            ->notEmptyString('is_venue_different');

        $validator
            ->nonNegativeInteger('city_id')
            ->allowEmptyString('city_id');

        $validator
            ->nonNegativeInteger('state_id')
            ->allowEmptyString('state_id');

        $validator
            ->nonNegativeInteger('country_id')
            ->allowEmptyString('country_id');

        $validator
            ->nonNegativeInteger('modality_id')
            ->allowEmptyString('on_site');
    
        // $validator
        //     ->scalar('mode')
        //     ->requirePresence('mode', 'create')
        //     ->notEmptyString('mode');

        $validator
            ->boolean('women_only')
            ->notEmptyString('women_only');

        $validator
            ->boolean('is_accommodation_included')
            ->notEmptyString('is_accommodation_included');

        $validator
            ->boolean('is_food_included')
            ->notEmptyString('is_food_included');

        $validator
            ->allowEmptyString('level');

        $validator
            ->boolean('is_featured')
            ->notEmptyString('is_featured');

        $validator
            ->boolean('has_certification')
            ->notEmptyString('has_certification');

        $validator
            ->scalar('old_url')
            ->maxLength('old_url', 255)
            ->allowEmptyString('old_url');

        $validator
            ->scalar('meta_title')
            ->maxLength('meta_title', 255)
            ->allowEmptyString('meta_title');

        $validator
            ->scalar('meta_description')
            ->allowEmptyString('meta_description');

        $validator
            ->scalar('meta_keywords')
            ->allowEmptyString('meta_keywords');
        
        $validator
            ->scalar('meta_robots')
            ->allowEmptyString('meta_robots');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        $validator
            ->dateTime('modified_at')
            ->notEmptyDateTime('modified_at');

        $validator
            ->dateTime('created_at')
            ->notEmptyDateTime('created_at');

        return $validator;
    }

    public function validationFull(Validator $validator): Validator
    {
         $validator
            ->scalar('name')
            ->maxLength('name', 255)
            ->requirePresence('name', 'create')
            ->notEmptyString('name');
            
        $validator
            ->scalar('slug')
            ->maxLength('slug', 255)
            ->requirePresence('slug', 'create')
            ->notEmptyString('slug')
            ->add('slug', 'unique', [
                'rule' => 'validateUnique',
                'provider' => 'table',
                'message' => 'This slug is already in use. Please choose another one.'
            ]);

        $validator
            ->nonNegativeInteger('partner_id')
            ->requirePresence('partner_id', 'create', 'Partner is required.');

        $validator
            ->nonNegativeInteger('course_type_id')
            ->requirePresence('course_type_id', 'create', 'Course Type is required.');
       
        $validator
            ->scalar('short_description')
            ->notEmptyString('short_description');

        $validator
            ->scalar('desc1_label')
            ->allowEmptyString('desc1_label');

        $validator
            ->scalar('desc1_text')
            ->allowEmptyString('desc1_text');
        
        $validator
            ->scalar('desc2_label')
            ->allowEmptyString('desc2_label');

        $validator
            ->scalar('desc2_text')
            ->allowEmptyString('desc2_text');

        $validator
            ->scalar('faq')
            ->allowEmptyString('faq');

        $validator
            ->scalar('refund_policy')
            ->allowEmptyString('refund_policy');

         $validator
            ->scalar('duration_details')
            ->notEmptyString('duration_details');

        $validator
            ->scalar('latitude')
            ->allowEmptyString('latitude');

        $validator
            ->scalar('longitude')
            ->allowEmptyString('longitude');

        $validator
            ->scalar('language')
            ->maxLength('language', 255)
            ->allowEmptyString('language');

        $validator
            ->email('email')
            ->allowEmptyString('email');

        $validator
            ->scalar('phone')
            ->maxLength('phone', 50)
            ->allowEmptyString('phone');

        $validator
            ->scalar('whatsapp')
            ->maxLength('whatsapp', 50)
            ->allowEmptyString('whatsapp');

        $validator
            ->scalar('banner_image')
            ->maxLength('banner_image', 255)
            ->allowEmptyFile('banner_image');

        $validator
            ->scalar('address')
            ->maxLength('address', 255)
            ->allowEmptyString('address');

        $validator
            ->boolean('is_venue_different')
            ->notEmptyString('is_venue_different');

        $validator
            ->nonNegativeInteger('city_id')
            ->requirePresence('city_id', 'create', 'City is required.');

        $validator
            ->nonNegativeInteger('state_id')
            ->requirePresence('state_id', 'create', 'State is required.');

        $validator
            ->nonNegativeInteger('country_id')
             ->requirePresence('country_id', 'create', 'Country is required.');

        $validator
            ->nonNegativeInteger('modality_id')
            ->allowEmptyString('on_site');
    
        // $validator
        //     ->scalar('mode')
        //     ->requirePresence('mode', 'create')
        //     ->notEmptyString('mode');

        $validator
            ->boolean('women_only')
            ->notEmptyString('women_only');

        $validator
            ->boolean('is_accommodation_included')
            ->notEmptyString('is_accommodation_included');

        $validator
            ->boolean('is_food_included')
            ->notEmptyString('is_food_included');

        $validator
            ->allowEmptyString('level');

        $validator
            ->boolean('is_featured')
            ->notEmptyString('is_featured');

        $validator
            ->boolean('has_certification')
            ->notEmptyString('has_certification');

        $validator
            ->scalar('old_url')
            ->maxLength('old_url', 255)
            ->allowEmptyString('old_url');

        $validator
            ->scalar('meta_title')
            ->maxLength('meta_title', 255)
            ->allowEmptyString('meta_title');

        $validator
            ->scalar('meta_description')
            ->allowEmptyString('meta_description');

        $validator
            ->scalar('meta_keywords')
            ->allowEmptyString('meta_keywords');
        
        $validator
            ->scalar('meta_robots')
            ->allowEmptyString('meta_robots');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        $validator
            ->dateTime('modified_at')
            ->notEmptyDateTime('modified_at');

        $validator
            ->dateTime('created_at')
            ->notEmptyDateTime('created_at');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->isUnique(['slug']), ['errorField' => 'slug']);
        // $rules->add($rules->existsIn(['city_id'], 'Cities'), ['errorField' => 'city_id']);
        // $rules->add($rules->existsIn(['state_id'], 'States'), ['errorField' => 'state_id']);
        // $rules->add($rules->existsIn(['country_id'], 'Countries'), ['errorField' => 'country_id']);
        // $rules->add($rules->existsIn(['partner_id'], 'Partners'), ['errorField' => 'partner_id']);
        // $rules->add($rules->existsIn(['course_type_id'], 'PartnerTypes'), ['errorField' => 'course_type_id']);

        return $rules;
    }

    public function getFeaturedCousres(){
        $result = $this->find()
        ->contain([
            'Partners' => ['PartnerTypes'],
            'States'
        ])
        ->where([
            'Courses.is_featured' => 1,
            //'Courses.is_opened' => 1,
            'Courses.status' => 'A'
        ])
        ->orderDesc('Courses.id')
        ->all();

        return  $result;
    }

    public function getFeaturedCoursesHome(){
        $result = $this->find()
        ->contain([
            'Partners' => ['PartnerTypes'],
            'Countries',
            'States'=>['Regions'],
            'Cities',
            'CourseBatches',
            'Modalities'
        ])
        ->where([
            'Courses.is_featured' => 1,
            'Courses.status' => 'A'
        ])
        ->orderDesc('Courses.id')
        ->limit(4)
        ->all();

        return  $result;
    }

    public function getList($filters){
        $search     = $filters['search'];
        $courseType = $filters['course_type'];
        $yogaStyle  = $filters['yoga_style'];
        $location   = $filters['location'];
        $courseDate= $filters['course_date'];
        $language   = $filters['language'];
        $site       = $filters['site'];
        $mode       = $filters['mode'];
        $sort       = $filters['sort'];
        $specialNeeds= $filters['special_needs'];
        $region     = $filters['region'];
        // today date //
        $today = (new DateTime())->format('Y-m-d');
       
        $query = $this->find()
        ->contain([
            'Countries',
            'States' => ['Regions'], 
            'Cities',
            'Localities',
            'Partners' => ['PartnerTypes'],
            'ReviewRatings',
            'Bookings',
            'CourseBatches',
            'YogaStyleMasterData',
            'SpecialNeedMasterData',
            'Modalities',
            'CourseBasePrices'
            ])
        // ->leftJoinWith('ReviewRatings')
        ->leftJoinWith('Bookings') 
        ->leftJoinWith('CourseBasePrices')
        ->select($this) // Select all course fields
        ->select($this->Partners)
        ->select($this->Countries)
        ->select($this->Cities)
        ->select($this->States)
        ->select($this->States->Regions)
        ->select($this->Localities)
        ->select([
            'bookings_count' => 'COUNT(Bookings.id)',
            'CourseBasePrices__price' => 'CourseBasePrices.price',
        ])
        // ->matching('CourseBatches', function ($q) use ($today) {
        //     return $q->where(['CourseBatches.start_date >' => $today]);
        // })
        ->distinct(['Courses.id']); // Important when using matching()

        if($search){
            $query->where(['Courses.name like' => '%'.trim($search).'%']);
        }

        if(!empty($courseType) && $courseType != 'all'){
            $types = is_array($courseType) ? $courseType : explode(',', (string)$courseType);
            $types = array_filter($types);
            $types = array_map('trim', $types);

            $ctypes = $this->CourseTypes
              ->find('list', [
                'keyField' => 'name',   // key by name
                'valueField' => 'id'    // get the id
            ])
            ->where(['slug IN' => $types])
            ->toArray();

            if (!empty($ctypes)) {
                $query->where(['Courses.course_type_id IN' => array_values($ctypes)]);
            }
        }

        // Filter by yoga style (many-to-many)
  
        if (!empty($yogaStyle) && $yogaStyle != 'all') {
            $styles = is_array($yogaStyle) ? $yogaStyle : explode(',', (string)$yogaStyle);
            $styles = array_filter($styles);

            $query->matching('YogaStyleMasterData', function ($q) use ($styles) {
                return $q->where(['YogaStyleMasterData.id IN' => $styles]);
            });
        }

        if(!empty($specialNeeds) && $specialNeeds != 'all'){
            $special = is_array($specialNeeds) ? $specialNeeds : explode(',', (string)$specialNeeds);
            $special = array_filter($special);
            if(count($special)){
                $query->matching('SpecialNeedMasterData', function ($q) use ($special) {
                    return $q->where(['SpecialNeedMasterData.id IN' => $special]);
                });
            }
        }
        
        if (!empty($region) && $region != 'all') {
            $regionName = str_replace('-', ' ' ,trim($region));
        
            // Get region IDs matching the region name
            $regionIds = $this->States->Regions->find()
                ->select(['id'])
                ->where(['Regions.name LIKE' => '%' . $regionName . '%'])
                ->enableHydration(false)
                ->all()
                ->extract('id')
                ->toArray();
            
            if (!empty($regionIds)) {
                // Get state IDs for those regions
                $stateIds = $this->States->find()
                    ->select(['id'])
                    ->where(['region_id IN' => $regionIds])
                    ->enableHydration(false)
                    ->all()
                    ->extract('id')
                    ->toArray();

                if (!empty($stateIds)) {
                    // Filter courses whose state_id is in those states
                    $query->where(['Courses.state_id IN' => $stateIds]);
                }
            }
        }

        if (!empty($location)) {
            $location =  trim($location);
            $conditions = [];

            $countryIds = $this->Countries->find()
                ->select(['id'])
                ->where(['name LIKE' => '%' . $location . '%'])
                ->enableHydration(false)
                ->all()
                ->extract('id')
                ->toArray();
        
            if (!empty($countryIds)) {
                $conditions[] = ['Courses.country_id IN' => $countryIds];
            }
        
            $stateIds = $this->States->find()
                ->select(['id'])
                ->where(['name LIKE' => '%' . $location . '%'])
                ->enableHydration(false)
                ->all()
                ->extract('id')
                ->toArray();
        
            if (!empty($stateIds)) {
                $conditions[] = ['Courses.state_id IN' => $stateIds];
            }
    
            $cityIds = $this->Cities->find()
                ->select(['id'])
                ->where(['name LIKE' => '%' . $location . '%'])
                ->enableHydration(false)
                ->all()
                ->extract('id')
                ->toArray();
        
            if (!empty($cityIds)) {
                $conditions[] = ['Courses.city_id IN' => $cityIds];
            }
        
            $conditions[] = ['Courses.address LIKE' => '%' . $location . '%'];
        
            // Apply OR conditions
            $query->where(function ($exp, $q) use ($conditions) {
                return $exp->or($conditions);
            });
        }

       if(!empty($courseDate)){
            $start_dates = array_map('urldecode', explode(',', $courseDate));
            $startDates = array_filter($start_dates);
            $today = new \DateTime();
            $monthConditions = [];

            foreach ($startDates as $monthYear) {
                $date = DateTime::createFromFormat('F Y', $monthYear);
                if ($date) {
                    $start = $date->format('Y-m-01');
                    $end = $date->format('Y-m-t'); // Last day of month
                    $monthConditions[] = [
                        'CourseBatches.start_date >=' => $start,
                        'CourseBatches.start_date <=' => $end
                    ];
                }
            }

            if (!empty($monthConditions)) {
                $query->matching('CourseBatches', function ($q) use ($monthConditions, $today) {
                    return $q->where([
                        'OR' => $monthConditions,
                        'CourseBatches.start_date >=' => $today->format('Y-m-d')
                    ]);
                });
            }
        }
            
        if(!empty($language) && $language != 'all'){
            $languages = array_map('urldecode', explode(',', $language));
            $languages = array_filter($languages);

            $query->where(['Courses.language IN' => $languages]);
        }

        if (!empty($mode)) {
            $modeIds = is_array($mode) ? $mode : explode(',', (string)$mode);
            if (!empty($modeIds)) {
                $query->matching('CourseModalities', function ($q) use ($modeIds) {
                    return $q->where(['CourseModalities.modality_id IN' => $modeIds]);
                });
            }
        }
        
        $query->where([
            'Courses.status' => 'A'
        ]);

       
        switch ($sort) {
            case 'featured':
                $query->order(['Courses.is_featured' => 'DESC']);
                break;
            case 'popular':
                $query->order(['bookings_count' => 'DESC']);
                break;
            case 'rating':
                $query->order(['ReviewRatings.rating' => 'DESC']);
                break;
            case 'newest':
                $query->order(['Courses.created_at' => 'DESC']);
                break;
            case 'price_asc':
                $query->order(['CourseBasePrices.price' => 'ASC']);
                break;
            case 'price_desc':
                $query->order(['CourseBasePrices.price' => 'DESC']);
                break;
            default:
                $query->order(['Courses.created_at' => 'ASC']);
                break;
        }

        return  $query;
        
    }

    public function getFilterCounts($baseFilters)
    {
        $results = [];

        // Prepare each filter type
        $yogaStyles =  $this->MasterData->getYogaStyleList();
        $courseTypes = $this->CourseTypes->getList();
        $specialNeeds = $this->MasterData->getspecialNeedList();
        $languages = $this->find()->distinct(['language'])->all()->extract('language')->toArray();
 
        // Site type: On Site
        $siteTypes = ['on_site' => 1];

        // Modes
        $modes = $this->Modalities->selectInputOptions();

        // Course Batch dates (unique year-month from start_date)
       $dateStrings = $this->CourseBatches
            ->find()
            ->select(['start_date'])
            ->where(['CourseBatches.status' => 'active'])
            ->enableHydration(false)
            ->all()
            ->extract(function ($row) {
                return !empty($row['start_date']) ? $row['start_date']->format('F Y') : null;
            })
            ->toArray();

        $courseDates = array_unique($dateStrings);

        // === YOGA STYLES ===
        $styleCounts = [];
        foreach ($yogaStyles as $id => $name) {
            $filters = $baseFilters;
            $filters['yoga_style'] = $id;
            $styleCounts[$id] = $this->getList($filters)->count();
        }
        $results['yoga_styles'] = $styleCounts;

        // === COURSE TYPES ===
        $typeCounts = [];
        foreach ($courseTypes as $id => $name) {
            $filters = $baseFilters;
            $filters['course_type'] = $id;
            $typeCounts[$id] = $this->getList($filters)->count();
        }
        $results['course_types'] = $typeCounts;

        // === LANGUAGES ===
        $langCounts = [];
        foreach ($languages as $lang) {
            $filters = $baseFilters;
            $filters['language'] = $lang;
            $langCounts[$lang] = $this->getList($filters)->count();
        }
        $results['languages'] = $langCounts;

        // === SPECIAL NEEDS ===
        $specialCounts = [];
        foreach ($specialNeeds as $id => $needName) {
            $filters = $baseFilters;
            $filters['special_needs'] = $id;
            $specialCounts[$id] = $this->getList($filters)->count();
        }
        $results['special_needs'] = $specialCounts;

        // === SITE TYPES ===
        // $siteCounts = [];
        // foreach ($siteTypes as $key => $value) {
        //     $filters = $baseFilters;
        //     $filters['site'] = $key;
        //     $siteCounts[$key] = $this->getList($filters)->count();
        // }
        // $results['site_types'] = $siteCounts;

        // === COURSE DATES ===
        $dateCounts = [];
        foreach ($courseDates as $dateStr) {
            if ($dateStr) {
                $filters = $baseFilters;
                $filters['course_date'] = urlencode($dateStr);
                $dateCounts[$dateStr] = $this->getList($filters)->count();
            }
        }

        $results['course_dates'] = $dateCounts;

        // === MODES ===
        $modeCounts = [];
        foreach ($modes as $id =>$mode) {
            $filters = $baseFilters;
            $filters['mode'] = $id;
            $modeCounts[$id] = $this->getList($filters)->count();
        }
     
        $results['modes'] = $modeCounts;

        return $results;
    }

    // function getPopularCourses(){
    //     $popularCourses = $this->find()
    //     ->select([
    //         'Courses.id',
    //         'Courses.name',
    //         'Courses.slug',
    //         'total_bookings' => $this->Bookings->find()
    //             ->select(['count' => 'COUNT(Bookings.id)'])
    //             ->where(['Bookings.course_id = Courses.id'])
    //             ->limit(1) 
    //     ])
    //     ->contain([])
    //     ->enableAutoFields(true)
    //     ->orderDesc('total_bookings')
    //     ->limit(5)
    //     ->all();

    //     return $popularCourses;
    // }
 
    public function findBySlug($slug){
        $course = $this->find()
         ->contain([
            'Partners',
            'Countries',
            'States',
            'Cities',
            'CourseBatches' => function ($q) {
                return $q->where([
                    'CourseBatches.end_date >=' => Date::today()
                ])
                ->order([
                    'CourseBatches.start_date' => 'ASC'
                ]);
            },
            'CourseAddons.CourseAddonPricing.Currencies',
            'CourseAddons.MasterData',
            'CourseBasePrices.Currencies'
            ])
        ->where(['Courses.slug' => $slug])
        ->first();
        return $course;
    }
}




