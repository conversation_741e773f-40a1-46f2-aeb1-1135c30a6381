<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="<?= $this->Url->webroot('bundles/ckeditor/ckeditor.js') ?>"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const keywordsTagInput1 = new TagInput('accommodation-tag-input', 'accommodation_options');
    const keywordsTagInput2 = new TagInput('food-tag-input', 'food_options');
});

$(function() {
    CKEDITOR.replace("desc1_text");
    CKEDITOR.config.height = 300;
    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].on('blur', function() {
            CKEDITOR.instances[this.name].updateElement();
        });
    }
});

$(function() {
    CKEDITOR.replace("desc2_text");
    CKEDITOR.config.height = 300;
    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].on('blur', function() {
            CKEDITOR.instances[this.name].updateElement();
        });
    }
});

$(function() {
    CKEDITOR.replace("desc3_text");
    CKEDITOR.config.height = 300;
    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].on('blur', function() {
            CKEDITOR.instances[this.name].updateElement();
        });
    }
});

$(function() {
    CKEDITOR.replace("refund_policy");
    CKEDITOR.config.height = 300;

    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].on('blur', function() {
            CKEDITOR.instances[this.name].updateElement();
        });
    }
});

$(function() {
    CKEDITOR.replace("faq");
    CKEDITOR.config.height = 300;

    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].on('blur', function() {
            CKEDITOR.instances[this.name].updateElement();
        });
    }
});


// $(dcoument).ready(function(){
    $('#country_id').change(function() {
        var countryId = $(this).val();
        $('#state_id').html('<option value="">Loading...</option>');
        $('#city_id').html('<option value="">Select City</option>');

        $.ajax({
            url: "<?= $this->Url->build(['action' => 'getStates']) ?>",
            data: {
                country_id: countryId
            },
            success: function(response) {
                var options = '<option value="">Select State</option>';
                 $.each(response.states, function(key, value) {
                    options += `<option value="${value.id}">${value.name}</option>`;
                });
                $('#state_id').html(options);
            }
        });
    });

    $('#state_id').change(function() {
        var stateId = $(this).val();
        $('#city_id').html('<option value="">Loading...</option>');

        $.ajax({
            url: "<?= $this->Url->build(['action' => 'getCities']) ?>",
            data: {
                state_id: stateId
            },
            success: function(response) {
                var options = '<option value="">Select City</option>';
                $.each(response.cities, function(key, value) {
                    options += `<option value="${value.id}">${value.name}</option>`;
                });
                $('#city_id').html(options);
            }
        });
    });
     $('#city_id').change(function() {
        var cityId = $(this).val();
        $('#locality_id').html('<option value="">Loading...</option>');

        $.ajax({
            url: "<?= $this->Url->build(['action' => 'getLocalities']) ?>",
            data: { city_id: cityId },
            success: function(response) {
                var options = '<option value="">Select Locality</option>';
                $.each(response.localities, function(key, value) {
                    options += `<option value="${value.id}">${value.name}</option>`;
                });
                $('#locality_id').html(options);
            }
        });
    });
// })

// First define the custom validation method
$.validator.addMethod('checkDuplicateCourseName', function(value, element) {
    let isValid = false;
    const recordId = $('#record_id').val();

    $.ajax({
        url:  "<?= $this->Url->build(['controller'=> 'Courses', 'action' => 'checkDuplicateCourseName']) ?>",
        method: 'POST',
        dataType: 'json',
        data: {
            name: value,
            id: recordId
        },
        async: false,
        headers: {
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        success: function(response) {
            isValid = !response.isDuplicate;
        },
        error: function() {
            isValid = false;
        }
    });

    return isValid;
}, 'This name is already in use. Please enter another one.');

// Add custom validation method for date comparison
$.validator.addMethod("greaterThan", function(value, element, param) {
    let startDate = $(param).val();
    let endDate = value;

    if (startDate && endDate) {
        return new Date(endDate) > new Date(startDate);
    }
    return true;
}, "<?= __('End date must be greater than start date') ?>");

// Before validate() or inside rules
$.validator.addMethod("select2Required", function(value, element, params) {
    return $(element).val() != null && $(element).val().length > 0;
}, "<?= __('Please select yoga style') ?>");

// Custom validator to check at least one checkbox in the group is checked

$.validator.addMethod('acceptimg', function(value, element, param) {
    var allowedExtensions = param.split(',');
    var files = element.files;
    if (files.length === 0) return true;
    for (var i = 0; i < files.length; i++) {
        var fileExtension = files[i].name.split('.').pop().toLowerCase();
        if ($.inArray(fileExtension, allowedExtensions) === -1) {
            return false;
        }
    }
    return true;
}, '<?= __("Please select a valid file type.") ?>');

$.validator.addMethod('filesize', function(value, element, param) {
    var files = element.files;
    if (files.length === 0) return true;
    for (var i = 0; i < files.length; i++) {
        if (files[i].size > param) {
            return false;
        }
    }
    return true;
}, '<?= __("File size must be less than 2 MB") ?>');

let clickedButton = null;

$(document).on('click', '.submitBtn', function () {
    clickedButton = $(this).val(); // 'save' or 'validate'
});

const validator = $("#courseForm").validate({
    ignore: [],
    rules: {
        'name': {
            required: true,
            checkDuplicateCourseName: true
        },
        'slug': {
            required: true
        },
        'partner_id': {
            required: true
        },
        'course_type_id': {
            required: true
        },
        'banner_image': {
            // required: function () {
            //     return clickedButton === 'validate';
            // },
            acceptimg: "jpg,jpeg,png,webp",
            filesize: 2097152
        },
        'short_description': {
            required: true,
            minlength: 80,
            maxlength: 180
        },
        'modality[]': {
            required:true
        },
        'yoga_style_id[]': {
            required: true
        },
        'special_need_id[]': {
            required: true
        },
        'techniques[]': {
            required: true
        },
        'level[]': {
            required: true
        },
        'modality[]': {
            required: true
        },
        'language': {
            required: true
        },
        'country_id': {
            required: function () {
                return clickedButton === 'validate';
            }
        },
        'state_id': {
            required: function () {
                return clickedButton === 'validate';
            }
        },
        'city_id': {
            required: function () {
                return clickedButton === 'validate';
            }
        },
        'address': {
           required: function () {
                return clickedButton === 'validate';
            }
        },
        duration_details: {
            required: function () {
                return clickedButton === 'validate';
            }
        },
        accommodation_options: {
            required: function () {
                return $('#accommodation_included').is(':checked');
            }
        },
        food_options: {
            required: function () {
                return $('#food_included').is(':checked');
            }
        }
    },
    messages: {
        'name': {
            required: "<?= __('Please enter name') ?>"
        },
        'slug': {
            required: "<?= __('Please enter slug') ?>"
        },
        'partner_id': {
            required: "<?= __('Please select center') ?>"
        },
        'course_type_id': {
            required: "<?= __('Please select course type') ?>"
        },
        'banner_image': {
            required: "<?= __('Please select banner image') ?>",
            acceptimg: "<?= __('Only .jpg, .jpeg, .webp and .png files are allowed') ?>",
            filesize: "<?= __('File size must be less than 2 MB') ?>"
        },
        'short_description': {
            required: "<?= __('Please enter short description') ?>",
            minlength: "<?= __('Short description must be at least 80 characters') ?>",
            maxlength: "<?= __('Short description must not exceed 180 characters') ?>"
        },
        'language': {
            required: "<?= __('Please select language') ?>"
        },
        'modality[]': {
            required: "<?= __('Please select modality') ?>"
        },
         'yoga_style_id[]': {
            required: "<?= __('Please select yoga styles') ?>"
        },
        'special_need_id[]': {
            required: "<?= __('Please select special needs') ?>"
        },
        'techniques[]': {
            required: "<?= __('Please select techniques') ?>"
        },
        'level[]': {
            required: "<?= __('Please select level') ?>"
        },
        'country_id': {
            required: "<?= __('Please select country') ?>"
        },
        'state_id': {
            required: "<?= __('Please select state') ?>"
        },
        'city_id': {
            required: "<?= __('Please select city') ?>"
        },
        'address': {
            required: "<?= __('Please enter address') ?>"
        },
        duration_details:{
            required: "<?= __('Please enter course duration details') ?>"
        },
        accommodation_options: {
            required: "Please select a accommodation option if accommodation is included."
        },
        food_options: {
            required: "Please select a food option if food is included."
        }
    },
        invalidHandler: function (event, validator) {
        
        if (validator.errorList.length > 0) {
            const firstInvalidEl = validator.errorList[0].element;
            const tabPane = firstInvalidEl.closest('.tab-pane');

            if (tabPane && tabPane.id) {
                const tabTriggerEl = document.querySelector(`[data-bs-toggle="tab"][href="#${tabPane.id}"]`);
                if (tabTriggerEl) {
                    const tab = new bootstrap.Tab(tabTriggerEl);
                    tab.show();
                }
            }
        }
    },
    errorPlacement: function(error, element) {
        error.appendTo(element.closest(".main-field"));
    }
});

$("#courseForm").on("submit", async function (e) {
    e.preventDefault(); 
    // jQuery Validate rules
    const validator = $("#courseForm").data("validator");
    if (!validator.form()) {
        return false;
    }

    // Sync CKEditor content
    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].updateElement();
    }

    let allValid = true;

    $('.submitBtn').attr('disabled', 'disabled');

    for (const el of document.querySelectorAll('.alpine-section')) {
        const alpineInstance = el._x_dataStack?.[0];

        // on full validation //
        if(clickedButton === 'validate'){
            // Teacher email check
            // if (alpineInstance?.teachers && typeof validateTeacherFormWithEmailCheck === 'function') {
            //     const isEmailValid = await validateTeacherFormWithEmailCheck(alpineInstance);
            //     if (!isEmailValid) {
            //         allValid = false;
            //         swal.fire({
            //         text: "<?= __('Please select or add at least one teacher.') ?>",
            //         icon: "warning"
            //         });

            //         const teacherTab = document.querySelector('#teachers-tab'); 
            //         if (teacherTab) {
            //             const tab = new bootstrap.Tab(teacherTab);
            //             tab.show();
            //         }
            //         el.scrollIntoView({ behavior: 'smooth', block: 'start' });
            //         break;
            //     }
            // }

           // Batch validation
            if (alpineInstance?.validateBatchForm && !alpineInstance.validateBatchForm()) {
                allValid = false;
                swal.fire({
                    text: "<?= __('Please add at least one batch in Schedule & Batches section.') ?>",
                    icon: "warning"
                });
                const batchTab = document.querySelector('#batches-tab'); 
                
                if (batchTab) {
                    const tab = new bootstrap.Tab(batchTab);
                    tab.show();
                }
                el.scrollIntoView({ behavior: 'smooth', block: 'start' });
                break;
            }

            //Base price validation
            if (alpineInstance?.validateBasePriceForm && !alpineInstance.validateBasePriceForm()) {
                allValid = false;
                swal.fire({
                    text: "<?= __('Please add at least one base price in Pricing Section.') ?>",
                    icon: "warning"
                });
                const pricingTab = document.querySelector('#pricing-tab'); 
                if (pricingTab) {
                    const tab = new bootstrap.Tab(pricingTab);
                    tab.show();
                }
                el.scrollIntoView({ behavior: 'smooth', block: 'start' });
                break;
            }
        }
    }

    if (allValid) {
      // this.submit(); // submit after everything is validated
        const form = document.querySelector('#courseForm');
        const formData = new FormData(form);
        
        // Append the batches JSON manually
        // formData.set('course_batches_json', JSON.stringify(this.batches));
        
        fetch(formAction, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    text: data.message || 'Course saved successfully!'
                });
                location.href = "<?= $this->Url->build(['controller'=> 'Courses', 'action' => 'index']) ?>";

            } else {
                $('.submitBtn').attr('disabled', false);
                let errorHtml = '';
                let errorIndex = 1;
               if (data.errors) {
                    errorHtml += '<ul style="text-align: left;">';
                    Object.keys(data.errors).forEach(field => {
                        const fieldErrors = data.errors[field];

                        // Helper function to flatten nested error messages
                        function extractMessages(obj) {
                            let messages = [];
                            if (typeof obj === 'string') {
                                messages.push(obj);
                            } else if (typeof obj === 'object' && obj !== null) {
                                Object.values(obj).forEach(val => {
                                    messages = messages.concat(extractMessages(val));
                                });
                            }
                            return messages;
                        }

                        extractMessages(fieldErrors).forEach(message => {
                            errorHtml += `<li>${message} for Item ${errorIndex++}</li>`;
                        });
                    });
                    errorHtml += '</ul>';
                }

                Swal.fire({
                    icon: 'error',
                    title: data.message || 'Please correct the following errors:',
                    html: errorHtml || 'Something went wrong. Please try again.',
                });
            }
        })
        .catch(error => {
            $('.submitBtn').attr('disabled', false);
           // console.error('AJAX Error:', error);
            Swal.fire({
                icon: 'error',
                text: 'Internal server error occured. Please try again.'
            });
        });
    } else {
        $('.submitBtn').attr('disabled', false);
    }
    
});

document.addEventListener('DOMContentLoaded', function () {

    let batchIndex = <?= count($course->course_batches ?? []) ?>;
    const addbatchbtn = document.getElementById('add-batch-btn');

    if(addbatchbtn){
        addbatchbtn.addEventListener('click', function () {
            let templateHtml = document.getElementById('batch-template').innerHTML;
            let newHtml = templateHtml.replace(/__INDEX__/g, batchIndex);
            let tempDiv = document.createElement('div');
            tempDiv.innerHTML = newHtml;
            document.getElementById('batchAccordion').appendChild(tempDiv.firstElementChild);
            batchIndex++;
        });
    }
    
    document.addEventListener('click', function (e) {
        if (e.target.classList.contains('remove-batch')) {
            e.target.closest('.accordion-item').remove();
        }
    });

     let videoIndex = 2;

    document.getElementById('add-video-link').addEventListener('click', function () {
        const wrapper = document.getElementById('video-links-wrapper');

        const newGroup = document.createElement('div');
        newGroup.className = 'video-link-group mb-2 d-flex gap-2';

        newGroup.innerHTML = `
            <input type="text" name="video_url[]" class="form-control video-link-input" placeholder="Video Link ${videoIndex}">
            <button type="button" class="btn btn-danger btn-sm remove-video-link">Delete</button>
        `;

        wrapper.appendChild(newGroup);
        videoIndex++;
    });

    document.getElementById('video-links-wrapper').addEventListener('click', function (e) {
        if (e.target && e.target.classList.contains('remove-video-link')) {
            e.target.closest('.video-link-group').remove();
        }
    });
    
    var deleteVideoLink = "<?= $this->Url->build(['controller'=> 'Courses', 'action' => 'deleteVideolink']) ?>";

    document.querySelectorAll('.del-video-link').forEach(function (btn) {
        btn.addEventListener('click', function () {
            const row = btn.closest('.video-link-row');
            const id = btn.dataset.id;

            if (id) {
                if (confirm('Are you sure you want to delete this video?')) {
                    
                    fetch(`${deleteVideoLink}/${id}`, {
                        method: 'POST',
                        headers: {
                            'X-CSRF-Token': "<?= $this->request->getAttribute('csrfToken'); ?>",
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    }).then(response => response.json())
                    .then(data => {
                        if (data.success === true) {
                            row.remove();
                        } else {
                            alert('Delete failed.');
                        }
                    });
                }
            } else {
                row.remove(); // remove directly if unsaved
            }
        });
    });
    

    function handleNAExclusive(selectId) {
        const select = document.getElementById(selectId);
        if (!select) return;
     
        // For Select2, listen to its change event
        const eventName = $(select).hasClass('select2-multi') ? 'select2:select select2:unselect' : 'change';

        $(select).on(eventName, function () {
            const options = Array.from(select.options);
            const selected = options.filter(opt => opt.selected).map(opt => opt.value);
            const naOption = options.find(opt => opt.text.trim().toLowerCase() === 'n/a');
            const naValue = naOption ? naOption.value : null;

            if (!naValue) return;

            // If N/A is selected with others, keep only N/A
            if (selected.includes(naValue) && selected.length > 1) {
                options.forEach(opt => opt.selected = false);
                naOption.selected = true;
                if ($(select).hasClass('select2-multi')) {
                    $(select).val([naValue]).trigger('change.select2');
                }
            }
        });
    }

    handleNAExclusive('yoga_style_id');
    handleNAExclusive('special_need_id');
    handleNAExclusive('techniques');
    handleNAExclusive('level');

});

</script>