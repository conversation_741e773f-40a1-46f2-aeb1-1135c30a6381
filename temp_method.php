    /**
     * Verify OTP and register user with full form data
     */
    public function verifyOtpAndRegister()
    {
        $this->request->allowMethod(['post']);

        if ($this->request->is('ajax')) {
            $this->viewBuilder()->setLayout('ajax');
            $response = ['success' => false, 'message' => ''];

            $data = $this->request->getData();
            
            // Required fields from the form
            $otp = $data['otp'] ?? '';
            $firstName = $data['first_name'] ?? '';
            $lastName = $data['last_name'] ?? '';
            $emailId = $data['email_id'] ?? '';
            $mobileNumber = $data['email_or_mobile'] ?? '';
            $password = $data['password'] ?? '';
            $countryCode = $data['country_code'] ?? '91';

            // Determine primary contact method
            $emailOrMobile = '';
            if ($mobileNumber) {
                $emailOrMobile = $mobileNumber;
            } else if ($emailId) {
                $emailOrMobile = $emailId;
            }

            if (empty($otp) || empty($firstName) || empty($emailOrMobile) || empty($password)) {
                $response['message'] = 'All required fields must be provided.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Step 1: Verify OTP first
            $storedOtpData = $this->request->getSession()->read('signup_otp');
            $storedEmailOtpData = $this->request->getSession()->read('email_id');

            $isVerified = false;
            
            // Check mobile OTP if mobile was used
            if ($mobileNumber && $storedOtpData) {
                if ($storedOtpData['email_or_mobile'] === $mobileNumber && time() <= $storedOtpData['expires']) {
                    if (isset($storedOtpData['session_id'])) {
                        // Verify via 2Factor API
                        $twoFactorService = new \App\Service\TwoFactorService();
                        $verifyResult = $twoFactorService->verifyOtp($storedOtpData['session_id'], $otp);
                        $isVerified = $verifyResult['success'];
                    } else {
                        // Verify local OTP
                        $isVerified = $storedOtpData['otp'] === $otp;
                    }
                }
            }
            
            // Check email OTP if email was used and mobile OTP failed/not available
            if (!$isVerified && $emailId && $storedEmailOtpData) {
                if ($storedEmailOtpData['email_id'] === $emailId && time() <= $storedEmailOtpData['expires']) {
                    $isVerified = $storedEmailOtpData['otp'] === $otp;
                }
            }

            if (!$isVerified) {
                $response['message'] = 'Invalid or expired OTP. Please try again.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Step 2: OTP is valid, now register the user
            
            // Determine if primary contact is email or mobile
            $isEmail = filter_var($emailOrMobile, FILTER_VALIDATE_EMAIL);
            $isMobile = preg_match('/^[0-9]{10}$/', $emailOrMobile);

            if (!$isEmail && !$isMobile) {
                $response['message'] = 'Please enter a valid email address or mobile number.';
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Check if email/mobile already exists
            $query = $this->UsersTable->find()->where(['status !=' => 'D','user_type' => 'Customer']);

            if ($isEmail) {
                $query->where(['email' => $emailOrMobile]);
            } else {
                $query->where(['mobile' => $emailOrMobile, 'country_code' => $countryCode]);
            }

            $existingUser = $query->first();

            if ($existingUser) {
                $type = $isEmail ? 'email' : 'mobile number';
                $response['message'] = "This {$type} is already registered.";
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            // Prepare user data
            $userData = [
                'first_name' => $firstName,
                'password' => $password,
                'role_id' => 2, // Regular user role
                'status' => 'A', // Active
                'user_type' => 'Customer' // Set user type as Customer
            ];

            // Add optional last name
            if ($lastName) {
                $userData['last_name'] = $lastName;
            }

            // Set primary contact field
            if ($isEmail) {
                $userData['email'] = $emailOrMobile;
            } else {
                $userData['mobile'] = $emailOrMobile;
                $userData['country_code'] = $countryCode;
            }

            // Add secondary contact if both email and mobile provided
            if ($emailId && $mobileNumber) {
                if ($isEmail) {
                    // Primary is email, secondary is mobile
                    $userData['mobile'] = $mobileNumber;
                    $userData['country_code'] = $countryCode;
                } else {
                    // Primary is mobile, secondary is email
                    $userData['email'] = $emailId;
                }
            }

            $user = $this->UsersTable->newEmptyEntity();
            $user = $this->UsersTable->patchEntity($user, $userData);

            // Begin transaction
            $connection = $this->UsersTable->getConnection();
            $connection->begin();

            try {
                if ($this->UsersTable->save($user)) {
                    // Create customer record
                    $customersTable = $this->fetchTable('Customers');
                    $customer = $customersTable->newEmptyEntity();
                    $customer = $customersTable->patchEntity($customer, [
                        'user_id' => $user->id,
                        'status' => 'A' // Active
                    ]);

                    if ($customersTable->save($customer)) {
                        $connection->commit();
                        
                        // Clear OTP sessions
                        $this->request->getSession()->delete('signup_otp');
                        $this->request->getSession()->delete('email_id');
                        
                        // Send welcome email if email is provided
                        if ($userData['email'] ?? null) {
                            $this->_sendWelcomeEmail($user, $password);
                        }
                        
                        // Set user identity for authentication
                        $this->Authentication->setIdentity($user);
                        
                        $response['success'] = true;
                        $response['message'] = 'Account created successfully!';
                        
                        $redirectUrl = Router::url(['controller' => 'Home', 'action' => 'index'], true);
                        if ($data['reqdirect_url']) {
                            $redirectUrl = Router::url($data['reqdirect_url']);
                        }
                        $response['redirect'] = $redirectUrl;
                        
                    } else {
                        $connection->rollback();
                        $response['message'] = 'Failed to create customer record.';
                        $response['errors'] = $customer->getErrors();
                    }
                } else {
                    Log::error('Failed to save user: ' . json_encode($user->getErrors()));
                    $connection->rollback();
                    $response['message'] = 'Failed to create account.';
                    $response['errors'] = $user->getErrors();
                }
            } catch (\Exception $e) {
                Log::error('Exception during OTP registration: ' . $e->getMessage());
                $connection->rollback();
                $response['message'] = 'An error occurred: ' . $e->getMessage();
            }

            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        return $this->redirect(['action' => 'index']);
    }
