<?php
/**
 * Card Helper Usage Examples
 * 
 * This file demonstrates how to use the CardHelper in different scenarios:
 * 1. Single card rendering
 * 2. Grid layout for product listings
 * 3. Complete section with header
 */

// Example 1: Single Card
// Use this when you need to render just one card
$singleCard = [
    'image' => 'https://picsum.photos/500/320?random=1',
    'title' => 'Advanced Yoga Teacher Training',
    'center' => 'Rishikesh Yoga Academy',
    'rating' => 4.8,
    'description' => 'Deepen your practice and teaching skills with our comprehensive 500-hour advanced yoga teacher training program.',
    'date' => '15-09-2025',
    'mode' => 'In Person',
    'language' => 'English',
    'link' => '/courses/advanced-teacher-training'
];

// Render single card
echo $this->Card->render($singleCard);

// Example 2: Grid Layout for Product/Course Listings
// Use this for pages like courses list, products, etc.
$coursesList = [
    [
        'image' => 'https://picsum.photos/500/320?random=21',
        'title' => 'Hatha Yoga Fundamentals',
        'center' => 'Yoga Alliance Center',
        'rating' => 4.6,
        'description' => 'Learn the foundational poses and principles of Hatha Yoga in this beginner-friendly course.',
        'date' => '01-10-2025',
        'mode' => 'Online',
        'language' => 'English',
        'link' => '/courses/hatha-fundamentals'
    ],
    [
        'image' => 'https://picsum.photos/500/320?random=22',
        'title' => 'Vinyasa Flow Training',
        'center' => 'Flow Studio',
        'rating' => 4.7,
        'description' => 'Master the art of flowing sequences with breath-synchronized movement patterns.',
        'date' => '05-10-2025',
        'mode' => 'Hybrid',
        'language' => 'English',
        'link' => '/courses/vinyasa-flow'
    ],
    [
        'image' => 'https://picsum.photos/500/320?random=23',
        'title' => 'Meditation & Mindfulness',
        'center' => 'Inner Peace Academy',
        'rating' => 4.9,
        'description' => 'Develop a consistent meditation practice and learn mindfulness techniques for daily life.',
        'date' => '10-10-2025',
        'mode' => 'Online',
        'language' => 'English',
        'link' => '/courses/meditation-mindfulness'
    ],
    [
        'image' => 'https://picsum.photos/500/320?random=24',
        'title' => 'Ayurveda & Yoga Integration',
        'center' => 'Holistic Health Center',
        'rating' => 4.5,
        'description' => 'Explore the connection between Ayurvedic principles and yoga practice for optimal health.',
        'date' => '15-10-2025',
        'mode' => 'In Person',
        'language' => 'English',
        'link' => '/courses/ayurveda-yoga'
    ]
];

// Render grid with default settings
echo $this->Card->renderGrid($coursesList);

// Render grid with custom options
echo $this->Card->renderGrid($coursesList, [
    'gridClass' => 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4', // 4 columns on large screens
    'cardClass' => 'group h-full flex flex-col bg-white rounded-lg shadow hover:shadow-xl transition duration-300', // Custom card styling
]);

// Example 3: Complete Section with Header
// Use this for complete page sections with title and description
echo $this->Card->renderSection($coursesList, [
    'title' => 'Popular Yoga Courses',
    'description' => 'Discover our most sought-after yoga programs designed to transform your practice',
    'gridClass' => 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
]);

// Example 4: Different Grid Layouts
// 2 columns layout
echo $this->Card->renderGrid($coursesList, [
    'gridClass' => 'grid grid-cols-1 md:grid-cols-2 gap-8',
]);

// List layout (single column)
echo $this->Card->renderGrid($coursesList, [
    'gridClass' => 'space-y-6',
    'itemClass' => 'w-full'
]);

// Example 5: Custom Section Styling
echo $this->Card->renderSection($coursesList, [
    'title' => 'Featured Courses',
    'description' => 'Hand-picked courses from top instructors',
    'sectionClass' => 'w-full py-16 bg-gray-50',
    'containerClass' => 'max-w-6xl mx-auto px-6',
    'gridClass' => 'grid grid-cols-1 md:grid-cols-3 gap-8'
]);
?>
