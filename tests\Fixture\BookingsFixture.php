<?php
declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;

/**
 * BookingsFixture
 */
class BookingsFixture extends TestFixture
{
    /**
     * Init method
     *
     * @return void
     */
    public function init(): void
    {
        $this->records = [
            [
                'id' => 1,
                'group_booking_id' => 1,
                'customer_id' => 1,
                'course_id' => 1,
                'class_id' => 1,
                'class_time_slot_id' => 1,
                'partner_id' => 1,
                'title' => 'Lorem ipsum dolor sit amet',
                'first_name' => 'Lorem ipsum dolor sit amet',
                'last_name' => 'Lorem ipsum dolor sit amet',
                'email' => 'Lorem ipsum dolor sit amet',
                'phone' => 'Lorem ipsum dolor sit amet',
                'age' => 1,
                'food' => 'Lorem ipsum dolor sit amet',
                'booking_date' => '2025-07-16',
                'booking_status' => 'Lorem ipsum dolor sit amet',
                'currency' => 'Lorem ipsum dolor sit amet',
                'discount_id' => 1,
                'discount_value' => 1.5,
                'tax_amount' => 1.5,
                'tax_rate' => 1.5,
                'total_price' => 1.5,
                'grand_total' => 1.5,
                'payment_status' => 'Lorem ipsum dolor sit amet',
                'created_at' => 1752658830,
                'modified_at' => 1752658830,
            ],
        ];
        parent::init();
    }
}
