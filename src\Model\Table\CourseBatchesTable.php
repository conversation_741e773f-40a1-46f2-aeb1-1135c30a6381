<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * CourseBatches Model
 *
 * @property \App\Model\Table\CoursesTable&\Cake\ORM\Association\BelongsTo $Courses
 *
 * @method \App\Model\Entity\CourseBatch newEmptyEntity()
 * @method \App\Model\Entity\CourseBatch newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\CourseBatch> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\CourseBatch get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\CourseBatch findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\CourseBatch patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\CourseBatch> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\CourseBatch|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\CourseBatch saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\CourseBatch>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseBatch>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CourseBatch>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseBatch> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CourseBatch>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseBatch>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CourseBatch>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseBatch> deleteManyOrFail(iterable $entities, array $options = [])
 */
class CourseBatchesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('course_batches');
        $this->setDisplayField('day_of_week');
        $this->setPrimaryKey('id');

        $this->belongsTo('Courses', [
            'foreignKey' => 'course_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('course_id')
            ->notEmptyString('course_id');

        $validator
        ->scalar('name')
        ->notEmptyString('name', 'Name is required.');

        // $validator
        // ->scalar('duration_details')
        // ->notEmptyString('name');

        $validator
            ->date('start_date')
            ->requirePresence('start_date', 'create', 'Start Date is required.')
            ->notEmptyDate('start_date');

        $validator
            ->date('end_date')
            ->requirePresence('end_date', 'create', 'End Date is required.')
            ->notEmptyDate('end_date');

        // $validator
        //     ->scalar('day_of_week')
        //     ->maxLength('day_of_week', 100)
        //     ->requirePresence('day_of_week', 'create')
        //     ->notEmptyString('day_of_week');

        // $validator
        //     ->time('start_time')
        //     ->requirePresence('start_time', 'create')
        //     ->notEmptyTime('start_time');

        // $validator
        //     ->time('end_time')
        //     ->requirePresence('end_time', 'create')
        //     ->notEmptyTime('end_time');

        // $validator
        //     ->decimal('hourly_rate')
        //     ->requirePresence('hourly_rate', 'create')
        //     ->notEmptyString('hourly_rate');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        $validator
            ->dateTime('created_at')
            ->notEmptyDateTime('created_at');

        $validator
            ->dateTime('modified_at')
            ->notEmptyDateTime('modified_at');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['course_id'], 'Courses'), ['errorField' => 'course_id']);

        return $rules;
    }
}
