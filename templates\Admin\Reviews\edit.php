<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Review $review
 * @var array $statusOptions
 * @var array $reviewTypeOptions
 * @var array $ratingOptions
 */
?>


<!-- Font Awesome for star icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

<style>
    .star-rating {
        direction: ltr;
        font-size: 1.5rem;
        unicode-bidi: bidi-override;
        display: inline-flex;
        gap: 5px;
    }

    .star-rating .fa-star {
        cursor: pointer;
        color: #ccc;
    }

    .star-rating .fa-star.selected {
        color: #f5b301;
    }
</style>


<div class="main-content">
    <section class="section">
        <div class="section-body1">
            <div class="container-fluid">
                <?php
                $successMessage = $this->Flash->render('success');
                $errorMessage = $this->Flash->render('error');
                ?>
                <?php if (!empty($successMessage)): ?>
                    <?= $successMessage ?>
                <?php endif; ?>
                <?php if (!empty($errorMessage)): ?>
                    <?= $errorMessage ?>
                <?php endif; ?>

                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>Edit Review </h4>
                                <div class="card-header-action">
                                    <?= $this->Html->link('Back to List', ['action' => 'index'], ['class' => 'btn btn-secondary']) ?>
                                    <!-- <?= $this->Form->postLink('Delete', ['action' => 'delete', $review->id], [
                                        'class' => 'btn btn-danger',
                                        'confirm' => 'Are you sure you want to delete this review?'
                                    ]) ?> -->
                                </div>
                            </div>
                            <div class="card-body">
                                <?= $this->Form->create($review) ?>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Customer ID</label>
                                            <?= $this->Form->control('customer_id', [
                                                'label' => false,
                                                'type' => 'number',
                                                'class' => 'form-control',
                                                'required' => true,
                                                'min' => 1
                                            ]) ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Review Type</label>
                                            <?= $this->Form->control('review_type', [
                                                'label' => false,
                                                'options' => $reviewTypeOptions,
                                                'empty' => 'Select Review Type',
                                                'class' => 'form-control',
                                                'required' => true
                                            ]) ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Reference ID</label>
                                            <?= $this->Form->control('reference_id', [
                                                'label' => false,
                                                'type' => 'number',
                                                'class' => 'form-control',
                                                'required' => true,
                                                'min' => 1
                                            ]) ?>
                                            <small class="form-text text-muted">ID of the course, teacher, or center being reviewed</small>
                                        </div>
                                    </div>


                                    <!-- <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Rating</label>
                                            <div class="star-rating" id="star-rating">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fa fa-star" data-value="<?= $i ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                           
                                            <?= $this->Form->hidden('rating', ['id' => 'rating-value', 'value' => $review->rating]) ?>
                                            <small class="form-text text-muted">Click on the stars to rate from 1 to 5</small>
                                        </div>
                                    </div> -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Booking ID (Optional)</label>
                                            <?= $this->Form->control('booking_id', [
                                                'label' => false,
                                                'type' => 'number',
                                                'class' => 'form-control',
                                                'min' => 1
                                            ]) ?>
                                        </div>
                                    </div>
                                   
                                </div>

                                <div class="row">
                                    
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Booking Item ID (Optional)</label>
                                            <?= $this->Form->control('booking_item_id', [
                                                'label' => false,
                                                'type' => 'number',
                                                'class' => 'form-control',
                                                'min' => 1
                                            ]) ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Status</label>
                                            <?= $this->Form->control('status', [
                                                'label' => false,
                                                'options' => $statusOptions,
                                                'class' => 'form-control'
                                            ]) ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label>Comment</label>
                                            <?= $this->Form->control('comment', [
                                                'label' => false,
                                                'type' => 'textarea',
                                                'class' => 'form-control',
                                                'rows' => 4,
                                                'maxlength' => 1000,
                                                'placeholder' => 'Enter review comment (optional)'
                                            ]) ?>
                                            <small class="form-text text-muted">Maximum 1000 characters</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    
                                </div>
                               <?php if (!empty($review->review_answers)): ?>
                                <h5>Review Question Answers</h5>
                                <?php foreach ($review->review_answers as $i => $answer): ?>
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <strong>Question:</strong> <?= h($answer->review_question->question) ?><br>
                                            <label>Rating</label>
                                            <div class="star-rating" id="star-rating-<?= $i ?>">
                                                <?php for ($star = 1; $star <= 5; $star++): ?>
                                                    <i class="fa fa-star<?= ($answer->rating >= $star) ? ' selected' : '' ?>" data-value="<?= $star ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                            <?= $this->Form->hidden("review_answers.{$i}.rating", [
                                                'id' => "rating-value-{$i}",
                                                'value' => $answer->rating
                                            ]) ?>
                                        
                                            <?= $this->Form->hidden("review_answers.{$i}.id", ['value' => $answer->id]) ?>
                                        </div>
                                    </div>
                                    <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        var stars = document.querySelectorAll('#star-rating-<?= $i ?> .fa-star');
                                        var ratingInput = document.getElementById('rating-value-<?= $i ?>');
                                        stars.forEach(function(star, idx) {
                                            star.addEventListener('click', function() {
                                                var rating = idx + 1;
                                                ratingInput.value = rating;
                                                stars.forEach(function(s, j) {
                                                    if (j < rating) {
                                                        s.classList.add('selected');
                                                    } else {
                                                        s.classList.remove('selected');
                                                    }
                                                });
                                            });
                                        });
                                    });
                                    </script>
                                <?php endforeach; ?>
                            <?php endif; ?>

                                <!-- Review Metadata -->
                                <!-- <div class="row mt-3">
                                    <div class="col-md-12">
                                        <div class="">
                                            <h6>Review Metadata</h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <strong>Created:</strong> <?= h($review->created_at->format('M d, Y H:i:s')) ?>
                                                </div>
                                                <div class="col-md-6">
                                                    <strong>Last Updated:</strong> <?= h($review->updated_at->format('M d, Y H:i:s')) ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div> -->

                                <div class="form-group mt-4">
                                    <?= $this->Form->button('Update Review', [
                                        'class' => 'btn btn-primary',
                                        'type' => 'submit'
                                    ]) ?>
                                    <!-- <?= $this->Html->link('View', ['action' => 'view', $review->id], [
                                        'class' => 'btn btn-info ms-2'
                                    ]) ?> -->
                                    <!-- <?= $this->Html->link('Cancel', ['action' => 'index'], [
                                        'class' => 'btn btn-secondary ms-2'
                                    ]) ?> -->
                                </div>
                                <?= $this->Form->end() ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>
<?php $this->append('script'); ?>
<script>
    $(document).ready(function () {
        const stars = $('#star-rating .fa-star');
        const hiddenInput = $('#rating-value');
        const currentRating = parseInt(hiddenInput.val());

        function highlightStars(rating) {
            stars.each(function (index) {
                $(this).toggleClass('selected', index < rating);
            });
        }

        highlightStars(currentRating);

        stars.on('click', function () {
            const rating = $(this).data('value');
            hiddenInput.val(rating);
            highlightStars(rating);
        });

        stars.on('mouseenter', function () {
            const rating = $(this).data('value');
            highlightStars(rating);
        });

        $('#star-rating').on('mouseleave', function () {
            highlightStars(parseInt(hiddenInput.val()));
        });
    });
</script>

<?php $this->end(); ?>
