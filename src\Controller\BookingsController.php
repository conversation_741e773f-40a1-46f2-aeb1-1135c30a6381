<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Validation\Validator;
use Cake\Http\Exception\BadRequestException;
use Cake\I18n\Date;
use Cake\Core\Configure;
use Cake\Routing\Router;

/**
 * Bookings Controller
 *
 * @property \App\Model\Table\BookingsTable $Bookings
 */
class BookingsController extends AppController
{
    protected $Courses;
    protected $Bookings;
    protected $BookingItems;
    protected $BookingAddons;
    protected $Payments;
    protected $Customers;
    protected $CourseAddons;
    protected $CourseAddonPricing;
    protected $Currencies;
    protected $Countries;
    protected $States;
    protected $Users;
    
    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['enrollment', 'add', 'confirmation']);
    }

    public function initialize(): void
    {
        parent::initialize();
        $this->loadComponent('Global');
        $this->loadComponent('Common');
        $this->Courses = $this->fetchTable('Courses');
        $this->Bookings = $this->fetchTable('Bookings');
        $this->BookingAddons = $this->fetchTable('BookingAddons');
        $this->Payments = $this->fetchTable('Payments');
        $this->Customers = $this->fetchTable('Customers');
        $this->CourseAddons = $this->fetchTable('CourseAddons');
        $this->CourseAddonPricing = $this->fetchTable('CourseAddonPricing');
        $this->BookingItems = $this->fetchTable('BookingItems');   
        $this->Currencies = $this->fetchTable('Currencies');   
        $this->Countries = $this->fetchTable('Countries'); 
        $this->States = $this->fetchTable('States');
        $this->Users = $this->fetchTable('Users');
    }

    public function index(){
        $text = "Coming Soon";
        $this->set(compact('text'));
    }
    // List bookings
    public function enrollment($c_slug)
    {
        if($c_slug){
            $course = $this->Courses->findBySlug($c_slug);
            $residencyOptions = [
                'india' => 'India Resident',
                'international' => 'International'
            ];

            $foreignCurrencies = $this->Currencies->getForeignCurrencies();
           
            $pricing = $this->getFormatPricing($course);
          
            $codesJson = file_get_contents(WWW_ROOT . 'js/data/country-codes.json');
            $countryCodes = json_decode($codesJson, true);
            // get country and state list //
            $countries = $this->Countries->getList();
            $states = $this->States->getList();
        
            $foodOptions = $course->food_options ? explode(',', $course->food_options) : [];
            $accommodationOptions = $course->accommodation_options ? explode(',', $course->accommodation_options) : [];
            $domestic_tax = $course->partner->domestic_tax_percentage ? $course->partner->domestic_tax_percentage : 0;
            $international_tax = !empty($course->partner->international_tax_percentage) ? $course->partner->international_tax_percentage: 0;
            // $domestic_tax = 18;
            // $international_tax = 12;
          
            $this->set(compact('course', 'residencyOptions', 'pricing', 'countryCodes', 'foreignCurrencies', 'accommodationOptions', 'foodOptions', 'domestic_tax', 'international_tax', 'countries', 'states'));
        }
    }

    protected function getFormatPricing($course){
        $basePrices = [];
        foreach ($course->course_base_prices as $price) {
            $currency = $price->currency->name; 
            if (!isset($groupedBasePrices[$currency])) {
                $groupedBasePrices[$currency] = [];
            }

            $left_count = $price->total_count - $price->booked_count;

            $basePrices[$currency][] = [
                'id' => $price->id,
                'name' => $price->name,
                'price' => $price->price,
                'currency' => $currency,
                'left_count' => $left_count
            ];
        }
        
        // course addons array//
        $addons = [];
        if(!empty($course->course_addons)){
            foreach($course->course_addons as $index => $addon){
                if($addon->status == 'active'){
                    $pricing = [];
                    if(!empty($addon->course_addon_pricing)){
                        foreach($addon->course_addon_pricing as $price){
                            $pricing[$price->currency->name] = [
                                'id' => $price->id,
                                'price' => $price->price
                            ];
                        }
                    } 

                    $left_slots = $addon->total_slots - $addon->booked_slots;

                    $addons[] = [
                        'id'   => $addon->id,
                        'name' => $addon->custom_name ? $addon->custom_name :  $addon->master_data->title,
                        'sort_order' => $addon->sort_order,
                        'left_count' => $left_slots,
                        'prices' => $pricing,
                    ];
                }
            }
        }

        $prices = [
            'basePrices' => $basePrices,
            'addons' => $addons
        ];
        return $prices;
    }

    public function calculateTotalTax($subTotal, $billing_currency, $course)
    {
        $domesticTax = !empty($course->partner->domestic_tax_percentage) ? $course->partner->domestic_tax_percentage : 0;
        $internationalTax = !empty($course->partner->international_tax_percentage) ? $course->partner->international_tax_percentage: 0;
        // $domesticTax = 18;
        // $internationalTax = 12;

        $taxRate = $billing_currency == 'INR' ? $domesticTax : $internationalTax;

        $totalTax = $subTotal * ($taxRate / 100);

        return round($totalTax, 2);
    }

    public function calculateParticipantTotalTax($subTotal, $billing_currency, $course){
       
        $domesticTax = !empty($course->partner->domestic_tax_percentage) ? $course->partner->domestic_tax_percentage : 0;
        $internationalTax = !empty($course->partner->international_tax_percentage) ? $course->partner->international_tax_percentage: 0;
        // $domesticTax = 18;
        // $internationalTax = 12;

        $taxRate = $billing_currency == 'INR' ? $domesticTax : $internationalTax;
        if($taxRate <= 0){
            return [
                'tax_rate' => 0,
                'tax_amount' => 0
            ];
        }
        $totalTax = $subTotal * ($taxRate / 100);
        return [
            'tax_rate' => $taxRate,
            'tax_amount' => round($totalTax, 2)
        ];
    }

    public function add(){
        $this->request->allowMethod(['post']);
        $this->request->accepts('application/json');
        
        $data = $this->request->getData();
        // dd($data);
        $validator = new Validator();
        $validator
            ->requirePresence('batch_id', true, 'Please select a batch.')
            ->notEmptyString('batch_id', 'Please select a batch.');

        $validator
            ->requirePresence('participants')
            ->add('participants', 'nonEmpty', [
                'rule' => fn($value) => is_array($value) && count($value) > 0,
                'message' => 'At least one participant is required.'
            ]);

        $errors = $validator->validate($data);
        
        // Per-participant validation
        if (empty($errors['participants']) && !empty($data['participants']) && is_array($data['participants'])) {
            
            foreach ($data['participants'] as $i => $participant) {
              
                $pValidator = new Validator();
                $pValidator
                    ->requirePresence('first_name', true)
                    ->notEmptyString('first_name', 'First name is required.')

                    ->requirePresence('last_name', true)
                    ->notEmptyString('last_name', 'Last name is required.')

                    ->requirePresence('email', true)
                    ->email('email', false, 'Invalid email.')

                    ->requirePresence('phone', true)
                    ->notEmptyString('phone', 'Phone is required.')

                    ->requirePresence('base_price_id', true)
                    ->notEmptyString('base_price_id', 'Base price is required.')

                    ->add('state_id', 'requiredIfIndia', [
                       'rule' => function ($value, $context) {
                            $residency = $context['data']['residency'] ?? null;
                            if (strtolower((string)$residency) === 'india' && empty($value)) {
                                return false;
                            }
                            return true;
                        },
                        'message' => 'State is required when residency is India.'
                    ]);

                $pErrors = $pValidator->validate($participant);
                if (!empty($pErrors)) {
                    $errors['participants'][$i] = $this->Global->formatValidationErrors($pErrors);
                }
            }
        }

        if (!empty($errors)) {
            return $this->response
                ->withType('application/json')
                ->withStatus(422)
                ->withStringBody(json_encode([
                    'success' => false,
                    'errors' => $errors,
                    'message' => 'Validation failed! Enter all required inputs.'
                ]));
        }
        
        $course = $this->Courses->get($data['course_id'], [
            'contain' => ['Partners']
        ]);
    
        // save Booking //
        $participants = $data['participants'];
        $courseId = $data['course_id'];
        $batchId = $data['batch_id'];
        $billingCurrency = $data['billing_currency'];
       
        $exchangeRates = [  
            'INR'=> 1,
            'USD'=> 0.012,
            'EUR'=> 0.011
        ];
        $subTotal = 0;
        $taxTotal = 0;
        $discountTotal = 0;
        $newUser = false;
        $sendEmailTo = !empty($participants[$data['billed_to']]) ? $participants[$data['billed_to']] : $participants[0]; 

        if(!empty($this->Authentication->getIdentity())){
            $user_id = $this->Authentication->getIdentity()->get('id');
        } else {
            // Create account if not logged in//
            if($sendEmailTo['email']){
               $user_id = $this->createAccount($sendEmailTo);
               $newUser = true;
            }   
        }
       
        $customer = $this->Customers->find()->where(['user_id' => $user_id])->first();
       
        $batch = $this->fetchTable('CourseBatches')->get($batchId);
      
       // $booking->user_id = $this->Authentication->getIdentity()->get('id');
        try{

            $participantCount = count($participants);
            $left_batch_count = $batch->capacity - $batch->booked_count;
          
            if($left_batch_count <  $participantCount){
                return $this->response
                    ->withType('application/json')
                    ->withStatus(422)
                    ->withStringBody(json_encode([
                        'success' => false,
                        'message' => 'The Course Batch do not have sufficient slots available. Available slot count is '. $left_batch_count
                    ]));
            }
          
            // Save each participant
            $validatedAddons = [];
            foreach ($participants as $i => $participant) {
                // validated addon count and prepare data
            
                if (!empty($participant['selected_addons']) && is_array($participant['selected_addons'])) {
                    foreach ($participant['selected_addons'] as $addonData) {
                        if (empty($addonData['addon_id'])) {
                            continue;
                        }

                        $courseAddon = $this->CourseAddons->get($addonData['addon_id'], [
                            'contain' => ['MasterData', 'CourseAddonPricing.Currencies']
                        ]);

                        $addonName = $courseAddon->custom_name ?? $courseAddon->master_data->title ?? '';
                        $left_slots = $courseAddon->total_slots - $courseAddon->booked_slots;

                        if($left_slots <  $participantCount){   
                            return $this->response->withType('application/json')
                                ->withStatus(422)
                                ->withStringBody(json_encode([
                                    'success' => false,
                                    'message' => 'The Addon '.$addonName.' do not have sufficient slots available. Available slot count is '. $left_slots
                                ]));
                        }

                        // Save for reuse during saving
                        $addonPricing = $this->CourseAddonPricing->get($addonData['price_id'], ['contain' => ['Currencies']]);
                        $validatedAddons[$i][] = compact('courseAddon', 'addonPricing');
                    }
                }
            }

            $conn = $this->Bookings->getConnection();
            $conn->begin();

            $booking = $this->Bookings->newEmptyEntity();
            $booking->course_id = $courseId;
            $booking->customer_id = !empty($customer) ? $customer->id : null;
            $booking->partner_id  = $course ? $course->partner_id : null;
            $booking->batch_id = $batchId;
            $booking->billing_currency = $billingCurrency;
            $booking->exchange_rate = json_encode($exchangeRates);
            $booking->booking_date =  date('Y-m-d');
            $booking->billed_to = !empty($sendEmailTo['email']) ? $sendEmailTo['email'] : '';

            if (!$this->Bookings->save($booking)) {
                $conn->rollback();
                return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'success' => false,
                    'message' => 'Unable to save booking. Please try again.'
                ]));
            }

            // Save booking details and addons //
            foreach ($participants as $i => $participant) {
                
                $participantSubTotal = 0;
                $basePrice = $this->Courses->CourseBasePrices->get($participant['base_price_id'],[
                    'contain' => ['Currencies']
                ]);
             
                // Get base price in original currency
                $basePriceAmount = $basePrice->price;
                $basePriceCurrency = $basePrice->currency->name;

                // Convert base price to billing currency
                $basePriceAmountBilling = $basePriceAmount;
             
                if ($basePriceCurrency !== $billingCurrency && !empty($exchangeRates[$basePriceCurrency]) && !empty($exchangeRates[$billingCurrency])) {
                    $basePriceAmountBilling = ($basePriceAmount / $exchangeRates[$basePriceCurrency]) * $exchangeRates[$billingCurrency];
                }
            
                // Save booking items
                $item = $this->BookingItems->newEmptyEntity();
                $item->booking_id = $booking->id;
                $item->title = $participant['title'];
                $item->first_name = $participant['first_name'];
                $item->last_name = $participant['last_name'];
                $item->email = $participant['email'];
                $item->phone_country_code = $participant['phone_country_code'];
                $item->phone = $participant['phone'];
                $item->age = $participant['age'];
                $item->food = $participant['food'];
                $item->residency = $participant['residency'];
                $item->state_id = $participant['state_id'];
                
                $item->base_price_id = !empty($basePrice) ? $basePrice->id : 0;
                $item->base_price_name = !empty($basePrice) ? $basePrice->name : '';
            
                $item->base_price_amount = $basePriceAmount;
                $item->base_price_currency = $basePriceCurrency;
                $item->base_price_amount_billing = round((int)$basePriceAmountBilling, 2); 
                $exchange_rate = $this->Common->getExchangeRate($basePriceCurrency);
                $item->exchange_rate = $exchange_rate;

                if($this->BookingItems->save($item)){
                    // update booked count for course base price//
                    $batch->booked_count += 1;
                    $this->Courses->CourseBatches->save($batch);

                    $participantSubTotal += $basePriceAmountBilling;
                    $subTotal += $basePriceAmountBilling;

                    // Save booking addons
                   
                    if (!empty($validatedAddons[$i])) {
                        foreach ($validatedAddons[$i] as $addonSet) {
                            $courseAddon = $addonSet['courseAddon'];
                            $addonPricing = $addonSet['addonPricing'];
                     
                            $addonPrice = $addonPricing->price ?? 0;
                            $addonCurrency = $addonPricing->currency->name ?? '';
                            $addonPriceBilling = $addonPrice;
                      
                            if ($addonCurrency !== $billingCurrency && !empty($exchangeRates[$addonCurrency]) && !empty($exchangeRates[$billingCurrency])) {
                                $addonPriceBilling = ($addonPrice / $exchangeRates[$addonCurrency]) * $exchangeRates[$billingCurrency];
                            }

                            $addon = $this->BookingAddons->newEmptyEntity();
                            $addon->booking_id = $booking->id;
                            $addon->booking_item_id = $item->id;
                            $addon->addon_id = $courseAddon->id;
                            $addon->addon_name = $courseAddon->custom_name ? $courseAddon->custom_name : $courseAddon->master_data->title;
                         
                            $addon->addon_price = $addonPrice;
                            $addon->addon_currency = $addonCurrency;
                            $addon->addon_price_billing = round((int)$addonPriceBilling, 2); 
                            $addon->total_price = $addonPrice;
                            $addon->total_price_billing = round((int)$addonPriceBilling, 2); 
                            
                            $this->BookingAddons->save($addon);

                            // update addon booked count
                            $courseAddon->booked_slots += 1;
                            
                            $this->CourseAddons->save($courseAddon);
                            // update subtotal
                            $participantSubTotal += $addonPriceBilling;
                            $subTotal += $addonPriceBilling;
                        }
                    }
                    //calculate tax and save for booking item//
                  
                    $taxValues = $this->calculateParticipantTotalTax($participantSubTotal, $billingCurrency, $course);
                
                   // if(!empty($taxValues['tax_rate'] && $taxValues['tax_amount'])){
                        $taxAmount = !empty($taxValues['tax_amount']) ? $taxValues['tax_amount'] : 0;
                        $taxRate = !empty($taxValues['tax_rate']) ? $taxValues['tax_rate'] : 0;
                        $item->tax_amount = $taxAmount;
                        $item->tax_rate = $taxRate;
                        $this->BookingItems->save($item);
                        $taxTotal += $taxAmount;
                   // }
                   
                }
            }
          
            // update total price/tax/discount //
            $booking->sub_total = round($subTotal, 2); // in billing currency
            $booking->tax_amount = round($taxTotal, 2); // in billing currency
            $booking->tax_rate = !empty($taxRate) ? $taxRate : 0;
            $booking->grand_total = round(($subTotal + $taxTotal) - $discountTotal, 2); // in billing currency
            $booking->booking_status =  'confirmed';
            $this->Bookings->save($booking);
            $conn->commit();
           

            if(!empty($sendEmailTo['email'])){
               
                $emailVar = [
                    'course' => $course,
                    'booking'=> $booking,
                    'batch'  => $batch,
                    'my_booking_url' => Router::url(['controller' => 'Bookings', 'action' => 'index'],true)
                ];
              
                $this->Global->send_email(
                    $sendEmailTo['email'],
                    Configure::read('Settings.FROM_EMAIL'),
                    "Booking Confirmation - Yoga.in",
                    'booking_confirmation',
                    $emailVar
                );
            }
       
            return $this->response->withType('application/json')
            ->withStringBody(json_encode([
                'success' => true,
                'message' =>  'Booking Successfull',
                'booking_id' => $booking->id,
            ]));
            
        }  catch (\Exception $e) {
            $conn->rollback();
            dd($e->getMessage());
            return $this->response
                ->withType('application/json')
                ->withStringBody(json_encode([
                'success' => false,
                'message' => 'Something went wrong! Please try again later.'
            ]));
        }
    }
    
    public function createAccount($data){
        $existingUser = $this->Users->findByEmail($data['email']);
        
        if ($existingUser) {
            $user_id = $existingUser->id;
        } else {
            $user = $this->Users->newEmptyEntity();
            // Prepare user data
            $userData = [
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'email' => $data['email'],
                'mobile' => $data['phone'],
                'country_code' => $data['phone_country_code'],
                'role_id' => 2, // Regular user role
                'status' => 'A', // Active
                'user_type' => 'Customer' // Set user type as Customer
            ];

            $user = $this->Users->patchEntity($user, $userData);
            if ($this->Users->save($user)) {
                 //create a customer record
                $customer = $this->Customers->newEmptyEntity();
                $customer = $this->Customers->patchEntity($customer, [
                    'user_id' => $user->id,
                    'status' => 'A' // Active
                ]);

                $this->Customers->save($customer);
            }
            $user_id = $user->id;
        }
        return $user_id;
    }
    private function savePayment($bookingId){
        $payment = $this->Payments->newEmptyEntity();
        $payment->booking_id = $bookingId;
        // $payment->payment_method_id = 
        // $payment->total_amount = 
        // $payment->tax_amount = 
        // $payment->transaction_id =
        // $payment->payment_status =
        // $payment->payment_date = 
    }
    
    public function confirmation($encodedId)
    {
        // Load the primary booking
        $id = base64_decode($encodedId);
        if (!$id) {
            throw new BadRequestException(__('Invalid booking ID'));
        }
        $booking = $this->Bookings->find()
            ->contain(['CourseBatches', 'Courses.Partners'])
            ->where(['Bookings.id' => $id])
            ->firstOrFail();

        $status = "Booking Confirmed";
        $this->set(compact('booking', 'status'));
    }
}
