<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * BookingItem Entity
 *
 * @property int $id
 * @property int $booking_id
 * @property string $title
 * @property string $first_name
 * @property string $last_name
 * @property string $email
 * @property string $phone
 * @property int $age
 * @property string $food
 * @property string $residency
 * @property string $currency
 * @property int $base_price_id
 * @property string $base_price_name
 * @property string $base_price_amount
 * @property string $base_price_currency
 * @property string|null $hourly_rate
 * @property string|null $exchange_rate
 * @property int|null $discount_id
 * @property string|null $discount_value
 * @property string|null $tax_amount
 * @property string|null $tax_rate
 * @property string|null $sub_total
 * @property string|null $grand_total
 * @property string|null $status
 * @property \Cake\I18n\DateTime $created_at
 * @property \Cake\I18n\DateTime $modified_at
 *
 * @property \App\Model\Entity\Booking $booking
 * @property \App\Model\Entity\Discount $discount
 */
class BookingItem extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'booking_id' => true,
        'title' => true,
        'first_name' => true,
        'last_name' => true,
        'email' => true,
        'phone' => true,
        'age' => true,
        'food' => true,
        'residency' => true,
        'state_id' => true,
        'currency' => true,
        'base_price_id' => true,
        'base_price_name' => true,
        'base_price_amount' => true,
        'base_price_currency' => true,
        'hourly_rate' => true,
        'exchange_rate' => true,
        'discount_id' => true,
        'discount_value' => true,
        'tax_amount' => true,
        'tax_rate' => true,
        'sub_total' => true,
        'grand_total' => true,
        'status' => true,
        'created_at' => true,
        'modified_at' => true,
        'booking' => true,
        'discount' => true,
    ];
}
