<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Table;
use Cake\Validation\Validator;

class BookmarksTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('bookmarks');
        $this->setPrimaryKey('id');

        // Relationships
        $this->belongsTo('Users', [
            'foreignKey' => 'customer_id',
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('customer_id')
            ->requirePresence('customer_id', 'create')
            ->notEmptyString('customer_id');

        $validator
            ->inList('type', ['course', 'class', 'center', 'teacher'])
            ->requirePresence('type', 'create')
            ->notEmptyString('type');

        $validator
            ->integer('ref_id')
            ->requirePresence('ref_id', 'create')
            ->notEmptyString('ref_id');

        return $validator;
    }
}
