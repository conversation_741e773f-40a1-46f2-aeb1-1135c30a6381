<?php
declare(strict_types=1);

namespace App\Controller\Admin;

use App\Controller\Admin\AppController;
use Cake\Routing\Router;
use Cake\Core\Configure;
use Cake\Utility\Security;
use Cake\I18n\FrozenTime;
use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\Http\Cookie\Cookie;
use DateTime;
use Cake\Http\Response;
use Cake\Cache\Cache;
use Cake\Log\Log;

/**
 * Reviews Controller
 *
 * @property \App\Model\Table\ReviewsTable $Reviews
 */
class ReviewsController extends AppController
{

    public function initialize(): void
    {
        parent::initialize();
        $this->loadComponent('Flash');
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('CustomPaginator');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
    }
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        try {
            $page = (int)$this->request->getQuery('page', 1);

            // Get filter parameters
            $filters = [
                'status' => $this->request->getQuery('status'),
                'review_type' => $this->request->getQuery('review_type'),
                'rating' => $this->request->getQuery('rating'),
                'search' => $this->request->getQuery('search')
            ];

            // Apply filters
            $query = $this->Reviews->find('all');

        if (!empty($filters['status'])) {
            $query->where(['Reviews.status' => $filters['status']]);
        }

        if (!empty($filters['review_type'])) {
            $query->where(['Reviews.review_type' => $filters['review_type']]);
        }

        if (!empty($filters['rating'])) {
            $query->where(['Reviews.rating' => $filters['rating']]);
        }

        if (!empty($filters['search'])) {
            $searchConditions = ['OR' => []];

            // Only search by ID if the search term is numeric
            if (is_numeric($filters['search'])) {
                $searchConditions['OR']['Reviews.id'] = (int)$filters['search'];
            }

            // Search in comment and other text fields
            $searchConditions['OR']['Reviews.comment LIKE'] = '%' . $filters['search'] . '%';

            $query->where($searchConditions);
        }

        // Use standard CakePHP pagination
        $this->paginate = [
            'limit' => 10,
            'page' => $page,
            'order' => ['Reviews.created_at' => 'desc']
        ];

        $reviews = $this->paginate($query);
      
        // $statistics = $this->Reviews->getStatistics();

        // Get filter options
        $statusOptions = \App\Model\Table\ReviewsTable::getStatusOptions();
        $reviewTypeOptions = \App\Model\Table\ReviewsTable::getReviewTypeOptions();
        $ratingOptions = \App\Model\Table\ReviewsTable::getRatingOptions();

        // AJAX request for pagination/search
        if ($this->request->is('ajax') || $this->request->getQuery('ajax')) {
            $this->set(compact('reviews'));
            $this->viewBuilder()->setLayout('ajax');
            $this->render('/element/Admin/Reviews/table_content');
            return $this->response;
        }

        $this->set(compact(
            'reviews',
            // 'statistics',
            'filters',
            'statusOptions',
            'reviewTypeOptions',
            'ratingOptions'
        ));
        } catch (\Exception $e) {
            $this->Flash->error('Database table "reviews" does not exist. Please run the migration first: bin/cake migrations migrate');
            // Set empty data for the view
            $reviews = [];
            $statistics = ['total' => 0, 'pending' => 0, 'approved' => 0, 'rejected' => 0, 'average_rating' => 0];
            $filters = [];
            $statusOptions = \App\Model\Table\ReviewsTable::getStatusOptions();
            $reviewTypeOptions = \App\Model\Table\ReviewsTable::getReviewTypeOptions();
            $ratingOptions = \App\Model\Table\ReviewsTable::getRatingOptions();
            $this->set(compact(
                'reviews',
                'statistics',
                'filters',
                'statusOptions',
                'reviewTypeOptions',
                'ratingOptions'
            ));
        }
    }

    /**
     * View method
     *
     * @param string|null $id Review id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        // $review = $this->Reviews->get($id);

         $review = $this->Reviews->get($id, [
        'contain' => [
            'ReviewAnswers' => ['ReviewQuestions']
        ]
    ]);
        $this->set(compact('review'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        // try {
        //     $review = $this->Reviews->newEmptyEntity();
        // } catch (\Exception $e) {
        //     $this->Flash->error('Database table "reviews" does not exist. Please run the migration first: bin/cake migrations migrate');
        //     return $this->redirect(['action' => 'index']);
        // }
        
        // if ($this->request->is('post')) {
        //     $review = $this->Reviews->patchEntity($review, $this->request->getData());
            
        //     if ($this->Reviews->save($review)) {
        //         $this->Flash->success(__('The review has been saved.'));
        //         return $this->redirect(['action' => 'index']);
        //     }
        //     $this->Flash->error(__('The review could not be saved. Please, try again.'));
        // }

        // Get options for dropdowns
        $statusOptions = \App\Model\Table\ReviewsTable::getStatusOptions();
        $reviewTypeOptions = \App\Model\Table\ReviewsTable::getReviewTypeOptions();
        $ratingOptions = \App\Model\Table\ReviewsTable::getRatingOptions();
      

        $this->set(compact('review', 'statusOptions', 'reviewTypeOptions', 'ratingOptions'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Review id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
         $review = $this->Reviews->get($id, [
        'contain' => [
            'ReviewAnswers' => ['ReviewQuestions']
        ]
    ]);

       if ($this->request->is(['patch', 'post', 'put'])) {
        $review = $this->Reviews->patchEntity($review, $this->request->getData(), [
            'associated' => ['ReviewAnswers']
        ]);
        if ($this->Reviews->save($review)) {
            $this->Flash->success(__('The review has been saved.'));
            return $this->redirect(['action' => 'index']);
        }
        $this->Flash->error(__('The review could not be saved. Please, try again.'));
       }
        
        

        // Get options for dropdowns
        $statusOptions = \App\Model\Table\ReviewsTable::getStatusOptions();
        $reviewTypeOptions = \App\Model\Table\ReviewsTable::getReviewTypeOptions();
        $ratingOptions = \App\Model\Table\ReviewsTable::getRatingOptions();

       $this->set(compact('review', 'statusOptions', 'reviewTypeOptions', 'ratingOptions'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Review id.
     * @return \Cake\Http\Response|null|void Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $review = $this->Reviews->get($id);
        
        if ($this->Reviews->delete($review)) {
            $this->Flash->success(__('The review has been deleted.'));
        } else {
            $this->Flash->error(__('The review could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }

    /**
     * Bulk action method
     *
     * @return \Cake\Http\Response|null|void Redirects to index.
     */
    public function bulkAction()
    {
        $this->request->allowMethod(['post']);
        
        $action = $this->request->getData('bulk_action');
        $selectedIds = $this->request->getData('selected_reviews');

        if (empty($selectedIds)) {
            $this->Flash->error(__('Please select at least one review.'));
            return $this->redirect(['action' => 'index']);
        }

        $count = 0;
        switch ($action) {
            case 'approve':
                $count = $this->Reviews->updateAll(
                    ['status' => 'Approved'],
                    ['id IN' => $selectedIds]
                );
                $this->Flash->success(__('Successfully approved {0} review(s).', $count));
                break;
                
            case 'reject':
                $count = $this->Reviews->updateAll(
                    ['status' => 'Rejected'],
                    ['id IN' => $selectedIds]
                );
                $this->Flash->success(__('Successfully rejected {0} review(s).', $count));
                break;
                
            case 'delete':
                $count = $this->Reviews->deleteAll(['id IN' => $selectedIds]);
                $this->Flash->success(__('Successfully deleted {0} review(s).', $count));
                break;
                
            default:
                $this->Flash->error(__('Invalid bulk action selected.'));
        }

        return $this->redirect(['action' => 'index']);
    }

    /**
     * Quick status update via AJAX
     *
     * @param string|null $id Review id.
     * @return \Cake\Http\Response|null|void JSON response
     */
    public function updateStatus($id = null)
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        $review = $this->Reviews->get($id);
        $status = $this->request->getData('status');

        if (in_array($status, ['Pending', 'Approved', 'Rejected'])) {
            $review->status = $status;
            
            if ($this->Reviews->save($review)) {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode([
                        'success' => true,
                        'message' => 'Status updated successfully',
                        'status' => $status
                    ]));
            }
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode([
                'success' => false,
                'message' => 'Failed to update status'
            ]));
    }
}
