<?php

use Cake\Utility\Security;

$rememberData = $rememberData ?? [];
$password = '';
if (!empty($rememberData['password'])) {
    try {
        $password = Security::decrypt(base64_decode($rememberData['password']), Security::getSalt());
    } catch (Exception $e) {
        $password = '';
    }
}
?>

<!-- Removed login.css, now fully Tailwind-based -->
<section class="login-container min-h-screen flex items-center justify-center bg-[#FFEFE9] px-4 py-6">
    <div class="w-full max-w-[60%]">
        <div class="block md:flex md:items-normal justify-center lg:h-[91vh]" id="loginWrapper">
            <div class="bg-white rounded-lg rounded-tr-none roundedbr-none left-img-content hidden lg:flex lg:flex-col lg:justify-between lg:strciky lg:top:0 w-[60%] relative">
                <!-- <img src="<?= $this->Url->webroot('img/yoga-register.png') ?>" alt="Login Yoga image" /> -->
                 <a href="/" class="flex items-center justify-center">
                    <img src="<?= $this->Url->webroot('img/yoga-big.png') ?>" alt="Yoga logo" class="yoga-logo h-24">
                </a>
                <div class="img-container absolute top-[30%] w-full px-[20px]">
                    <img src="/img/login-img.png" alt="Login image" class="login-img w-full h-[250px] rounded-[8px] object-cover">
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-lg overflow-y-auto overflow-x-hidden">
                <!-- Header with logo only -->
                <div class="px-0 pt-4 pb-2 text-center">
                    <a href="/" class="inline-block mb-2">
                        <img src="<?= $this->Url->webroot('img/yoga-big.png') ?>" alt="Yoga logo" class="h-24">
                    </a>
                    <div class="flex items-center mb-2">
                        <div class="flex-1 border-t border-[#CFDFE2]"></div>
                        <h2 class="px-2 text-[24px] font-[700] text-[#000] font-[Open_Sans]">Login</h2>
                        <div class="flex-1 border-t border-[#CFDFE2]"></div>
                    </div>
                    <!-- Social buttons -->
                    <div class="flex gap-3 justify-center mb-2">
                        <button id="google-signin-btn" class="flex items-center justify-center rounded-[7px] border border-[#c05e47] px-4 py-1">
                            <img src="<?= $this->Url->webroot('img/google-icon.png') ?>" alt="Google" class="w-5 h-5 rounded-full">
                        </button>
                        <button class="flex items-center justify-center rounded-[7px] border border-[#c05e47] px-4 py-1">
                            <img src="<?= $this->Url->webroot('img/facebook-icon.png') ?>" alt="Facebook" class="w-5 h-5 rounded-full">
                        </button></div>
                </div>
                <!-- Flash messages -->
                <div class="px-6"><?= $this->Flash->render() ?></div>
    <div class="w-full max-w-md">
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Header with logo only -->
            <div class="px-6 pt-4 pb-2 text-center">
                <a href="/" class="inline-block mb-2">
                    <img src="<?= $this->Url->webroot('img/yoga-big.png') ?>" alt="Yoga logo" class="h-24">
                </a>
                <div class="flex items-center mb-2">
                    <div class="flex-1 border-t border-gray-200"></div>
                    <h2 class="px-2 text-base font-semibold text-gray-800">Login</h2>
                    <div class="flex-1 border-t border-gray-200"></div>
                </div>
                <!-- Social buttons -->
                <div class="flex gap-3 justify-center mb-2">
                    <button onclick="window.location.href='<?= $this->Url->build('/login/social?provider=google') ?>'" class="flex items-center justify-center w-10 h-10 border border-gray-300 rounded-full hover:bg-gray-50 transition shadow-sm">
                        <img src="<?= $this->Url->webroot('img/google-icon.png') ?>" alt="Google" class="w-5 h-5 rounded-full">
                    </button>
                    <button onclick="window.location.href='<?= $this->Url->build('/login/social?provider=facebook') ?>'" class="flex items-center justify-center w-10 h-10 border border-gray-300 rounded-full hover:bg-gray-50 transition shadow-sm">
                        <img src="<?= $this->Url->webroot('img/facebook-icon.png') ?>" alt="Facebook" class="w-5 h-5 rounded-full">
                    </button></div>
            </div>
            <!-- Flash messages -->
            <div class="px-6"><?= $this->Flash->render() ?></div>

                <div x-data="{ tab: 'first' }" class="px-6 pb-4">
                    <!-- Tabs -->
                    <div class="flex bg-[#FFEFE9] rounded-lg p-0.5 mb-3">
                        <button @click="tab = 'first'" :class="tab === 'first' ? 'bg-[#D87A61] text-white shadow-sm' : 'text-gray-600'" class="flex-1 py-1.5 px-3 rounded-md font-medium transition-all duration-200 text-sm">With Password</button>
                        <button @click="tab = 'second'" :class="tab === 'second' ? 'bg-[#D87A61] text-white shadow-sm' : 'text-gray-600'" class="flex-1 py-1.5 px-3 rounded-md font-medium transition-all duration-200 text-sm">With OTP</button>
                    </div>

                    <!-- Tab Panels -->
                    <div x-show="tab === 'first'" class="first-tab">
                        <form class="login-form" id="login-form" method="post" action="<?= $this->Url->build('/customer-login') ?>">
                            <input type="hidden" name="_csrfToken" value="<?= $this->request->getAttribute('csrfToken') ?>">
                            <input type="hidden" name="redirect" value="<?= h($this->request->getQuery('redirect')) ?>">
                            <div class="form-container space-y-2.5">
                                <!-- Radio buttons -->
                                <div class="radio-option-container flex gap-6 justify-center mb-2">
                                    <div class="radio-email flex items-center gap-2">
                                        <input type="radio" id="email" name="login" value="email" checked onclick="checkRadio(id)" class="w-4 h-4 text-[#D87A61] border-gray-300 focus:ring-[#D87A61] focus:ring-1">
                                        <label for="email" class="cursor-pointer select-none text-[14px] font-[600] text-[#231F20] font-[Open_Sans]">Email</label>
                                    </div>
                                    <div class="radio-phone flex items-center gap-2">
                                        <input type="radio" id="phone" name="login" value="phone" onclick="checkRadio(id)" class="w-4 h-4 text-[#D87A61] border-gray-300 focus:ring-[#D87A61] focus:ring-1">
                                        <label for="phone" class="cursor-pointer select-none text-[14px] font-[600] text-[#231F20] font-[Open_Sans]">Mobile No</label>
                                    </div>
                                </div>
                                
                                <!-- Email/Mobile input -->
                                <div class="relative form-field">
                                    <label for="email_or_mobile" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1 email-id">Email ID <span class="text-red-600">*</span></label>
                                    <input type="text" name="email_or_mobile" id="email_or_mobile" class="form-control email-id block w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] transition text-sm" required />
                                    <span class="mobile">
                                        <label for="mobile_input" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">Mobile No <span class="text-red-600">*</span></label>
                                        <div class="flex">
                                            <select id="country_code_select" name="country_code" class="dropdown-with-arrow px-2 py-2.5 border border-gray-300 border-r-0 bg-white text-[14px] font-[600] text-[#231F20] font-[Open_Sans] focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] rounded-l-lg appearance-none" style="min-width:4.5rem; max-width:5.5rem;">
                                                <option value="1">+1</option>
                                                <option value="7">+7</option>
                                                <option value="43">+43</option>
                                                <option value="44">+44</option>
                                                <option value="60">+60</option>
                                                <option value="65">+65</option>
                                                <option value="91">+91</option>
                                                <option value="92">+92</option>
                                                <option value="1 648">******</option>
                                            </select>
                                            <input type="text" name="email_or_mobile" id="mobile_input" maxlength="10" class="flex-1 px-3 py-2.5 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] transition text-sm" required onkeypress="validNumber(event);" />
                                        </div>
                                    </span>
                                </div>
                                
                                <!-- Password input -->
                                <div class="relative form-field">
                                    <label for="password" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">Password <span class="text-red-600">*</span></label>
                                    <div class="relative">
                                        <input type="password" name="password" id="password" value="<?= h($password) ?>" class="form-control password block w-full px-3 py-2.5 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] transition text-sm" required />
                                        <div class="eye-icon absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer text-gray-400 hover:text-[#D87A61]">
                                            <i class="fas fa-eye-slash"></i>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Remember me and forgot password -->
                                <div class="remember flex items-center justify-between">
                                    <label class="flex items-center gap-2 text-sm text-gray-700">
                                        <input type="checkbox" name="remember_me" class="w-4 h-4 text-[#D87A61] border-gray-300 rounded focus:ring-[#D87A61]">
                                        <span class="text-[14px] font-[600] text-[#231F20] font-[Open_Sans]">Remember Me</span>
                                    </label>
                                    <a href="<?= $this->Url->build(['controller' => 'Login', 'action' => 'forgotPassword']) ?>" class="text-[14px] font-[600] font-[Open_Sans] text-[#D87A61] hover:text-[#c05e47] hover:underline">Forgot Password?</a>
                                </div>
                                
                                <!-- Login button -->
                                <button type="submit" class="btn-login w-full bg-[#D87A61] hover:bg-[#c05e47] text-white block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] py-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:ring-offset-2">Login</button>
                                
                                <!-- Terms -->
                                <p class="text-[12px] font-[600] text-[#231F20] font-[Open_Sans] text-center">By continuing, you agree to <a href="<?= h($configSettings['WP_TERMS_CONDITIONS']) ?>" class="text-[#D87A61] hover:underline font-[700]">Terms of Use</a> and <a href="<?= h($configSettings['WP_PRIVACY_POLICY']) ?>" class="text-[#D87A61] hover:underline font-[700]">Privacy Policy</a> of yoga.in</p>
                            </div>
                        </form>
                    </div>
                    <div x-show="tab === 'second'" class="second-tab">
                        <form class="login-form" id="otp-form">
                            <input type="hidden" name="_csrfToken" value="<?= $this->request->getAttribute('csrfToken') ?>">
                            <input type="hidden" name="redirect" value="<?= h($this->request->getQuery('redirect')) ?>">
                            <div class="form-container space-y-2.5">
                                <!-- Radio buttons -->
                                <div class="radio-option-container flex gap-6 justify-center mb-2">
                                    <div class="radio-email flex items-center gap-2">
                                        <input type="radio" id="otp-email" name="login" value="email" checked onclick="checkOtpRadio(id)" class="w-4 h-4 text-[#D87A61] border-gray-300 focus:ring-[#D87A61] focus:ring-1">
                                        <label for="otp-email" class="cursor-pointer select-none text-[14px] font-[600] text-[#231F20] font-[Open_Sans]">Email</label>
                                    </div>
                                    <div class="radio-phone flex items-center gap-2">
                                        <input type="radio" id="otp-phone" name="login" value="phone" onclick="checkOtpRadio(id)" class="w-4 h-4 text-[#D87A61] border-gray-300 focus:ring-[#D87A61] focus:ring-1">
                                        <label for="otp-phone" class="cursor-pointer select-none text-[14px] font-[600] text-[#231F20] font-[Open_Sans]">Mobile No</label>
                                    </div>
                                </div>
                                
                                <!-- Email/Mobile input for OTP -->
                                <div class="relative form-field">
                                    <label for="otp_email_or_mobile" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1 otp-email">Email ID <span class="text-red-600">*</span></label>
                                    <input type="text" name="otp_email_or_mobile" id="otp_email_or_mobile" class="form-control otp-email block w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] transition text-sm" required />
                                    <span class="otp-mobile">
                                        <label for="otp_mobile_input" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans] mb-1">Mobile No <span class="text-red-600">*</span></label>
                                        <div class="flex">
                                            <select id="otp_country_code_select" name="country_code" class="code px-2 py-2.5 border border-gray-300 border-r-0 bg-white focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] rounded-l-lg appearance-none text-[14px] font-[600] text-[#231F20] font-[Open_Sans]" style="min-width:4.5rem; max-width:5.5rem;">
                                                <option value="1">+1</option>
                                                <option value="7">+7</option>
                                                <option value="43">+43</option>
                                                <option value="44">+44</option>
                                                <option value="60">+60</option>
                                                <option value="65">+65</option>
                                                <option value="91">+91</option>
                                                <option value="92">+92</option>
                                                <option value="1 648">******</option>
                                            </select>
                                            <input type="text" name="email_or_mobile" id="otp_mobile_input" maxlength="10" class="flex-1 px-3 py-2.5 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] transition text-sm" required onkeypress="validNumber(event);" />
                                        </div>
                                    </span>
                                    <button type="button" class="btn-send-otp w-full bg-[#D87A61] hover:bg-[#c05e47] text-white block text-[14px] font-[600] font-[Open_Sans] py-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:ring-offset-2 mt-2" id="send-otp-btn" onclick="sendOTP();">Send OTP</button>
                                </div>
                                
                                <!-- OTP Input Section -->
                                <div class="relative form-field otp-wrapper">
                                    <div class="otp-form space-y-2">
                                        <p class="otp-sent-msg text-green-600 text-sm font-medium"></p>
                                        <div class="otp-msg space-y-2">
                                            <label for="complete-otp" class="block text-[14px] font-[600] text-[#231F20] font-[Open_Sans]">Enter Verification Code <span class="text-red-600">*</span></label>
                                            <div class="flex gap-2 justify-start">
                                                <input type="text" class="form-control form-otp otp-input w-[25%] h-[42px] text-center border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] text-lg font-semibold" maxlength="1" data-index="1" />
                                                <input type="text" class="form-control form-otp otp-input w-[25%] h-[42px] text-center border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] text-lg font-semibold" maxlength="1" data-index="2" />
                                                <input type="text" class="form-control form-otp otp-input w-[25%] h-[42px] text-center border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] text-lg font-semibold" maxlength="1" data-index="3" />
                                                <input type="text" class="form-control form-otp otp-input w-[25%] h-[42px] text-center border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] text-lg font-semibold" maxlength="1" data-index="4" />
                                            </div>
                                            <input type="hidden" name="otp" id="complete-otp" />
                                            <p class="text-xs text-red-600 font-bold text-center" id="otp-timer"></p>
                                            <p class="text-[14px] font-[600] text-[#231F20] font-[Open_Sans] text-center">Didn't receive a code? <button type="button" class="text-[#D87A61] hover:text-[#c05e47] underline" id="resend-otp-btn" disabled>Resend OTP</button></p>
                                            <p class="text-[12px] font-[600] text-[#231F20] font-[Open_Sans] text-center">Verification code sent to <span class="verify-mail font-semibold"><EMAIL></span></p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Login button for OTP -->
                                <button type="button" class="btn-otp w-full bg-[#D87A61] hover:bg-[#c05e47] text-white text-[14px] font-[600] font-[Open_Sans] py-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:ring-offset-2" id="verify-otp-btn">Login</button>
                                
                                <!-- Terms -->
                                <p class="text-[12px] font-[600] text-[#231F20] font-[Open_Sans] text-center">By continuing, you agree to <a href="<?= h($configSettings['WP_TERMS_CONDITIONS']) ?>" class="text-[#D87A61] hover:underline font-[700]">Terms of Use</a> and <a href="<?= h($configSettings['WP_PRIVACY_POLICY']) ?>" class="text-[#D87A61] hover:underline font-[700]">Privacy Policy</a> of yoga.in</p>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Footer -->
                <div class="px-4 py-3 border-t text-center">
                    <div class="inline-block">
                        <a href="<?= $this->Url->build('/signup') ?>?redirect=<?= $qryRedirect ?>" class="inline-block bg-[#fff] border-1 border-[#c05e47] hover:bg-[#c05e47] px-4 py-2 rounded-lg transition-colors duration-200 text-[14px] font-[600] text-[#231F20] font-[Open_Sans] ml-1 group">
                            New User? 
                            <span class="text-[#D87A61] group-hover:text-white">Create an Account</span>
                        </a>
                    </div>
                <!-- Tab Panels -->
                <div x-show="tab === 'first'" class="first-tab">
                    <form class="login-form" id="login-form" method="post" action="<?= $this->Url->build('/login/customer-login') ?>">
                        <input type="hidden" name="_csrfToken" value="<?= $this->request->getAttribute('csrfToken') ?>">
                        <input type="hidden" name="redirect" value="<?= h($this->request->getQuery('redirect')) ?>">
                        <div class="form-container space-y-2.5">
                            <!-- Radio buttons -->
                            <div class="radio-option-container flex gap-6 justify-center mb-2">
                                <div class="radio-email flex items-center gap-2">
                                    <input type="radio" id="email" name="login" value="email" checked onclick="checkRadio(id)" class="w-4 h-4 text-[#D87A61] border-gray-300 focus:ring-[#D87A61] focus:ring-1">
                                    <label for="email" class="cursor-pointer select-none text-gray-700 font-medium text-sm">Email</label>
                                </div>
                                <div class="radio-phone flex items-center gap-2">
                                    <input type="radio" id="phone" name="login" value="phone" onclick="checkRadio(id)" class="w-4 h-4 text-[#D87A61] border-gray-300 focus:ring-[#D87A61] focus:ring-1">
                                    <label for="phone" class="cursor-pointer select-none text-gray-700 font-medium text-sm">Mobile No</label>
                                </div>
                            </div>
                            
                            <!-- Email/Mobile input -->
                            <div class="relative form-field">
                                <input type="hidden" id="login_type" name="type" value="email">
                                <label for="email_or_mobile" class="block text-sm font-medium text-gray-700 mb-1 email-id">Email ID</label>
                                <input type="text" id="email_or_mobile" class="form-control email-id block w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] transition text-sm" />
                                <span class="mobile">
                                    <label for="mobile_input" class="block text-sm font-medium text-gray-700 mb-1">Mobile No</label>
                                    <div class="flex">
                                        <select id="country_code_select" name="country_code" class="dropdown-with-arrow px-2 py-2.5 border border-gray-300 border-r-0 bg-white text-gray-700 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] rounded-l-lg appearance-none text-sm" style="min-width:4.5rem; max-width:5.5rem;">
                                            <option value="1">+1</option>
                                            <option value="7">+7</option>
                                            <option value="43">+43</option>
                                            <option value="44">+44</option>
                                            <option value="60">+60</option>
                                            <option value="65">+65</option>
                                            <option selected value="91">+91</option>
                                            <option value="92">+92</option>
                                            <option value="1 648">******</option>
                                        </select>
                                        <input type="text" id="mobile_input" maxlength="10" class="flex-1 px-3 py-2.5 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] transition text-sm" onkeypress="validNumber(event);" />
                                    </div>
                                </span>
                            </div>
                            
                            <!-- Password input -->
                            <div class="relative form-field">
                                <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                                <div class="relative">
                                    <input type="password" name="password" id="password" value="<?= h($password) ?>" class="form-control password block w-full px-3 py-2.5 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] transition text-sm" required />
                                    <div class="eye-icon absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer text-gray-400 hover:text-[#D87A61]">
                                        <i class="fas fa-eye-slash"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Remember me and forgot password -->
                            <div class="remember flex items-center justify-between">
                                <label class="flex items-center gap-2 text-sm text-gray-700">
                                    <input type="checkbox" name="remember_me" class="w-4 h-4 text-[#D87A61] border-gray-300 rounded focus:ring-[#D87A61]">
                                    <span>Remember Me</span>
                                </label>
                                <a href="<?= $this->Url->build(['controller' => 'Login', 'action' => 'forgotPassword']) ?>" class="text-sm text-[#D87A61] hover:text-[#c05e47] hover:underline">Forgot Password?</a>
                            </div>
                            
                            <!-- Login button -->
                            <button type="submit" class="btn-login w-full bg-[#D87A61] hover:bg-[#c05e47] text-white font-semibold py-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:ring-offset-2">Login</button>
                            
                            <!-- Terms -->
                            <p class="text-xs text-gray-500 text-center">By continuing, you agree to <a href="<?= h($configSettings['WP_TERMS_CONDITIONS']) ?>" class="text-[#D87A61] hover:underline">Terms of Use</a> and <a href="<?= h($configSettings['WP_PRIVACY_POLICY']) ?>" class="text-[#D87A61] hover:underline">Privacy Policy</a> of yoga.in</p>
                        </div>
                    </form>
                </div>
                <div x-show="tab === 'second'" class="second-tab">
                    <form class="login-form" id="otp-form">
                        <input type="hidden" name="_csrfToken" value="<?= $this->request->getAttribute('csrfToken') ?>">
                        <input type="hidden" name="redirect" value="<?= h($this->request->getQuery('redirect')) ?>">
                        <div class="form-container space-y-2.5">
                            <!-- Radio buttons -->
                            <div class="radio-option-container flex gap-6 justify-center mb-2">
                                <div class="radio-email flex items-center gap-2">
                                    <input type="radio" id="otp-email" name="login" value="email" checked onclick="checkOtpRadio(id)" class="w-4 h-4 text-[#D87A61] border-gray-300 focus:ring-[#D87A61] focus:ring-1">
                                    <label for="otp-email" class="cursor-pointer select-none text-gray-700 font-medium text-sm">Email</label>
                                </div>
                                <div class="radio-phone flex items-center gap-2">
                                    <input type="radio" id="otp-phone" name="login" value="phone" onclick="checkOtpRadio(id)" class="w-4 h-4 text-[#D87A61] border-gray-300 focus:ring-[#D87A61] focus:ring-1">
                                    <label for="otp-phone" class="cursor-pointer select-none text-gray-700 font-medium text-sm">Mobile No</label>
                                </div>
                            </div>
                            
                            <!-- Email/Mobile input for OTP -->
                            <div class="relative form-field">
                                <label for="otp_email_or_mobile" class="block text-sm font-medium text-gray-700 mb-1 otp-email">Email ID</label>
                                <input type="text" name="otp_email_or_mobile" id="otp_email_or_mobile" class="form-control otp-email block w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] transition text-sm" required />
                                <span class="otp-mobile">
                                    <label for="otp_mobile_input" class="block text-sm font-medium text-gray-700 mb-1">Mobile No</label>
                                    <div class="flex">
                                        <select id="otp_country_code_select" name="country_code" class="code px-2 py-2.5 border border-gray-300 border-r-0 bg-white text-gray-700 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] rounded-l-lg appearance-none text-sm" style="min-width:4.5rem; max-width:5.5rem;">
                                            <option value="1">+1</option>
                                            <option value="7">+7</option>
                                            <option value="43">+43</option>
                                            <option value="44">+44</option>
                                            <option value="60">+60</option>
                                            <option value="65">+65</option>
                                            <option selected value="91">+91</option>
                                            <option value="92">+92</option>
                                            <option value="1 648">******</option>
                                        </select>
                                        <input type="text" name="email_or_mobile" id="otp_mobile_input" maxlength="10" class="flex-1 px-3 py-2.5 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] transition text-sm" required onkeypress="validNumber(event);" />
                                    </div>
                                </span>
                                <button type="button" class="btn-send-otp w-full bg-[#D87A61] hover:bg-[#c05e47] text-white font-semibold py-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:ring-offset-2 mt-2" id="send-otp-btn" onclick="sendOTP();">Send OTP</button>
                            </div>
                            
                            <!-- OTP Input Section -->
                            <div class="relative form-field otp-wrapper">
                                <div class="otp-form space-y-2">
                                    <p class="otp-sent-msg text-green-600 text-sm font-medium"></p>
                                    <div class="otp-msg space-y-2">
                                        <label for="complete-otp" class="block text-sm font-medium text-gray-700">Enter Verification Code</label>
                                        <div class="flex gap-2 justify-center">
                                            <input type="text" class="form-control form-otp otp-input w-10 h-10 text-center border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] text-lg font-semibold" maxlength="1" data-index="1" />
                                            <input type="text" class="form-control form-otp otp-input w-10 h-10 text-center border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] text-lg font-semibold" maxlength="1" data-index="2" />
                                            <input type="text" class="form-control form-otp otp-input w-10 h-10 text-center border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] text-lg font-semibold" maxlength="1" data-index="3" />
                                            <input type="text" class="form-control form-otp otp-input w-10 h-10 text-center border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:border-[#D87A61] text-lg font-semibold" maxlength="1" data-index="4" />
                                        </div>
                                        <input type="hidden" name="otp" id="complete-otp" />
                                        <p class="text-xs text-red-600 font-bold text-center" id="otp-timer"></p>
                                        <p class="text-xs text-center">Didn't receive a code? <button type="button" class="text-[#D87A61] hover:text-[#c05e47] underline" id="resend-otp-btn" disabled>Resend OTP</button></p>
                                        <p class="text-xs text-center">Verification code sent to <span class="verify-mail font-semibold"><EMAIL></span></p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Login button for OTP -->
                            <button type="button" class="btn-otp w-full bg-[#D87A61] hover:bg-[#c05e47] text-white font-semibold py-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-[#D87A61] focus:ring-offset-2" id="verify-otp-btn">Login</button>
                            
                            <!-- Terms -->
                            <p class="text-xs text-gray-500 text-center">By continuing, you agree to <a href="<?= h($configSettings['WP_TERMS_CONDITIONS']) ?>" class="text-[#D87A61] hover:underline">Terms of Use</a> and <a href="<?= h($configSettings['WP_PRIVACY_POLICY']) ?>" class="text-[#D87A61] hover:underline">Privacy Policy</a> of yoga.in</p>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="bg-[#FFEFE9] px-4 py-3 border-t text-center">
                <div class="inline-block">
                    <a href="<?= $this->Url->build('/signup') ?>?redirect=<?= $qryRedirect ?? '' ?>" class="inline-block bg-[#fff] border-1 border-[#c05e47] hover:bg-[#c05e47] font-semibold px-4 py-2 rounded-lg transition-colors duration-200 text-sm ml-1 group">
                        New User? 
                        <span class="text-[#D87A61] group-hover:text-white">Create an Account</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- SweetAlert2 CDN -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- Alpine.js CDN -->
<script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
<!-- FontAwesome CDN -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Common elements
        const loginForm = document.getElementById('login-form');
        const otpForm = document.getElementById('otp-form');
        const emailOrMobileInput = document.querySelector('input[name="email_or_mobile"]');
        const passwordInput = document.querySelector('input[name="password"]');
        const otpEmailOrMobileInput = document.getElementById('otp_email_or_mobile');
        const sendOtpBtn = document.getElementById('send-otp-btn');
        const verifyOtpBtn = document.getElementById('verify-otp-btn');
        const resendOtpBtn = document.getElementById('resend-otp-btn');
        const otpInputs = document.querySelectorAll('.otp-input');
        const completeOtpInput = document.getElementById('complete-otp');
        const otpTimer = document.getElementById('otp-timer');
        const queryRedirect = '<?php echo  $qryRedirect ?? '' ?>'

        // Toggle password visibility
        const eyeIcons = document.querySelectorAll('.eye-icon');
        eyeIcons.forEach(icon => {
            icon.addEventListener('click', function() {
                const passwordField = this.previousElementSibling;
                const eyeIcon = this.querySelector('i');

                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    eyeIcon.classList.remove('fa-eye-slash');
                    eyeIcon.classList.add('fa-eye');
                } else {
                    passwordField.type = 'password';
                    eyeIcon.classList.remove('fa-eye');
                    eyeIcon.classList.add('fa-eye-slash');
                }
            });
        });

        // Email/Mobile validation for first tab
        if (emailOrMobileInput) {
            let debounceTimer;

            emailOrMobileInput.addEventListener('input', function() {
                clearTimeout(debounceTimer);

                // Remove validation messages while typing
                this.classList.remove('is-invalid', 'is-valid');
                const feedbackElement = this.parentElement.querySelector('.validation-feedback');
                if (feedbackElement) feedbackElement.remove();

                // Debounce the validation
                debounceTimer = setTimeout(() => {
                    const value = this.value.trim();

                    // Skip validation if empty
                    if (!value) return;

                    // Check if it looks like an email attempt
                    const looksLikeEmail = /[a-zA-Z@]/.test(value);

                    if (looksLikeEmail) {
                        // Validate email format
                        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        const isValidEmail = emailRegex.test(value);
                     
                        // Show validation feedback
                        this.classList.add(isValidEmail ? 'is-valid' : 'is-invalid');
                       
                        clearInlineMessage(this.parentElement);
                        if (!isValidEmail) {
                            const parent = this.parentElement;
                            // Remove existing validation feedback if present
                            const existingFeedback = parent.querySelector('.validation-feedback');
                            if (existingFeedback) {
                                existingFeedback.remove();
                            }
                           
                            // Create and append new feedback
                            const feedback = document.createElement('div');
                            feedback.className = 'validation-feedback text-red-500 text-xs mt-1';
                            feedback.textContent = 'Please enter a valid email address';
                            parent.appendChild(feedback);
                        }
                    } else {
                        // Validate mobile number (exactly 10 digits)
                        const isValidMobile = /^\d{10}$/.test(value);

                        // Show validation feedback
                        this.classList.add(isValidMobile ? 'is-valid' : 'is-invalid');

                        if (!isValidMobile) {
                            clearInlineMessage(this.parentElement);
                            const parent = this.parentElement;
                            // Remove existing validation feedback if present
                            const existingFeedback = parent.querySelector('.validation-feedback');
                            if (existingFeedback) {
                                existingFeedback.remove();
                            }

                            // Create and append new feedback
                            const feedback = document.createElement('div');
                            feedback.className = 'validation-feedback text-red-500 text-xs mt-1';
                            feedback.textContent = 'Please enter a valid 10-digit mobile number';
                            parent.appendChild(feedback);
                        }
                    }
                }, 500);
            });
        }

        // Email/Mobile validation for OTP tab
        if (otpEmailOrMobileInput) {
            let debounceTimer;
            
            const showValidationFeedback = (input, message) => {
                const parent = input.closest('.form-field');
                removeValidationFeedback(input);
                const feedback = document.createElement('div');
                feedback.className = 'validation-feedback text-red-500 text-xs mt-1';
                feedback.textContent = message;
                parent.appendChild(feedback);
            };

            const removeValidationFeedback = (input) => {
                const parent = input.closest('.form-field');
                const existingFeedback = parent.querySelector('.validation-feedback');
                if (existingFeedback) existingFeedback.remove();
            };


            otpEmailOrMobileInput.addEventListener('input', function() {
                clearTimeout(debounceTimer);
                removeValidationFeedback(this);
                // Remove validation messages while typing
                this.classList.remove('is-invalid', 'is-valid');
                const feedbackElement = this.parentElement.querySelector('.validation-feedback');
                if (feedbackElement) feedbackElement.remove();

                // Debounce the validation
                debounceTimer = setTimeout(() => {
                    const value = this.value.trim();

                    // Skip validation if empty
                    if (!value) return;

                    // Check if it looks like an email attempt
                    const looksLikeEmail = /[a-zA-Z@]/.test(value);

                    if (looksLikeEmail) {
                        // Validate email format
                        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        const isValidEmail = emailRegex.test(value);
                      
                        // Show validation feedback
                        this.classList.add(isValidEmail ? 'is-valid' : 'is-invalid');
                      
                        if (!isValidEmail) {   
                            clearInlineMessage(this.parentElement.parentElement);
                            showValidationFeedback(this, 'Please enter a valid email address');
                        } else {
                            removeValidationFeedback(this);
                        }

                    } else {
                        // Validate mobile number (exactly 10 digits)
                        const isValidMobile = /^\d{10}$/.test(value);

                        // Show validation feedback
                        this.classList.add(isValidMobile ? 'is-valid' : 'is-invalid');

                        if (!isValidMobile) {
                            clearInlineMessage(this.parentElement.parentElement);
                            showValidationFeedback(this, 'Please enter a valid 10-digit mobile number');
                        } else {
                            removeValidationFeedback(this);
                        }
                    }
                }, 500);
            });
        }

        // Initialize OTP inputs
        if (otpInputs.length) {
            otpInputs.forEach(input => {
                input.addEventListener('input', function(e) {
                    // Allow only one digit
                    this.value = this.value.replace(/\D/g, '').substring(0, 1);

                    const index = parseInt(this.dataset.index);

                    // Collect all OTP digits
                    let otp = '';
                    otpInputs.forEach(input => {
                        otp += input.value;
                    });
                    completeOtpInput.value = otp;

                    // Auto-focus next input
                    if (this.value && index < 4) {
                        otpInputs[index].focus();
                    }
                });

                // Handle backspace to go to previous input
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Backspace' && !this.value) {
                        const index = parseInt(this.dataset.index);
                        if (index > 1) {
                            otpInputs[index - 2].focus();
                        }
                    }
                });
            });
        }

        // Timer functions for OTP
        let countdown;
        let remainingTime = 60;

        function startTimer() {
            remainingTime = 60;
            updateTimerText();

            if (countdown) {
                clearInterval(countdown);
            }

            countdown = setInterval(function() {
                remainingTime--;
                updateTimerText();

                if (remainingTime <= 0) {
                    clearInterval(countdown);
                    if (resendOtpBtn) {
                        resendOtpBtn.disabled = false;
                        resendOtpBtn.classList.remove('disabled:cursor-not-allowed');
                    }
                }
            }, 1000);
        }

        function updateTimerText() {
            if (otpTimer) {
                otpTimer.textContent = `Resend OTP in ${remainingTime} seconds`;
            }
        }

        // Handle login form submission with inline validation
        if (loginForm) {
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Always update input names before collecting form data
                if (typeof updateInputNames === 'function') updateInputNames();

                // Clear previous validation messages
                const existingFeedbacks = loginForm.querySelectorAll('.validation-feedback');
                existingFeedbacks.forEach(fb => fb.remove());
                
                // Get the currently active input (email or mobile)
                const emailInput = document.getElementById('email_or_mobile');
                const mobileInput = document.getElementById('mobile_input');
                const emailBlock = document.getElementsByClassName('email-id')[0];
                const activeInput = (emailBlock && emailBlock.style.display !== 'none') ? emailInput : mobileInput;
                
                activeInput.classList.remove('is-invalid');
                passwordInput.classList.remove('is-invalid');

                const emailOrMobile = activeInput.value.trim();
                const password = passwordInput.value.trim();

                // Validation flag
                let isValid = true;

                // Helper function to show validation feedback
                function showValidationError(inputElement, message) {
                    inputElement.classList.add('is-invalid');
                    const feedback = document.createElement('div');
                    feedback.className = 'validation-feedback text-red-500 text-xs mt-1';
                    feedback.textContent = message;
                    inputElement.parentElement.appendChild(feedback);
                }

                // Validate email or mobile
                if (!emailOrMobile) {
                    showValidationError(activeInput, 'Please enter your email or mobile number');
                    isValid = false;
                } else {
                    const looksLikeEmail = /[a-zA-Z@]/.test(emailOrMobile);
                    if (looksLikeEmail) {
                        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        if (!emailRegex.test(emailOrMobile)) {
                            showValidationError(activeInput, 'Please enter a valid email address');
                            isValid = false;
                        }
                    } else {
                        if (!/^\d{10}$/.test(emailOrMobile)) {
                            showValidationError(activeInput, 'Please enter a valid 10-digit mobile number');
                            isValid = false;
                        }
                    }
                }

                // Validate password
                if (!password) {
                    showValidationError(passwordInput, 'Please enter your password');
                    isValid = false;
                }

                if (!isValid) {
                    // Don't proceed with form submission
                    return;
                }

                // If valid, submit via fetch to the form's action attribute
                const formData = new FormData(this);
                const csrfToken = document.querySelector('input[name="_csrfToken"]').value;
                formData.append('reqdirect_url', queryRedirect);

                // Debug: Log form data
                console.log('Form data being sent:');
                for (let pair of formData.entries()) {
                    console.log(pair[0] + ': ' + pair[1]);
                }

                // Use the form's action attribute for the fetch URL
                const fetchUrl = this.getAttribute('action') || window.location.href;
                console.log('Fetch URL:', fetchUrl);

                fetch(fetchUrl, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-Token': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    },
                    body: formData
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Login response:', data);
                    if (data.success) {
                        window.location.href = data.redirect;
                    } else {
                        // Show error message
                        if (typeof Swal !== 'undefined') {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: data.message || 'Login failed. Please check your credentials.',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            alert(data.message || 'Login failed. Please check your credentials.');
                        }
                    }
                })
                .catch(error => {
                    console.error('Login error:', error);
                    let formError = document.getElementById('form-error');
                    if (!formError) {
                        formError = document.createElement('div');
                        formError.id = 'form-error';
                        formError.className = 'validation-feedback text-red-600 text-sm mb-3';
                        loginForm.prepend(formError);
                    }
                    formError.textContent = 'An unexpected error occurred. Please try again.';
                    
                    // Also show alert as fallback
                    alert('Login failed. Please try again.');
                });
            });
        }

        // Handle Send OTP button click
        if (sendOtpBtn) {
            sendOtpBtn.addEventListener('click', function() {
                // Clear any previous inline messages
                clearInlineMessage(otpEmailOrMobileInput.parentElement);

                const emailOrMobile = otpEmailOrMobileInput.value.trim();

                if (!emailOrMobile) {
                    showInlineMessage(otpEmailOrMobileInput.parentElement.parentElement, 'Please enter your email or mobile number', 'error');
                    return;
                }

                // Validate email or mobile format
                const looksLikeEmail = /[a-zA-Z@]/.test(emailOrMobile);
                let isValid = true;

                if (looksLikeEmail) {
                    // Validate email format
                    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                    if (!emailRegex.test(emailOrMobile)) {
                        clearInlineMessage(otpEmailOrMobileInput.parentElement.parentElement);
                        showInlineMessage(otpEmailOrMobileInput.parentElement.parentElement, 'Please enter a valid email address', 'error');
                        isValid = false;
                    }
                } else {
                    // Validate mobile number (exactly 10 digits)
                    if (!/^\d{10}$/.test(emailOrMobile)) {
                        clearInlineMessage(otpEmailOrMobileInput.parentElement.parentElement);
                        showInlineMessage(otpEmailOrMobileInput.parentElement.parentElement, 'Please enter a valid 10-digit mobile number', 'error');
                        isValid = false;
                    }
                }

                if (!isValid) {
                    return;
                }

                // Show loading state
                sendOtpBtn.textContent = 'Sending...';
                sendOtpBtn.disabled = true;

                // Send OTP request
                const formData = new FormData();
                formData.append('email_or_mobile', emailOrMobile);
                const csrfToken = document.querySelector('input[name="_csrfToken"]').value;

                fetch('<?= $this->Url->build(['controller' => 'Login', 'action' => 'sendOtp']) ?>', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-Token': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Reset button state
                        sendOtpBtn.textContent = 'Send OTP';
                        sendOtpBtn.disabled = false;

                        // Clear old messages
                        clearInlineMessage(otpEmailOrMobileInput.parentElement);

                        if (data.success) {
                            // Update message based on input type
                            const otpSentMsg = document.querySelector('.otp-sent-msg');
                            if (otpSentMsg) {
                                otpSentMsg.textContent = data.input_type === 'mobile' ?
                                    'The OTP is sent to your mobile number' :
                                    'The OTP is sent to your email address';
                            }

                            // Start countdown timer
                            startTimer();

                            // Disable resend button initially
                            if (resendOtpBtn) {
                                resendOtpBtn.disabled = true;
                                resendOtpBtn.classList.add('disabled:cursor-not-allowed');
                            }

                            // Focus first OTP input
                            if (otpInputs.length) {
                                otpInputs[0].focus();
                            }

                            // Show success message
                            Swal.fire({
                                icon: 'success',
                                title: 'OTP Sent',
                                text: data.message,
                                confirmButtonText: 'OK'
                            });
                        } else {
                            // Show error message
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: data.message || 'Failed to send OTP. Please try again.',
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch(error => {
                        // Reset button state
                        sendOtpBtn.textContent = 'Send OTP';
                        sendOtpBtn.disabled = false;

                        // Show error message below input
                        clearInlineMessage(otpEmailOrMobileInput.parentElement);
                        showInlineMessage(otpEmailOrMobileInput.parentElement, 'An unexpected error occurred. Please try again.', 'error');
                    });
            });
        }

        // Helper functions for inline messages
        function showInlineMessage(container, message, type = 'error') {
            // Remove existing message
            const existingMsg = container.querySelector('.inline-message');
            if (existingMsg) existingMsg.remove();

            const msgDiv = document.createElement('div');
            msgDiv.className = 'inline-message mt-1 text-sm ' + (type === 'error' ? 'text-red-600' : 'text-green-600');
            msgDiv.textContent = message;
            container.appendChild(msgDiv);
        }

        function clearInlineMessage(container) {
            const existingMsg = container.querySelector('.inline-message');
            if (existingMsg) existingMsg.remove();
        }


        // Handle Verify OTP button click
        if (verifyOtpBtn) {
            verifyOtpBtn.addEventListener('click', function() {
                const otp = completeOtpInput.value;
                const emailOrMobile = otpEmailOrMobileInput.value.trim();

                if (!otp || otp.length !== 4) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Please enter the complete 4-digit OTP',
                        confirmButtonText: 'OK'
                    });
                    return;
                }

                // Show loading state
                verifyOtpBtn.textContent = 'Verifying...';
                verifyOtpBtn.disabled = true;

                // Verify OTP request
                const formData = new FormData();
                formData.append('email_or_mobile', emailOrMobile);
                formData.append('otp', otp);
                formData.append('reqdirect_url', queryRedirect);
                const csrfToken = document.querySelector('input[name="_csrfToken"]').value;

                fetch('<?= $this->Url->build(['controller' => 'Login', 'action' => 'verifyOtp']) ?>', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-Token': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Reset button state
                        verifyOtpBtn.textContent = 'Login';
                        verifyOtpBtn.disabled = false;

                        if (data.success) {
                            // Show success message
                            window.location.href = data.redirect;
                            // Swal.fire({
                            //     icon: 'success',
                            //     title: 'Success!',
                            //     text: data.message,
                            //     confirmButtonText: 'OK'
                            // }).then(() => {
                            //     if (data.redirect) {
                            //         window.location.href = data.redirect;
                            //     }
                            // });
                        } else {
                            // Show error message
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: data.message || 'Invalid OTP. Please try again.',
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch(error => {
                        // Reset button state
                        verifyOtpBtn.textContent = 'Login';
                        verifyOtpBtn.disabled = false;

                        // Show error message
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'An unexpected error occurred. Please try again.',
                            confirmButtonText: 'OK'
                        });
                    });
            });
        }

        // Handle Resend OTP button click
        if (resendOtpBtn) {
            resendOtpBtn.addEventListener('click', function() {
                // Trigger the send OTP function
                if (sendOtpBtn) {
                    sendOtpBtn.click();
                }
            });
        }

        // Add CSS for validation styling
        document.head.insertAdjacentHTML('beforeend', `
        <style>
        .is-invalid {
            border-color: #dc3545 !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        .is-valid {
            border-color: #28a745 !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 0 0s.46 1.4.0 2.47z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        /* Custom dropdown arrow using pure CSS */
        .dropdown-with-arrow {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.25rem center;
            background-repeat: no-repeat;
            background-size: 0.75rem 0.75rem;
            transition: background-image 0.2s ease-in-out;
            padding-right: 1.5rem !important;
        }
        .dropdown-with-arrow.open {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M14 12l-4-4-4 4'/%3e%3c/svg%3e");
        }
        /* Country code dropdown optimization */
        #country_code_select, #otp_country_code_select {
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
        #country_code_select option, #otp_country_code_select option {
            font-size: 0.875rem;
            padding: 0.5rem;
        }
        /* Override Alpine.js default styling for better consistency */
        [x-cloak] { display: none !important; }
        /* Radio button custom styling */
        input[type="radio"] {
            accent-color: #D87A61;
            color: #D87A61;
        }
        input[type="radio"]:checked {
            background-color: #D87A61;
            border-color: #D87A61;
        }
        input[type="radio"]:focus {
            ring-color: #D87A61;
            ring-opacity: 0.5;
            box-shadow: 0 0 0 2px rgba(216, 122, 97, 0.2);
        }
        /* Checkbox custom styling */
        input[type="checkbox"] {
            accent-color: #D87A61;
        }
        input[type="checkbox"]:checked {
            background-color: #D87A61;
            border-color: #D87A61;
        }
        input[type="checkbox"]:focus {
            ring-color: #D87A61;
            ring-opacity: 0.5;
            box-shadow: 0 0 0 2px rgba(216, 122, 97, 0.2);
        }
        </style>
        `);

        const mobregExp = /^[0-9\b]+$/;
        document.getElementsByClassName("mobile")[0].style.display = "none";
        window.checkRadio = function (id) {
            console.log("The id is:", id);
            if(id == "phone"){
                document.getElementsByClassName("mobile")[0].style.display = "block";
                document.getElementsByClassName("email-id")[0].style.display = "none";
                document.getElementsByClassName("email-id")[1].style.display = "none";
            }
            else{
                document.getElementsByClassName("mobile")[0].style.display = "none";
                document.getElementsByClassName("email-id")[0].style.display = "block";
                document.getElementsByClassName("email-id")[1].style.display = "block";
            }
        };
        window.validNumber = function(event){
            var mobno = event.target.value + event.key;
            if(!mobregExp.test(mobno)){
                event.preventDefault();
            }
        }

        //disable otp wrapper and enable after click send otp button
        document.getElementsByClassName("otp-wrapper")[0].style.display = "none";
        document.getElementById("verify-otp-btn").style.display = "none";
        window.sendOTP = function(){
            document.getElementsByClassName("otp-wrapper")[0].style.display = "block";
            document.getElementById("verify-otp-btn").style.display = "block";
            document.getElementById("send-otp-btn").style.display = "none";
        }

        //Code for mobile view
        document.getElementsByClassName("otp-mobile")[0].style.display = "none";
        window.checkOtpRadio = function (id) {
            console.log("The id is:", id);
            if(id == "otp-phone"){
                document.getElementsByClassName("otp-mobile")[0].style.display = "block";
                document.getElementsByClassName("otp-email")[0].style.display = "none";
                document.getElementsByClassName("otp-email")[1].style.display = "none";
            }
            else{
                document.getElementsByClassName("otp-mobile")[0].style.display = "none";
                document.getElementsByClassName("otp-email")[0].style.display = "block";
                document.getElementsByClassName("otp-email")[1].style.display = "block";
            }
        };
        window.validNumber = function(event){
            var mobno = event.target.value + event.key;
            if(!mobregExp.test(mobno)){
                event.preventDefault();
            }
        }

        // Dropdown arrow rotation using CSS classes
        const countryCodeSelect = document.getElementById('country_code_select');
        if (countryCodeSelect) {
            // Add 'open' class on focus (when dropdown opens)
            countryCodeSelect.addEventListener('focus', function() {
                this.classList.add('open');
            });
            
            // Remove 'open' class on blur (when clicking outside)
            countryCodeSelect.addEventListener('blur', function() {
                this.classList.remove('open');
            });
            
            // Remove 'open' class on change (when selecting an option)
            countryCodeSelect.addEventListener('change', function() {
                this.classList.remove('open');
            });
        }
        const otpCountryCodeSelect = document.getElementById('otp_country_code_select');
        if (otpCountryCodeSelect) {
            otpCountryCodeSelect.addEventListener('focus', function() {
                this.classList.add('open');
            });
            otpCountryCodeSelect.addEventListener('blur', function() {
                this.classList.remove('open');
            });
            otpCountryCodeSelect.addEventListener('change', function() {
                this.classList.remove('open');
            });
        }

        // Helper to toggle name and required attribute for visible input only
        function updateInputNames() {
            const emailInput = document.getElementById('email_or_mobile');
            const mobileInput = document.getElementById('mobile_input');
            const emailBlock = document.getElementsByClassName('email-id')[0];
            const mobileBlock = document.getElementsByClassName('mobile')[0];
            const typeInput = document.getElementById('login_type');
            if (emailBlock && emailBlock.style.display !== 'none') {
                emailInput.setAttribute('name', 'email_or_mobile');
                emailInput.required = true;
                mobileInput.removeAttribute('name');
                mobileInput.required = false;
                if(typeInput) typeInput.value = 'email';
            } else {
                emailInput.removeAttribute('name');
                emailInput.required = false;
                mobileInput.setAttribute('name', 'email_or_mobile');
                mobileInput.required = true;
                if(typeInput) typeInput.value = 'mobile';
            }
        }
        // Initial call
        updateInputNames();
        // Patch checkRadio to update names and required
        const origCheckRadio = window.checkRadio;
        window.checkRadio = function(id) {
            origCheckRadio(id);
            updateInputNames();
        };
        // Also update on page load in case of autofill
        setTimeout(updateInputNames, 100);
    });

</script>