<?php $this->assign('title', h(!empty($course->meta_title) ? $course->meta_title : $course->name)); ?>
<?php $this->assign('meta_desc', h(!empty($course->meta_description) ? $course->meta_description : $course->short_description)); ?>
<?php $this->assign('keywords', h($course->meta_keywords)); ?>
<?= $this->Form->create(null, ['url' => ['controller' => 'Bookings', 'action' => 'add'], 'id' => 'enrollForm']) ?>
<input type="hidden" name="course_id" value="<?= $course->id ?>">
<div x-data="enrollmentPage()" x-init="init()">
    <section class="group-booking">
        <div class="container mx-auto px-4 pb-4">
            <div class="flex items-center space-x-4 p-6 bg-[] grp-booking-body">
                <!-- Image -->
                <img src="<?= $course->image_url ?>" alt="<?= $course->name ?>"
                    class="w-24 h-24 rounded-[18px] object-cover mr-2" />

                <!-- Text Content -->
                <div class="content">
                    <!-- Organizer -->
                    <p class="text-sm text-[#D87A61] font-medium">@<?= $course->partner ? $course->partner->name : '' ?>
                    </p>

                    <!-- Title -->
                    <h2 class="text-xl md:text-2xl font-bold text-gray-800">
                        <?= $course->name ?>
                    </h2>


                </div>
            </div>
            <!-- Info Row -->
            <div class="flex items-center mt-2 space-x-6 text-sm text-gray-700 time-info ">
                <!-- Time -->
                <div class="flex items-center space-x-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path
                            d="M14.6673 7.99967C14.6673 11.6797 11.6807 14.6663 8.00065 14.6663C4.32065 14.6663 1.33398 11.6797 1.33398 7.99967C1.33398 4.31967 4.32065 1.33301 8.00065 1.33301C11.6807 1.33301 14.6673 4.31967 14.6673 7.99967Z"
                            stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                        <path opacity="0.4"
                            d="M10.4739 10.1202L8.40724 8.88684C8.04724 8.6735 7.75391 8.16017 7.75391 7.74017V5.00684"
                            stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <span><span class="italic font-semibold"><?= $course->duration_details ?></span></span>
                </div>

                <!-- Language -->
                <div class="flex items-center space-x-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path
                            d="M8.00065 14.6663C11.6825 14.6663 14.6673 11.6816 14.6673 7.99967C14.6673 4.31778 11.6825 1.33301 8.00065 1.33301C4.31875 1.33301 1.33398 4.31778 1.33398 7.99967C1.33398 11.6816 4.31875 14.6663 8.00065 14.6663Z"
                            stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                        <g opacity="0.4">
                            <path d="M5.33372 2H6.00039C4.70039 5.89333 4.70039 10.1067 6.00039 14H5.33372"
                                stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                            <path d="M10 2C11.3 5.89333 11.3 10.1067 10 14" stroke="#C45F44" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path d="M2 10.6667V10C5.89333 11.3 10.1067 11.3 14 10V10.6667" stroke="#C45F44"
                                stroke-linecap="round" stroke-linejoin="round" />
                            <path d="M2 6.00039C5.89333 4.70039 10.1067 4.70039 14 6.00039" stroke="#C45F44"
                                stroke-linecap="round" stroke-linejoin="round" />
                        </g>
                    </svg>
                    <span class="italic font-semibold"><?= $course->language ?></span>
                </div>
            </div>
        </div>
    </section>
    <section class="select-branch bg-[#FFF0E9] border-t-[#C45F44] border-t-2" id="section-batch">
        <div class="container mx-auto px-4">
            <div class="select-branch-grid-content py-10 px-4">
                <div class="max-w-5xl mx-auto text-center">
                    <!-- Title -->
                    <h3 class="text-2xl font-bold text-gray-800 mb-8">Select Batch<span class="text-red-500">*</span>
                    </h3>

                    <!-- Batches Grid -->
                    <template x-if="errors.batch">
                        <div class="text-red-600 mb-2" x-text="errors.batch"></div>
                    </template>
                    <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-4 content mt-5">
                        <!-- Repeat this block for each batch -->
                        <template x-for="batch in batches" :key="batch.id">
                            <div class="select-date px-6 py-3 border-[#C45F44] border-1 rounded-full text-center "
                                :class="selectedBatch === batch.id ? 'bg-[#C45F44] border-[#C45F44] text-white' : 'bg-white'"
                                @click="toggleBatch(batch.id)">
                                <span class=""
                                    x-text="formatDate(batch.start_date) + ' to ' + formatDate(batch.end_date)"></span>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="bg-[#fffdf7] text-gray-800 md:p-6 Participants" id="section-participants">
        <div class="container mx-auto p-0">
            <!-- Header -->
            <div class="flex items-center justify-between mb-6 head">
                <!-- <h4 class=" participants-title">Participants</h4> -->
                <!-- <div class="flex items-center space-x-4 mb-6 increment-decrement">
                <button class="w-8 h-8 bg-[#D87A61] text-white rounded-full text-xl font-bold leading-none" type="button" @click="if(participantCount > 1){ participantCount--; generateParticipants(); }"
                    class="w-8 h-8 bg-[#D87A61] text-white rounded-full text-xl leading-none">−</button>
                <span class="text-lg font-semibold w-8 text-center m-0" x-text="participantCount"></span>
                
            </div> -->
            </div>
            <!-- popup -->
            <div class="relative" x-data="{ open: false }"
                x-effect="document.body.style.overflow = open ? 'hidden' : ''">
                <div class="bg-[#1f2941] text-white px-4 py-3 flex items-center justify-between continue-sticky">
                    <div>
                        <div class="text-lg font-bold"></div>
                        <div class="text-xs text-gray-300"></div>
                    </div>
                    <button type="button" @click="open = true"
                        class="bg-[#D87A61] hover:bg-[#c66d52] text-white text-sm font-semibold px-4 py-2 rounded">
                        Continue
                    </button>
                </div>
                <!-- Popupbody -->
                <div x-show="open" x-cloak x-transition.opacity
                    class="fixed inset-0 bg-[#0000005c] bg-opacity-50 flex items-start justify-center z-50 overflow-scroll mt-12">
                    <div @click.away="open = false" class="bg-white  pt-12 shadow-lg relative">
                        <button type="button" @click="open = false"
                            class="flex items-center text-[#000000] text-[10px] font-[700]  fixed top-15 w-full px-2 p-3 bg-[#ffffffba] mt-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none">
                                <path d="M9.57 5.92969L3.5 11.9997L9.57 18.0697" stroke="#292D32" stroke-width="1.5"
                                    stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M20.5 12H3.67" stroke="#292D32" stroke-width="1.5" stroke-miterlimit="10"
                                    stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <span class="ml-2 text-[10px] ">Back to Select Slot</span>
                        </button>


                        <div class="grid grid-cols-1 md:grid-cols-[100%] gap-6 mt-3">
                            <div class=" p-0">
                                <div id="accordionContainer">
                                    <template x-for="(participant, index) in participants" :key="participant.uid">
                                        <div class="bg-[#FFFBF0] border border-[#f8e2d2] rounded-md m-2  mb-4"
                                            :id="'participant-' + index">
                                            <button type="button"
                                                class="w-full flex justify-between items-center font-semibold text-[#D87A61]"
                                                onclick="this.nextElementSibling.classList.toggle('hidden')">
                                                <span>Participant <span x-text="index + 1"></span></span>
                                                <!-- <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                                            </svg> -->
                                            </button>

                                            <div class="mt-4 p-0 accordion-body form-bod">
                                                <!-- <template x-for="msg in Object.values(errors.participants[index] || {})" :key="msg">
                                                <p class="text-red-600 text-sm" x-text="msg"></p>
                                            </template> -->
                                                <div class="flex justify-between items-center">
                                                    <!-- <h3 class="font-semibold">Participant <span x-text="index + 1"></span></h3> -->
                                                    <button type="button" @click="removeParticipant(index)"
                                                        class="text-red-600 text-sm hover:underline"
                                                        x-show="index > 0">Remove</button>
                                                </div>
                                                <div class=" rounded-xl w-full">

                                                    <!-- Form: Title, Name -->
                                                    <div class="grid grid-cols-[22%_75%_] gap-2 mb-2 form-content ">
                                                        <div class="">
                                                            <label
                                                                class="text-sm block mb-1 form-content-title">Title<span
                                                                    class="text-red-500">*</span></label></br>
                                                            <select
                                                                class="w-full border rounded-lg p-2 bg-white text-sm"
                                                                x-model="participant.title"
                                                                :name="'participants[' + index + '][title]'">
                                                                <option value="Mr">Mr</option>
                                                                <option value="Ms">Ms</option>
                                                                <option value="Other">Other</option>
                                                            </select>
                                                        </div>
                                                        <div>
                                                            <label class="text-sm block mb-1 form-content-title">First
                                                                name as per passport<span
                                                                    class="text-red-500">*</span></label>
                                                            <input type="text"
                                                                class="w-full border rounded-lg p-2 text-sm bg-white"
                                                                x-model="participant.first_name"
                                                                :name="'participants[' + index + '][first_name]'" />
                                                            <p class="text-red-500 text-sm"
                                                                x-text="errors.participants[index]?.first_name"></p>
                                                        </div>

                                                    </div>

                                                    <!-- Form: Age, Phone, Email -->
                                                    <div class="grid grid-cols-[75%_22%]  gap-2 mb-2 form-content">
                                                        <div>
                                                            <label class="text-sm block mb-1 form-content-title">Last
                                                                Name as per passport<span
                                                                    class="text-red-500">*</span></label>
                                                            <input type="text"
                                                                class="w-full border rounded-lg p-2 text-sm bg-white "
                                                                x-model="participant.last_name"
                                                                :name="'participants[' + index + '][last_name]'" />
                                                            <p class="text-red-500 text-sm"
                                                                x-text="errors.participants[index]?.last_name"></p>
                                                        </div>
                                                        <div>
                                                            <label
                                                                class="text-sm block mb-1 form-content-title">Age</label>
                                                            <input type="number" min="5"
                                                                class="w-full border rounded-lg p-2 text-sm bg-white"
                                                                x-model="participant.age"
                                                                :name="'participants[' + index + '][age]'"
                                                                @input="if (participant.age < 0) participant.age = 0" />
                                                            <p class="text-red-500 text-sm"
                                                                x-text="errors.participants[index]?.age"></p>
                                                        </div>

                                                    </div>
                                                    <div class="form-phone mb-2 ">
                                                        <label class="text-sm block mb-1 form-content-title">Phone<span
                                                                class="text-red-500">*</span></label>
                                                        <div class="flex form-content">
                                                            <select x-model="participant.phone_country_code"
                                                                :key="'phone-country-select-' + countryCodes.length"
                                                                :name="'participants[' + index + '][phone_country_code]'"
                                                                class="border rounded-l-lg p-2 text-sm bg-white code"
                                                                x-effect="
                                                                    if (!participant.phone_country_code && countryCodes.length) {
                                                                        participant.phone_country_code = '+91';
                                                                    }
                                                                ">
                                                                <template x-for="(item, i) in countryCodes"
                                                                    :key="item.code + '-' + i">
                                                                    <option :value="item.dial_code"
                                                                        x-text="item.dial_code"></option>
                                                                </template>
                                                            </select>
                                                            <input type="text"
                                                                class="border border-l-0 rounded-r-lg p-2 text-sm w-full bg-white phone"
                                                                x-model="participant.phone"
                                                                :name="'participants[' + index + '][phone]'" />
                                                        </div>
                                                        <p class="text-red-500 text-sm"
                                                            x-text="errors.participants[index]?.phone"></p>
                                                        <p class="text-red-500 text-sm"
                                                            x-text="errors.participants[index]?.phone_country_code"></p>
                                                    </div>

                                                    <!-- Food & Residency -->
                                                    <div class="grid grid-cols-[75%_22%_] gap-2  form-content">
                                                        <div>
                                                            <label
                                                                class="text-sm block mb-1 form-content-title">E-Mail<span
                                                                    class="text-red-500">*</span></label>
                                                            <input type="email"
                                                                class="w-full border rounded-lg p-2 text-sm bg-white"
                                                                x-model="participant.email"
                                                                :name="'participants[' + index + '][email]'" />
                                                            <p class="text-red-500 text-sm"
                                                                x-text="errors.participants[index]?.email"></p>
                                                        </div>
                                                        <div class="w-full">
                                                            <label class="text-sm form-content-title">Food<span
                                                                    class="text-red-500">*</span></label>
                                                            <select
                                                                class="border rounded-lg p-2 text-sm bg-white w-full"
                                                                x-model="participant.food"
                                                                :name="'participants[' + index + '][food]'"
                                                                x-init="if (!participant.food) participant.food = foodOptions[0]">
                                                                <template x-for="(label, value) in foodOptions"
                                                                    :key="value">
                                                                    <option :value="label" x-text="label"></option>
                                                                </template>
                                                            </select>
                                                            <p class="text-red-500 text-sm"
                                                                x-text="errors.participants[index]?.food"></p>
                                                        </div>
                                                        <!-- <div class="flex gap-4 items-center">
                                                        <template x-for="(label, key) in residencyOptions" :key="key">
                                                            <label class="flex items-center gap-2 text-sm">
                                                                <input type="radio" :value="key" x-model="participant.residency" :name="'participants[' + index + '][residency]'" @change="onResidencyChange(participant)" class="accent-[#C45F44]"/>
                                                                <span class="text-[#D87A61] font-medium" x-text="label"></span>
                                                            </label>
                                                        </template>
                                                            <p class="text-red-500 text-sm" x-text="errors.participants[index]?.residency"></p>
                                                    </div> -->
                                                    </div>
                                                    <div class="grid grid-cols-[75%_22%_] gap-2 mt-2 form-content">
                                                        <div
                                                            class="grid grid-cols-[100%] items-center residence-options">
                                                            <template x-for="(label, key) in residencyOptions"
                                                                :key="key">
                                                                <label
                                                                    class="flex items-center gap-4 text-sm select-residence-label">
                                                                    <input type="radio" :value="key"
                                                                        x-model="participant.residency"
                                                                        :name="'participants[' + index + '][residency]'"
                                                                        @change="onResidencyChange(participant)"
                                                                        class="accent-[#C45F44]" />
                                                                    <span
                                                                        class="text-[#344054] font-[500] select-residence-text text-[16px]"
                                                                        x-text="label"></span>
                                                                </label>
                                                            </template>
                                                            <p class="text-red-500 text-sm"
                                                                x-text="errors.participants[index]?.residency"></p>
                                                        </div>
                                                        <div>
                                                            <!-- Option to select state if residency Indian -->
                                                            <div class=" w-auto"
                                                                x-show="participant.residency === 'india'" x-cloak>
                                                                <!-- <label class="text-sm">State<span class="text-red-500">*</span></label> -->
                                                                <select
                                                                    class="border rounded-lg p-2 text-sm bg-white w-full"
                                                                    x-model="participant.state_id"
                                                                    :name="'participants[' + index + '][state_id]'">
                                                                    <option value="">State</option>
                                                                    <template x-for="(label, value) in states"
                                                                        :key="value">
                                                                        <option :value="value" x-text="label"></option>
                                                                    </template>
                                                                </select>
                                                                <p class="text-red-500 text-sm"
                                                                    x-text="errors.participants[index]?.state_id"></p>
                                                            </div>
                                                            <!-- Set Currency -->
                                                            <div x-effect="
                                                            participant.residency_currency = 
                                                            participant.residency === 'international' 
                                                                ? participant.currency || '' 
                                                                : 'INR'
                                                                ">
                                                            </div>
                                                            <!-- Conditionally show currency dropdown -->
                                                            <div class=" w-auto"
                                                                x-show="participant.residency === 'international'"
                                                                x-cloak>
                                                                <!-- <label class="block text-sm font-medium text-gray-700 mb-1">Select Currency</label> -->
                                                                <select
                                                                    class="border rounded-lg p-2 text-sm bg-white w-full"
                                                                    x-model="participant.currency"
                                                                    @change="onCurrencyChange">
                                                                    <template x-for="(label, code) in foreignCurrencies"
                                                                        :key="code">
                                                                        <option :value="code" x-text="label"></option>
                                                                    </template>
                                                                </select>
                                                                <p class="text-red-500 text-sm"
                                                                    x-text="errors.participants[index]?.currency"></p>
                                                            </div>
                                                            <input type="hidden"
                                                                :name="'participants[' + index + '][residency_currency]'"
                                                                :value="participant.residency_currency" />
                                                        </div>
                                                    </div>
                                                    <!-- <div x-effect="
                                                    const prices = getBasePricesForParticipant(participant);
                                                    if (prices.length > 0) {
                                                        participant.base_price_id = prices[0].id;
                                                    }
                                                "></div> -->

                                                    <!-- Base Cost -->
                                                    <div class="mb-6">
                                                        <p class="text-[#6A9E74] font-bold text-lg mb-2">Base Cost<span
                                                                class="text-red-500">*</span></p>
                                                        <div
                                                            class="bg-[#E0F9E4] border-1 border-[#6A9E74] rounded-lg p-3 space-y-4">
                                                            <template
                                                                x-for="price in getBasePricesForParticipant(participant)"
                                                                :key="price.id">
                                                                <template
                                                                    x-if="price.price !== null && price.price !== undefined">
                                                                    <label
                                                                        class="base-cost-label flex items-center justify-between">
                                                                        <span class="flex gap-3">
                                                                            <input type="radio" class="accent-[#C45F44]"
                                                                                :value="price.id"
                                                                                x-model="participant.base_price_id"
                                                                                :name="'participants[' + index + '][base_price_id]'"
                                                                                :disabled="getSelectedBatchLeftCount() === 0" />
                                                                            <div class="base-price">
                                                                                <p class="font-[600] text-[12px]  text-[#344054] mb-1"
                                                                                    x-text="price.name"></p>
                                                                                <p class="font-[700] text-[18px] text-[#000000] mb-0"
                                                                                    x-text="price.price + ' '+ price.currency">
                                                                                </p>
                                                                            </div>
                                                                        </span>
                                                                        <span class="text-green-600 text-sm italic"
                                                                            x-text="
                                                                    getSelectedBatchLeftCount() === null
                                                                        ? ''
                                                                        : (getSelectedBatchLeftCount() === 0 ? 'Fully Booked' : getSelectedBatchLeftCount() + ' Left')
                                                                ">
                                                                        </span>
                                                                    </label>
                                                                </template>
                                                            </template>
                                                            <p class="text-red-500 text-sm"
                                                                x-text="errors.participants[index]?.base_price_id"></p>
                                                        </div>
                                                    </div>

                                                    <!-- Add Ons -->
                                                    <div>
                                                        <p class="text-[#4D9257] font-bold text-lg mb-4">Add Ons
                                                            (Optional)</p>
                                                        <ul class="space-y-3">
                                                            <template x-for="addon in addons" :key="addon.id">
                                                                <template
                                                                    x-if="getAddonPrice(addon.id, participant.residency_currency) !== '' && getAddonPrice(addon.id, participant.residency_currency) !== null">
                                                                    <li class="flex justify-between items-center">
                                                                        <label
                                                                            class="flex gap-2 items-center text-[#D87A61] font-medium">
                                                                            <input type="checkbox"
                                                                                class="accent-[#C45F44]"
                                                                                :value="addon.id"
                                                                                @change="toggleAddon(addon, participant)"
                                                                                :checked="isAddonSelected(addon.id, participant)"
                                                                                x-model="participant.selected_addons"
                                                                                :disabled="getAddonLeftCount(addon, participant.residency_currency) === 0" />
                                                                            <span x-text="addon.name"></span>
                                                                            (<small
                                                                                x-text="getAddonLeftCount(addon, participant.residency_currency) == 0 ? 'Fully Booked' : getAddonLeftCount(addon, participant.residency_currency)"></small>)
                                                                        </label>
                                                                        <span class="text-sm font-medium"
                                                                            x-text="getAddonPrice(addon.id, participant.residency_currency) + ' ' + getAddonCurrency(addon, participant.residency_currency)"></span>
                                                                    </li>
                                                                </template>
                                                            </template>
                                                        </ul>
                                                    </div>

                                                </div>
                                            </div>

                                        </div>

                                    </template>
                                    <div class="w-full m-auto mb-3 text-center">
                                        <button type="button" @click="open = true"
                                            class="px-4 py-3 w-[80%]  save-participant bg-[#D87A61] hover:bg-[#c66d52] text-white text-sm font-semibold px-4 py-2">
                                            Save Participant
                                        </button>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <!-- popup ends -->
            <template x-if="hasParticipantDetails()">
                <div class=" md:p-4">
                    <div class="w-full rounded-lg overflow-hidden border border-[#f3c9b7] ">
                        <!-- Dropdown -->
                        <div class=" mx-auto rounded overflow-hidden text-sm payment-summary">
                            <!-- Accordion Header -->
                            <!-- <button type="button"
                                onclick="document.getElementById('payment-summary-content').classList.toggle('hidden'); this.querySelector('svg').classList.toggle('rotate-180')"
                                class="w-full bg-[#fff] px-4 py-2 flex items-center justify-between text-gray-700 font-semibold border-b">
                                <span>Payment Summary</span>
                                <svg class="w-4 h-4 transition-transform duration-300" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7" />
                                </svg>
                            </button> -->

                            <!-- Accordion Content -->
                            <div id="payment-summary-content" class="bg-[#fff] px-0">
                                <!-- Title -->
                                <div class="bg-[#fff] px-0">

                                    <div class=" mx-auto p-0 px-4 text-sm font-medium text-gray-800 ">
                                        <!-- Billed To -->
                                        <div class="font-semibold">
                                            <h5><?= h($course->name) ?></br>
                                                <span class="underline"
                                                    x-text="getBatchNameSummary(selectedBatch)"></span>
                                            </h5>
                                        </div>
                                        <div class="billed">
                                            <label for="billedTo"
                                                class="block my-3 font-semibold text-[#231F20] title">Billed To</label>
                                            <div class="relative1 body1">
                                                <select id="billedTo" x-model="billedToIndex"
                                                    class="w-full border-1 border-[#6C6C6C] rounded-[20px] px-2 py-3 bg-white text-[15px] text-[#983419] ">
                                                    <template x-for="(participant, index) in participants" :key="index">
                                                        <option :value="index"
                                                            class="text-[#983419] text-[15px] font-[600]"
                                                            x-text="`${participant.first_name} ${participant.last_name}`">
                                                        </option>
                                                    </template>
                                                </select>
                                                <!-- Down Arrow -->
                                                <div
                                                    class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-[#00000033]">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M19 9l-7 7-7-7" />
                                                    </svg>
                                                </div>

                                            </div>
                                            <label for="billedTo"
                                                class="block my-3 font-semibold text-[#231F20] title">Address</label>
                                            <div class="relative1 body1">
                                                <input id="billedTo" x-model="billedToIndex"
                                                    class="w-full border-1 border-[#6C6C6C] rounded-[20px] px-2 py-3 bg-white text-[15px] text-[#00000033] " />
                                            </div>
                                            <label for="billedTo"
                                                class="block my-3 font-semibold text-[#231F20] title">Country</label>
                                            <div class="relative1 body1">
                                                <select id="billedTo" x-model="billedToIndex"
                                                    class="w-full border-1 border-[#6C6C6C] rounded-[20px] px-2 py-3 bg-white text-[15px] text-[#983419] ">
                                                    <template x-for="(participant, index) in participants" :key="index">
                                                        <option :value="index"
                                                            class="text-[#983419] text-[15px] font-[600]"
                                                            x-text="`${participant.first_name} ${participant.last_name}`">
                                                        </option>
                                                    </template>
                                                </select>
                                            </div>
                                            <label for="billedTo"
                                                class="block my-3 font-semibold text-[#231F20] title">State</label>
                                            <div class="relative1 body1">
                                                <select id="billedTo" x-model="billedToIndex"
                                                    class="w-full border-1 border-[#6C6C6C] rounded-[20px] px-2 py-3 bg-white text-[15px] text-[#983419] ">
                                                    <template x-for="(participant, index) in participants" :key="index">
                                                        <option :value="index"
                                                            class="text-[#983419] text-[15px] font-[600]"
                                                            x-text="`${participant.first_name} ${participant.last_name}`">
                                                        </option>
                                                    </template>
                                                </select>
                                            </div>
                                            <label for="billedTo"
                                                class="block my-3 font-semibold text-[#231F20] title">City</label>
                                            <div class="relative1 body1">
                                                <select id="billedTo" x-model="billedToIndex"
                                                    class="w-full border-1 border-[#6C6C6C] rounded-[20px] px-2 py-3 bg-white text-[15px] text-[#983419] ">
                                                    <template x-for="(participant, index) in participants" :key="index">
                                                        <option :value="index"
                                                            class="text-[#983419] text-[15px] font-[600]"
                                                            x-text="`${participant.first_name} ${participant.last_name}`">
                                                        </option>
                                                    </template>
                                                </select>
                                            </div>
                                            <label for="billedTo"
                                                class="block my-3 font-semibold text-[#231F20] title">Postal\Zip
                                                code</label>
                                            <div class="relative1 body1">
                                                <input id="billedTo" x-model="billedToIndex"
                                                    class="w-full border-1 border-[#6C6C6C] rounded-[20px] px-2 py-3 bg-white text-[15px] text-[#00000033] " />
                                            </div>
                                            <!-- Email Display -->
                                            <template
                                                x-if="participants[billedToIndex] && participants[billedToIndex].email">
                                                <small class="text-gray-600 block my-3">
                                                    Booking details will be sent to <span
                                                        x-text="participants[billedToIndex].email"></span>
                                                </small>
                                            </template>
                                        </div>
                                        <div class="w-full m-auto mb-4 mt-1 text-center">
                                            <button type="button" @click="open = true"
                                                class="px-4 py-3 w-[80%]  save-participant bg-[#D87A61] hover:bg-[#c66d52] text-white text-sm font-semibold px-4 py-2 rounded-lg">
                                                Save address
                                            </button>
                                        </div>
                                        <!-- Agreement Checkbox -->
                                        <div class="flex items-start space-x-2 privacy-policy">
                                            <div class="">
                                                <input type="checkbox" x-model="agreedToTerms" name="agree_terms"
                                                    class="accent-[#C45F44] mt-0" required>
                                                <!-- <div class="w-4 h-4 rounded-full border-2 border-red-600 flex items-center justify-center">
                                                <div class="w-2 h-2 bg-red-600 rounded-full"></div>
                                            </div> -->
                                            </div>
                                            <p class="text-xs terms-condition flex items-center flex-wrap">
                                                <span class="text-[#000000]">By proceeding, I agree to Yoga.in's</span>
                                                <a href="#" class=" text-[#0F43CA] underline">User Agreement</a>,
                                                <a href="#" class=" text-[#0F43CA] underline">Terms of Service</a>,
                                                <a href="#" class=" text-[#0F43CA] underline">Cancellation & Booking
                                                    Policies</a>.
                                            </p>
                                        </div>
                                        <!-- Billing Currency Selector -->
                                        <div
                                            class="w-full max-w-md billingCurrency-element p-2 mt-2 border-1 border-[#D87A61] rounded-lg">
                                            <div class="flex items-start billingCurrency-body">
                                                <!-- Label -->
                                                <label for="billingCurrency"
                                                    class="block mb-2 font-semibold text-gray-800">
                                                    Select Billing Currency:
                                                </label>

                                                <!-- Select Dropdown -->
                                                <div class="relative w-[60px]">
                                                    <select id="billingCurrency" x-model="selectedBillingCurrency"
                                                        @change="updateRate"
                                                        class="w-full appearance-none text-gray-800 font-bold rounded pl-2 bg-white focus:outline-none focus:ring-2 focus:ring-red-200">
                                                        <option value="USD" selected>USD</option>
                                                        <option value="INR">INR</option>
                                                        <option value="EUR">EUR</option>
                                                    </select>
                                                    <!-- Down arrow -->
                                                    <div
                                                        class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                            viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2" d="M19 9l-7 7-7-7" />
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- Exchange Rates -->
                                            <div class=" exc-rate">
                                                <p class="text-[#983419] font-semibold mb-2 title">Exchange Rates:</p>
                                                <ul class=" text-sm text-gray-700 list-rate pl-0 mb-2"
                                                    style="padding-left:0px">
                                                    <li
                                                        x-text="'1 INR = ' + exchangeRates[selectedBillingCurrency] + ' ' + selectedBillingCurrency">
                                                    </li>
                                                </ul>
                                                <p class="text-xs text-gray-400 italic mb-2 Disclaimer">
                                                    <span class="text-underline">Disclaimer:</span> </br>
                                                    Exchange rates are approximate and based on current rates at the
                                                    time of booking.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- <hr class="border-[#C45F44]"> -->
                                    <template x-if="participants.length > 0">
                                        <template x-for="(participant, index) in participants" :key="index">
                                            <template x-if="
                                                    participant.first_name &&
                                                    participant.last_name &&
                                                    participant.email &&
                                                    participant.phone &&
                                                    participant.residency &&
                                                    participant.base_price_id
                                                ">
                                                <div
                                                    class="participant-cost-wrapper space-y-3 p-4 border-[#C45F44] border-t-1 bg-[#FFF0ED] mt-4">
                                                    <!--Participant Name -->
                                                    <div>
                                                        <p class="text-[#983419] font-semibold mb-1"
                                                            x-text="participant.title +' '+ participant.first_name + ' ' + participant.last_name">
                                                        </p>
                                                        <p class="text-xs text-[#727272] font-bold italic about-user"
                                                            x-text="
                                                                capitalize(participant.residency) + ' | ' +
                                                                (participant.age ? participant.age + 'yrs | ' : '') +
                                                                participant.phone_country_code + participant.phone + ' | ' +
                                                                participant.food
                                                            ">
                                                        </p>
                                                    </div>

                                                    <!-- Cost Breakdown -->
                                                    <div class="cost-breakdown">
                                                        <div class="flex justify-between mb-3">
                                                            <span>Base : <span
                                                                    x-text="getBasePriceName(participant.base_price_id)"></span></span>
                                                            <span class="text-red-600">
                                                                <template
                                                                    x-if="getBasePriceById(participant.base_price_id) && getBasePriceById(participant.base_price_id).currency !== selectedBillingCurrency">
                                                                    <span>
                                                                        <span
                                                                            x-text="getBasePriceById(participant.base_price_id).price + ' ' + getBasePriceById(participant.base_price_id).currency"></span>
                                                                        (<span
                                                                            x-text="getBasePriceAmountInBillingCurrency(participant.base_price_id)"></span>)
                                                                    </span>
                                                                </template>
                                                                <template
                                                                    x-if="getBasePriceById(participant.base_price_id) && getBasePriceById(participant.base_price_id).currency === selectedBillingCurrency">
                                                                    <span
                                                                        x-text="getBasePriceAmountInBillingCurrency(participant.base_price_id)"></span>
                                                                </template>
                                                            </span>
                                                        </div>
                                                        <template x-for="addonObj in participant.selected_addons"
                                                            :key="`${addonObj.addon_id}-${index}`">
                                                            <div class="flex justify-between mb-3">
                                                                <span
                                                                    x-text="getAddonName(addonObj.addon_id, selectedBillingCurrency)"></span>
                                                                <!-- Addon Cost Summary -->
                                                                <span class="text-red-600">
                                                                    <template
                                                                        x-if="addonObj?.addon_id && participant?.residency_currency &&
                                                                            getAddonCurrency(getAddonById(addonObj.addon_id), participant.residency_currency) !== selectedBillingCurrency">
                                                                        <span>
                                                                            <span
                                                                                x-text="getAddonPrice(addonObj.addon_id, participant.residency_currency) + ' ' + participant.residency_currency"></span>
                                                                            (<span
                                                                                x-text="getAddonPriceInBillingCurrency(addonObj.addon_id, participant.residency_currency)"></span>)
                                                                        </span>
                                                                    </template>

                                                                    <template
                                                                        x-if="addonObj?.addon_id && participant?.residency_currency &&
                                                                            getAddonCurrency(getAddonById(addonObj.addon_id), participant.residency_currency) === selectedBillingCurrency">
                                                                        <span
                                                                            x-text="getAddonPriceInBillingCurrency(addonObj.addon_id, participant.residency_currency)"></span>
                                                                    </template>

                                                                </span>
                                                            </div>
                                                        </template>

                                                        <div class="flex justify-between mb-3 discount-applied">
                                                            <span>Discount Applied:</span>
                                                            <span><span class="text-red-600"></span>
                                                            </span>
                                                        </div>

                                                        <!-- <div class="text-sm new">
                                                            <span class="font-semibold mb-3">NEW10</span>
                                                            <a href="#" class="text-[#BF0A30] ml-2 underline">Remove</a>
                                                        </div> -->

                                                    </div>

                                                    <!-- Discount Code Input -->
                                                    <div class="flex items-center space-x-2 discount">
                                                        <input type="text" placeholder="Discount Code"
                                                            class="w-full px-3 py-1.5 border border-gray-300 bg-[#FFFFFF] rounded-lg focus:outline-none focus:ring-2 focus:ring-green-300 text-[#C8C8C8]"
                                                            x-model="participant.discount_code"
                                                            :name="'participants.discount_code'" />
                                                        <button
                                                            class="bg-[#C8EBCD] text-[#479456] px-4 py-1.5 rounded-lg hover:bg-green-400"
                                                            type="button">Apply</button>
                                                    </div>
                                                    <div class=" p-2 border-t-2 border-[#bb5c3c] space-y-4 text-sm">
                                                        <!-- Total Discounts -->
                                                        <div class="flex justify-between items-center">
                                                            <span class="italic">Total Discounts</span>
                                                            <span class="text-red-600 font-semibold italic"></span>
                                                        </div>

                                                        <!-- Total Before Tax -->
                                                        <div
                                                            class="flex justify-between items-center total-before-tax font-bold">
                                                            <span>Total Before Tax</span>
                                                            <span class="italic font-bold text-lg value"
                                                                x-text="calculateSubtotalInBillingCurrency(participant) + ' ' + selectedBillingCurrency"></span>
                                                        </div>

                                                        <!-- Applicable Tax -->
                                                        <div
                                                            class="flex justify-between items-center text-sm Applicable-tax">
                                                            <span class="italic">Applicable Tax (<span
                                                                    x-text="getTaxRateForParticipant(selectedBillingCurrency)+'%'"></span>)</span>
                                                            <span class="italic font-semibold text-[#505050] value"
                                                                x-text="calculateTaxForParticipantInBillingCurrency(participant) + ' ' + selectedBillingCurrency"></span>
                                                        </div>

                                                        <div
                                                            class="flex justify-between pt-2 border-t border-gray-300 mt-2 total">
                                                            <span>Sub-Total</span>
                                                            <span><span class="text-red-600"
                                                                    x-text="calculateTotalWithTaxInBillingCurrency(participant) + ' ' + selectedBillingCurrency"></span></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                        </template>
                                    </template>
                                </div>
                            </div>
                        </div>
                        <!-- Bottom Section"-->
                        <div class="bg-[#1f2941] text-white px-4 py-3 flex items-center justify-between"
                            x-show="hasParticipantDetails()">
                            <div>
                                <div class="text-lg font-bold"
                                    x-text="calculateGrandTotal() + ' ' + selectedBillingCurrency"></div>
                                <div class="text-xs text-gray-300" x-text="'For '+ participantCount + ' Participants'">
                                </div>
                            </div>
                            <button type="button"
                                class="bg-[#00AE4D] hover:bg-[#c66d52] text-white text-sm font-semibold px-4 py-2 rounded"
                                @click="submitBooking" :disabled="isProcessing"
                                x-text="isProcessing ? 'Processing...' : 'Pay Now'">
                                Pay Now
                            </button>
                        </div>
                    </div>
            </template>
            <div class="participant-cost-wrapper p-2  bg-[#FFFBF0] m-3">
                <div class="  grid grid-cols-[70%_25%_] ">
                    <!--Participant Name -->
                    <div>
                        <p class="text-[#983419] text-[15px] font-bold mb-1 rounded-lg">Ms. Gisha Jatin Shah
                        </p>

                    </div>
                    <div class="flex items-center justify-between">
                        <div class=""><button class="text-[#293148] text-[12px] font-[700] underline">Edit</button>
                        </div>
                        <div class=""><button class="text-[#293148] text-[12px] font-[700] underline">Delete</button>
                        </div>
                    </div>
                </div>
                <p class="text-xs text-[#727272] font-bold italic about-user mb-2">India Resident | 24 yrs | +91 79043
                    30323 | Veg
                </p>

            </div>
            <div class="text-left p-4 pt-1">
                <button type="button" @click="participantCount++; generateParticipants()"
                    class="text-[#1877F2] leading-none">+Add Participants</button>
            </div>
        </div>
    </section>
    <section class="booking-successfull">
        <div class="container mx-auto text-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="197" height="132" viewBox="0 0 197 132" fill="none" class="m-auto pb-4">
                <g clip-path="url(#clip0_2123_5579)">
                    <path opacity="0.3"
                        d="M166.054 96.5254C162.89 97.3573 160.853 99.0197 157.419 100.995L155.923 101.586C155.81 101.505 155.894 101.564 155.493 101.287V101.281C153.89 100.007 153.482 98.6418 152.454 96.7407C150.367 92.8579 151.67 87.8357 153.276 83.7332C154.096 82.9672 154.944 81.0383 154.598 79.2719C154.722 78.7637 154.815 78.3141 154.862 78.0753C155.833 75.6294 154.594 73.3109 155.906 70.6115C159.991 62.1371 158.54 56.4177 159.302 52.9626C159.698 51.0601 158.809 50.4186 156.686 48.3637C147.771 39.6476 147.111 39.5363 146.455 37.001C143.949 27.3651 145.174 26.8364 144.049 26.5947C143.479 27.1088 143.726 28.1238 143.556 28.818C143.455 28.0535 143.508 26.6006 143.345 26.5479C142.042 26.8173 143.002 26.4629 140.729 37.0054C140.212 39.4103 139.245 39.7838 137.507 41.534C127.806 51.2036 127.833 50.7173 128.027 52.4998C128.258 54.7012 128.535 59.8333 128.89 62.1005C130.09 69.8397 132.361 69.9642 132.227 74.0886C132.201 75.1065 132.163 77.2507 132.615 78.1735C132.715 78.6114 132.837 79.0845 132.955 79.5092C132.833 80.8538 133.304 82.7944 134.187 83.6937L134.741 85.504C134.878 87.2762 138.891 93.1816 131.959 101.757C126.086 98.4426 126.387 98.3694 124.779 97.5375C120.087 95.1164 112.962 94.2933 110.265 98.8161C108.345 102.03 110.562 103.991 113.533 105.942C117.576 108.588 120.811 109.209 124.63 109.265L125.973 109.496L124.785 109.934C124.551 109.928 124.286 110.075 124.069 110.208C121.665 111.746 122.769 114.256 124.48 113.313C124.691 113.196 124.891 113.057 125.143 112.899C126.175 113.569 126.375 113.605 127.601 113.325C134.292 111.846 133.538 113.984 138.033 113.127C140.194 112.735 138.877 112.906 141.887 112.392H141.893L142.348 112.316C145.109 112.792 143.382 112.471 146.733 113.208C154.887 115.015 151.034 111.91 159.258 113.36C160.249 113.536 161.283 114.025 162.15 112.992C164.053 114.176 165.125 112.988 164.597 111.557C164.224 110.545 162.955 110.344 162.901 109.599C168.802 109.121 172.915 107.251 176.676 103.901C179.419 101.439 177.648 93.4907 166.051 96.5239L166.054 96.5254ZM133.672 51.416C146.73 36.2584 142.702 35.8014 143.551 30.7323C143.678 31.803 143.651 36.3404 143.651 36.399C143.631 39.5612 150.451 46.8141 153.812 51.6547C154.884 53.1779 154.582 54.3789 153.93 56.1072C152.917 58.7567 153.055 61.2715 152.552 63.1404C152.183 64.5025 151.162 65.4209 149.994 67.6866C149.726 67.8946 149.136 68.2989 148.933 68.4219C148.927 68.4043 148.927 68.3867 148.921 68.3692C145.397 67.2033 147.789 63.385 148.768 60.1921C148.909 60.5143 149.27 60.5143 149.507 60.2975C149.682 60.1438 149.679 60.113 150.598 58.1387C151.35 56.4924 150.141 56.0501 149.601 56.1599C150.001 52.7634 149.847 49.4138 146.609 48.1059C146.544 48.0649 146.48 48.0297 146.416 48.0004C146.385 43.1408 140.319 43.5509 140.726 48.1864C136.394 50.13 137.836 54.0098 138.015 56.176C137.481 56.012 136.717 56.4324 136.667 57.1281C136.618 57.7022 138.192 61.6421 138.796 60.1394C139.707 63.1624 141.995 67.6984 138.367 68.4087C138.361 68.4322 138.361 68.4497 138.355 68.4732C138.076 68.2535 137.822 68.0177 137.475 67.5797C136.87 65.8837 134.87 64.668 134.507 61.5161C133.791 54.8403 131.666 53.818 133.673 51.4145L133.672 51.416Z"
                        fill="#D7BCE3" />
                    <path opacity="0.3"
                        d="M74.3986 96.5254C71.2345 97.3573 69.1968 99.0197 65.7633 100.995L64.2674 101.586C64.1541 101.505 64.238 101.564 63.8375 101.287V101.281C62.2341 100.007 61.8263 98.6418 60.7986 96.7407C58.7108 92.8579 60.0139 87.8357 61.6202 83.7332C62.4403 82.9672 63.2883 81.0383 62.9423 79.2719C63.066 78.7637 63.1588 78.3141 63.2059 78.0753C64.1762 75.6294 62.9379 73.3109 64.2498 70.6115C68.3355 62.1371 66.8838 56.4177 67.6465 52.9626C68.0425 51.0601 67.1532 50.4186 65.0301 48.3637C56.1151 39.6476 55.4555 39.5363 54.7989 37.001C52.2929 27.3651 53.5179 26.8364 52.3931 26.5947C51.8247 27.1088 52.0706 28.1238 51.8998 28.818C51.7997 28.0535 51.8527 26.6006 51.6893 26.5479C50.3863 26.8173 51.3462 26.4629 49.0729 37.0054C48.5562 39.4103 47.5888 39.7838 45.8515 41.534C36.1503 51.2036 36.1768 50.7173 36.3711 52.4998C36.6023 54.7012 36.8791 59.8333 37.2339 62.1005C38.4338 69.8397 40.7057 69.9642 40.5717 74.0886C40.5452 75.1065 40.5069 77.2507 40.9589 78.1735C41.059 78.6114 41.1812 79.0845 41.299 79.5092C41.1768 80.8538 41.648 82.7944 42.5314 83.6937L43.085 85.504C43.2219 87.2762 47.2355 93.1816 40.3037 101.757C34.4306 98.4426 34.7309 98.3694 33.1231 97.5375C28.4308 95.1164 21.3062 94.2933 18.6088 98.8161C16.6889 102.03 18.9063 103.991 21.8774 105.942C25.919 108.588 29.1552 109.209 32.9744 109.265L34.3172 109.496L33.129 109.934C32.8949 109.928 32.6299 110.075 32.4135 110.208C30.0091 111.746 31.1134 114.256 32.8242 113.313C33.0363 113.196 33.235 113.057 33.4868 112.899C34.5189 113.569 34.7191 113.605 35.9456 113.325C42.6359 111.846 41.8821 113.984 46.3771 113.127C48.5385 112.735 47.2222 112.906 50.2317 112.392H50.2376L50.6925 112.316C53.4531 112.792 51.7261 112.471 55.0771 113.208C63.2309 115.015 59.3778 111.91 67.6023 113.36C68.5932 113.536 69.6268 114.025 70.494 112.992C72.3977 114.176 73.4695 112.988 72.941 111.557C72.5685 110.545 71.2993 110.344 71.2448 109.599C77.146 109.121 81.2597 107.251 85.02 103.901C87.763 101.439 85.9918 93.4907 74.3956 96.5239L74.3986 96.5254ZM42.016 51.416C55.0742 36.2584 51.0459 35.8014 51.8954 30.7323C52.022 31.803 51.9955 36.3404 51.9955 36.399C51.9749 39.5612 58.7948 46.8141 62.1561 51.6547C63.228 53.1779 62.9261 54.3789 62.2739 56.1072C61.2609 58.7567 61.3993 61.2715 60.8958 63.1404C60.5277 64.5025 59.5059 65.4209 58.3384 67.6866C58.0704 67.8946 57.48 68.2989 57.2768 68.4219C57.2709 68.4043 57.2709 68.3867 57.265 68.3692C53.7417 67.2033 56.1328 63.385 57.1119 60.1921C57.2532 60.5143 57.614 60.5143 57.851 60.2975C58.0262 60.1438 58.0233 60.113 58.942 58.1387C59.6944 56.4924 58.4856 56.0501 57.9452 56.1599C58.3457 52.7634 58.1911 49.4138 54.9534 48.1059C54.8887 48.0649 54.8239 48.0297 54.7591 48.0004C54.7282 43.1408 48.6622 43.5509 49.0685 48.1864C44.7369 50.13 46.1783 54.0098 46.358 56.176C45.8235 56.012 45.0594 56.4324 45.0093 57.1281C44.9607 57.7022 46.5346 61.6421 47.1383 60.1394C48.0497 63.1624 50.3377 67.6984 46.7098 68.4087C46.704 68.4322 46.704 68.4497 46.6981 68.4732C46.4183 68.2535 46.1651 68.0177 45.8176 67.5797C45.2125 65.8837 43.213 64.668 42.8494 61.5161C42.1338 54.8403 40.0092 53.818 42.016 51.4145V51.416Z"
                        fill="#A3C4A8" />
                    <path
                        d="M130.328 107.525C125.843 108.704 122.955 111.061 118.089 113.861L115.97 114.698C115.811 114.585 115.929 114.667 115.362 114.274V114.265C113.089 112.46 112.51 110.523 111.054 107.83C108.096 102.327 109.942 95.2076 112.218 89.393C113.382 88.3062 114.583 85.5732 114.093 83.0701C114.268 82.351 114.4 81.7139 114.467 81.3741C115.843 77.9058 114.087 74.6206 115.946 70.795C121.737 58.7835 119.68 50.6767 120.761 45.7804C121.322 43.084 120.062 42.1745 117.052 39.2628C104.415 26.91 103.482 26.7518 102.55 23.1576C98.9968 9.5013 100.733 8.75141 99.1396 8.40868C98.3328 9.13661 98.6817 10.5763 98.4418 11.5606C98.3004 10.4768 98.3755 8.41747 98.1429 8.34277C96.2951 8.72504 97.6555 8.22267 94.4341 23.1664C93.7008 26.5746 92.3301 27.1048 89.8684 29.5859C76.1182 43.2905 76.1565 42.6036 76.4303 45.1287C76.7572 48.2483 77.1503 55.5232 77.6524 58.7366C79.3544 69.7053 82.5744 69.8825 82.3845 75.7264C82.3477 77.1691 82.2932 80.2082 82.9337 81.5162C83.075 82.1372 83.2502 82.8065 83.4166 83.4099C83.2443 85.3154 83.9098 88.066 85.1642 89.3417L85.949 91.9078C86.1433 94.4182 91.8324 102.792 82.0075 114.945C73.683 110.247 74.1085 110.143 71.8293 108.965C65.1787 105.533 55.08 104.366 51.2578 110.777C48.5369 115.332 51.6804 118.112 55.8898 120.877C61.6186 124.629 66.2064 125.508 71.6187 125.587L73.5225 125.915L71.8381 126.535C71.5054 126.526 71.1314 126.734 70.8237 126.923C67.4152 129.102 68.9803 132.661 71.4053 131.324C71.7041 131.159 71.9868 130.959 72.3446 130.737C73.8081 131.689 74.0908 131.737 75.8282 131.34C85.31 129.243 84.2426 132.273 90.6133 131.059C93.6773 130.502 91.8104 130.744 96.0772 130.016H96.086L96.7309 129.909C100.644 130.583 98.1959 130.129 102.946 131.175C114.502 133.736 109.041 129.335 120.699 131.39C122.104 131.638 123.567 132.333 124.798 130.869C127.497 132.547 129.015 130.863 128.265 128.834C127.737 127.4 125.939 127.115 125.863 126.057C134.225 125.381 140.057 122.728 145.387 117.981C149.276 114.491 146.765 103.225 130.328 107.525ZM84.431 43.5893C102.94 22.1045 97.23 21.4586 98.4344 14.2731C98.6125 15.7905 98.5757 22.2217 98.5757 22.3052C98.5463 26.7884 108.214 37.0658 112.978 43.9277C114.498 46.088 114.071 47.7899 113.145 50.2388C111.709 53.9927 111.906 57.5591 111.191 60.2071C110.67 62.139 109.221 63.4396 107.566 66.6515C107.186 66.9474 106.35 67.5186 106.061 67.6944C106.052 67.6695 106.052 67.6446 106.045 67.6197C101.051 65.9661 104.442 60.5542 105.828 56.03C106.029 56.4869 106.541 56.4855 106.877 56.1794C107.126 55.9626 107.12 55.9172 108.423 53.1183C109.489 50.7851 107.775 50.1582 107.009 50.3135C107.578 45.4992 107.357 40.7523 102.769 38.8981C102.678 38.8395 102.586 38.7912 102.495 38.7487C102.451 31.8605 93.854 32.4434 94.4296 39.0138C88.2915 41.7688 90.3336 47.2685 90.5883 50.3384C89.8315 50.1055 88.7479 50.7016 88.6758 51.6873C88.608 52.5016 90.8372 58.0863 91.694 55.9553C92.9868 60.2393 96.2288 66.6691 91.0874 67.6768C91.0786 67.7105 91.0786 67.7354 91.0712 67.7676C90.6737 67.4571 90.3159 67.1217 89.8242 66.5021C88.9658 64.0987 86.1316 62.3763 85.6162 57.9076C84.6018 48.4461 81.5909 46.9961 84.4354 43.5893H84.431Z"
                        fill="#EFB960" />
                </g>
                <defs>
                    <clipPath id="clip0_2123_5579">
                        <rect width="197" height="132" fill="white" />
                    </clipPath>
                </defs>
            </svg>
            <div class="title">
                <h2 class="mb-2">Booking Confirmed!</h2>
                <p>Let your practice be a celebration of life.</p>
            </div>
            <div class="title mt-3">
                <p>Booking details is sent to <b class="italic"><EMAIL></b></p>
            </div>
        </div>
    </section>
    <?= $this->Form->end() ?>
    <script>
        const courseBatches = <?= json_encode($course->course_batches, JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT) ?>;
        const basePricesByCurrency = <?= !empty($pricing['basePrices']) ? json_encode($pricing['basePrices'], JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT) : [] ?>;
        const residencyOptions = <?= json_encode($residencyOptions) ?>;
        const courseAddons = <?= json_encode($pricing['addons'], JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT) ?>;
        const confimationURL = '<?= $this->Url->build(['controller' => 'Bookings', 'action' => 'confirmation']) ?>';
        const dialCodes = <?= !empty($countryCodes) ? json_encode($countryCodes, JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT) : [] ?>;
        window.foreignCurrencies = <?= json_encode($foreignCurrencies, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_AMP | JSON_HEX_QUOT) ?>;
        window.foodOptions = <?= json_encode($foodOptions, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_AMP | JSON_HEX_QUOT) ?>;
        window.countries = <?= json_encode($countries, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_AMP | JSON_HEX_QUOT) ?>;
        window.states = <?= json_encode($states, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_AMP | JSON_HEX_QUOT) ?>;

        function enrollmentPage() {
            return {
                isProcessing: false,
                step: 1,
                participantCount: 1,
                selectedBatch: null,
                batches: courseBatches || [],
                participants: [],
                basePrices: basePricesByCurrency,
                addons: courseAddons,
                residencyOptions: <?= json_encode($residencyOptions) ?>,
                selectedBillingCurrency: 'INR',
                countryCodes: dialCodes,
                foreignCurrencies: window.foreignCurrencies || {},
                domestic_tax: <?= $domestic_tax ?>,
                international_tax: <?= $international_tax ?>,
                states: window.states,
                billedToIndex: 0,
                billed_to: '',
                agreedToTerms: false,
                exchangeRates: {
                    INR: 1,
                    USD: 0.012,
                    EUR: 0.011
                },
                errors: {
                    batch: '',
                    participants: []
                },
                get exchangeRateDisplay() {
                    return `1 INR = ${this.exchangeRates[this.selectedBillingCurrency]} ${this.selectedBillingCurrency}`;
                },
                updateRate() { },
                toggleBatch(id) {
                    this.selectedBatch = this.selectedBatch === id ? null : id;
                },
                init() {
                    this.generateParticipants();
                },
                capitalize(str) {
                    return str ? str.charAt(0).toUpperCase() + str.slice(1) : '';
                },
                generateParticipants() {
                    const currentCount = this.participants.length;
                    for (let i = currentCount; i < this.participantCount; i++) {

                        if (!this.participants[i]) {
                            const residency = 'india'; // default residency
                            const residency_cuurency = 'INR';
                            const baseOptions = this.basePrices[residency_cuurency] || [];
                            const base_price_id = baseOptions.length > 0 ? baseOptions[0].id : null;

                            this.participants.push({
                                uid: Date.now() + '-' + Math.floor(Math.random() * 1000),
                                title: 'Mr',
                                first_name: '',
                                last_name: '',
                                email: '',
                                phone_country_code: '+91',
                                phone: '',
                                age: '',
                                food: '',
                                residency: 'india',
                                residency_currency: 'INR',
                                currency: '',
                                base_price_id: base_price_id,
                                selected_addons: [],
                                discount: 0,
                                state_id: ''
                            });
                        }
                    }

                    if (this.participants.length > this.participantCount) {
                        this.participants.splice(this.participantCount);
                    }
                },
                removeParticipant(index) {
                    if (this.participants.length > 1) {
                        this.participants.splice(index, 1); // remove participant
                        this.participantCount = this.participants.length; // keep count in sync
                    } else {
                        // Optional: reset the only remaining participant instead of removing
                        this.participants[0] = {
                            title: 'Mr',
                            first_name: '',
                            last_name: '',
                            email: '',
                            phone_country_code: '+91',
                            phone: '',
                            age: '',
                            food: '',
                            residency: 'india',
                            residency_currency: 'INR',
                            currency: '',
                            base_price_id: this.basePrices['INR']?.[0]?.id ?? null,
                            selected_addons: [],
                            discount: 0,
                            state_id: ''
                        };
                    }

                    // Optional: recalculate totals or update errors
                    this.errors.participants = Array(this.participants.length).fill('');
                },
                getBatchNameSummary(id) {
                    const batch = this.batches.find(b => b.id === id);
                    return batch ? this.formatDate(batch.start_date) + ' to ' + this.formatDate(batch.end_date) : '';
                },
                // Get base price list for a participant by residency
                getBasePricesForParticipant(participant) {
                    const currency = participant.residency_currency ? participant.residency_currency : this.selectedBillingCurrency;
                    return this.basePrices[currency] || [];
                },
                // Get base price object by ID across all currencies
                getBasePriceById(id) {
                    for (const currency in this.basePrices) {
                        const found = this.basePrices[currency].find(p => Number(p.id) === Number(id));
                        if (found) return found;
                    }
                    return null;
                },
                getAddonById(id) {
                    return this.addons.find(a => Number(a.id) === Number(id));
                },
                getAddonCurrency(addon, currencyCode) {
                    return addon?.prices?.[currencyCode] ? currencyCode : '';
                },
                getAddonLeftCount(addon, currencyCode) {
                    return addon ? addon.left_count + ' Left' : '';
                },
                toggleAddon(addon, participant) {
                    const price = addon.prices?.[participant.residency_currency];
                    if (!price) return;

                    const existingIndex = participant.selected_addons.findIndex(a => a.addon_id === addon.id);
                    if (existingIndex !== -1) {
                        participant.selected_addons.splice(existingIndex, 1); // remove
                    } else {
                        participant.selected_addons.push({
                            addon_id: addon.id,
                            price_id: price.id
                        });
                    }
                },
                isAddonSelected(addonId, participant) {
                    return participant.selected_addons.some(a => a.addon_id === addonId);
                },
                getBasePriceName(id) {
                    const base = this.getBasePriceById(id);
                    return base ? base.name : '';
                },
                getBasePriceAmount(id) {
                    const base = this.getBasePriceById(id);
                    return base ? `${base.price} ${base.currency}` : 'Not selected';
                },
                getAddonName(id, currencyCode = 'INR') {
                    const addon = this.addons.find(a => Number(a.id) === Number(id));
                    return addon ? addon.name : '';
                },
                // getAddonPrice(id, currencyCode = 'INR') {
                //     const addon = this.addons.find(a => Number(a.id) === Number(id));
                //     if (!addon) return '';
                //     const priceData = addon.prices?.[currencyCode];
                //     if (!priceData) return ``;

                //     return `${priceData.price} ${currencyCode}`;
                // },
                calculateSubtotalParticipant(participant) {
                    const base = this.getBasePriceById(participant.base_price_id);
                    const basePrice = base ? parseFloat(base.price) : 0;

                    const addonTotal = participant.selected_addons.reduce((sum, addonObj) => {
                        const addon = this.addons.find(a => a.id === addonObj.addon_id);
                        const addonPrice = addon?.prices?.[participant.residency_currency]?.price || 0;
                        return sum + parseFloat(addonPrice);
                    }, 0);

                    const discount = participant.discount ? parseFloat(participant.discount) : 0;
                    return (basePrice + addonTotal - discount).toFixed(2);
                },
                calculateTaxForParticipant(participant) {
                    const baseSubtotal = parseFloat(this.calculateSubtotalParticipant(participant));
                    const taxRate = participant.residency === 'india' ? this.domestic_tax : this.international_tax;
                    return (baseSubtotal * (taxRate / 100)).toFixed(2);
                },
                calculateTotalWithTaxParticipant(participant) {
                    const baseSubtotal = parseFloat(this.calculateSubtotalParticipant(participant));
                    const tax = parseFloat(this.calculateTaxForParticipant(participant));
                    return (baseSubtotal + tax).toFixed(2);
                },
                calculateSubtotalInBillingCurrency(participant) {

                    let baseNum = 0;
                    if (participant.base_price_id) {
                        const base = this.getBasePriceById(participant.base_price_id);
                        if (base) {
                            const baseValue = this.getBasePriceAmountInBillingCurrency(participant.base_price_id)?.split(' ')[0];
                            baseNum = parseFloat(baseValue) || 0;
                        }
                    }
                    // --- Addons ---
                    const addonsTotal = (participant.selected_addons || []).reduce((sum, addonObj) => {
                        if (!addonObj?.addon_id) return sum; // skip if no ID

                        const addonValue = this.getAddonValueInBillingCurrency(
                            addonObj.addon_id,
                            participant.residency_currency
                        );

                        return sum + (parseFloat(addonValue) || 0);
                    }, 0);

                    // --- Discount ---
                    const discount = parseFloat(participant.discount || 0);

                    return (baseNum + addonsTotal - discount).toFixed(2);
                },
                calculateTotalWithTaxInBillingCurrency(participant) {
                    const subtotal = parseFloat(this.calculateSubtotalInBillingCurrency(participant));
                    const tax = parseFloat(this.calculateTaxForParticipantInBillingCurrency(participant));
                    return (subtotal + tax).toFixed(2);
                },
                calculateGrandTotal() {
                    return this.participants.reduce((total, p) => {
                        return total + parseFloat(this.calculateTotalWithTaxInBillingCurrency(p));
                    }, 0).toFixed(2);
                },
                calculateTotalTax() {
                    const subtotal = this.participants.reduce((total, p) => {
                        return total + parseFloat(this.calculateSubtotalInBillingCurrency(p));
                    }, 0);

                    // const isInternational = this.participants.length && this.participants[0].residency !== 'india';
                    const taxRate = this.selectedBillingCurrency == 'INR' ? this.domestic_tax : this.international_tax;

                    return (subtotal * (taxRate / 100)).toFixed(2);
                },
                getBasePriceAmountInBillingCurrency(id) {
                    const base = this.getBasePriceById(id);
                    if (!base) return '';
                    const fromCurrency = base.currency;
                    const toCurrency = this.selectedBillingCurrency;
                    if (fromCurrency === toCurrency) {
                        return `${base.price} ${toCurrency}`;
                    }
                    const rateFrom = this.exchangeRates[fromCurrency] || 1;
                    const rateTo = this.exchangeRates[toCurrency] || 1;
                    const converted = (parseFloat(base.price) / rateFrom) * rateTo;
                    return `${converted.toFixed(2)} ${toCurrency}`;
                },
                getAddonPriceInBillingCurrency(addonId, fromCurrency) {
                    const addon = this.addons.find(a => Number(a.id) === Number(addonId));
                    if (!addon || !fromCurrency) return '';

                    const toCurrency = this.selectedBillingCurrency?.toUpperCase();
                    const fromCur = fromCurrency.toUpperCase();

                    const fromPrice = parseFloat(addon.prices?.[fromCur]?.price ?? 0);
                    if (!fromPrice) return `0.00 ${toCurrency}`;

                    // If same currency, no conversion
                    if (fromCur === toCurrency) {
                        return `${fromPrice.toFixed(2)} ${toCurrency}`;
                    }

                    const rateFrom = parseFloat(this.exchangeRates[fromCur] ?? 1);
                    const rateTo = parseFloat(this.exchangeRates[toCurrency] ?? 1);

                    const converted = (fromPrice / rateFrom) * rateTo;
                    return `${converted.toFixed(2)} ${toCurrency}`;
                },
                getAddonPrice(addonId, currencyCode) {
                    const addon = this.addons.find(a => Number(a.id) === Number(addonId));

                    if (!addon) return '';
                    const priceData = addon.prices?.[currencyCode];

                    if (!priceData) {
                        return '';
                        // Try to convert from another currency if not available
                        // const firstCurrency = Object.keys(addon.prices || {})[0];
                        // if (!firstCurrency) return '';
                        // const rateFrom = this.exchangeRates[firstCurrency] || 1;
                        // const rateTo = this.exchangeRates[currencyCode] || 1;
                        // const converted = (parseFloat(addon.prices[firstCurrency].price) / rateFrom) * rateTo;
                        // return `${converted.toFixed(2)}`;
                    }
                    return `${priceData.price}`;
                },
                getAddonValueInBillingCurrency(addonId, fromCurrency) {
                    const addon = this.addons.find(a => Number(a.id) === Number(addonId));
                    if (!addon) return 0;

                    const toCurrency = (this.selectedBillingCurrency || '').trim().toUpperCase();
                    const fromCur = (fromCurrency || '').trim().toUpperCase();

                    const fromPrice = addon.prices?.[fromCur]?.price ?? 0;

                    if (fromCurrency === toCurrency) {
                        return parseFloat(fromPrice);
                    }


                    const rateFrom = this.exchangeRates[fromCurrency] || 1;
                    const rateTo = this.exchangeRates[toCurrency] || 1;
                    return (parseFloat(fromPrice) / rateFrom) * rateTo;
                },
                getSelectedBatchLeftCount() {
                    if (!this.selectedBatch) return null; // Return null if no batch selected
                    const batch = this.batches.find(b => b.id === this.selectedBatch);
                    if (!batch) return null;
                    const left = batch.capacity - batch.booked_count;
                    return left > 0 ? left : 0;
                },
                getTaxRateForParticipant(selectedBillingCurrency) {
                    return selectedBillingCurrency === 'INR'
                        ? this.domestic_tax
                        : this.international_tax;
                },
                validate() {
                    // Clear previous errors
                    this.errors.batch = '';
                    this.errors.participants = this.participants.map(() => ({}));

                    if (!this.selectedBatch) {
                        this.errors.batch = 'Please select a batch.';
                        document.getElementById('section-batch').scrollIntoView({ behavior: 'smooth' });
                        return false;
                    }

                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    const phoneRegex = /^\+?[0-9]{7,15}$/;

                    for (const [i, participant] of this.participants.entries()) {
                        const errors = {};

                        if (!participant.first_name) errors.first_name = 'First name is required.';
                        if (!participant.last_name) errors.last_name = 'Last name is required.';
                        if (!participant.email) {
                            errors.email = 'Email is required.';
                        } else if (!emailRegex.test(participant.email)) {
                            errors.email = 'Invalid email format.';
                        }

                        if (!participant.phone) {
                            errors.phone = 'Phone number is required.';
                        } else if (!phoneRegex.test(participant.phone)) {
                            errors.phone = 'Invalid phone number.';
                        }

                        // if (!participant.age) errors.age = 'Age is required.';
                        if (!participant.phone_country_code) errors.phone_country_code = 'Phone country code is required.';
                        if (!participant.residency) errors.residency = 'Residency is required.';
                        if (!participant.base_price_id) errors.base_price_id = 'Base price is required.';

                        if (participant.residency === 'india' && !participant.state_id) {
                            errors.state_id = 'State is required for Indian residents.';
                        }

                        if (Object.keys(errors).length > 0) {
                            this.errors.participants[i] = errors;
                            const el = document.getElementById('participant-' + i);
                            if (el) el.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            return false;
                        }
                    }

                    if (!this.agreedToTerms) {
                        swal.fire({
                            text: 'You must agree to the terms before proceeding',
                            icon: "error"
                        });
                        return false;
                    }

                    return true;
                },
                calculateTaxForParticipantInBillingCurrency(participant) {
                    const subtotal = parseFloat(this.calculateSubtotalInBillingCurrency(participant));
                    const taxRate = this.selectedBillingCurrency === 'INR' ? this.domestic_tax : this.international_tax;
                    return (subtotal * (taxRate / 100)).toFixed(2);
                },
                submitBooking(event) {
                    // Optional: Validate again before submission
                    if (event) event.preventDefault();
                    if (!this.validate()) return;
                    this.isProcessing = true;
                    const payload = {
                        course_id: <?= (int) $course->id ?>,
                        batch_id: this.selectedBatch,
                        participants: this.participants,
                        billing_currency: this.selectedBillingCurrency,
                        exchange_rate: this.exchangeRates[this.selectedBillingCurrency],
                        billed_to: this.billedToIndex,
                    };

                    fetch('<?= $this->Url->build(['controller' => 'Bookings', 'action' => 'add']) ?>', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify(payload)
                    })
                        .then(response => response.json())
                        .then(data => {
                            let errorMessages = [];
                            if (data.success) {
                                // Redirect or show confirmation
                                const encodedBookingId = btoa(data.booking_id.toString());
                                window.location.href = `${confimationURL}/${encodedBookingId}`;
                            } else {
                                this.errors.batch = '';
                                this.errors.participants = this.participants.map(() => ({})); // always array of objects

                                if (data.errors) {
                                    // Set batch-level error
                                    if (data.errors.batch) {
                                        this.errors.batch = data.errors.batch;
                                        errorMessages.push(data.errors.batch);
                                    }

                                    // Set participant-level errors
                                    if (Array.isArray(data.errors.participants)) {
                                        data.errors.participants.forEach((errorObj, index) => {
                                            // If errorObj is a string, wrap it as an object
                                            if (typeof errorObj === 'string') {
                                                this.errors.participants[index] = { general: errorObj };
                                                errorMessages.push(`Participant ${index + 1}: ${errorObj}`);
                                            } else if (typeof errorObj === 'object' && errorObj !== null) {
                                                this.errors.participants[index] = errorObj;
                                                Object.entries(errorObj).forEach(([field, msg]) => {
                                                    errorMessages.push(`Participant ${index + 1} - ${field}: ${msg}`);
                                                });
                                            }
                                        });
                                    }
                                }
                                const formattedHtml = `<ul class="text-center pl-4 list-disc">${errorMessages.map(msg => `<li>${msg}</li>`).join('')}</ul>`;

                                swal.fire({
                                    title: data.message || 'Booking failed. Please try again.',
                                    html: formattedHtml,
                                    icon: "error"
                                });
                                this.isProcessing = false;
                            }
                        })
                        .catch(error => {
                            swal.fire({
                                text: 'Something went wrong while submitting the booking. Try again later',
                                icon: "error"
                            });
                            this.isProcessing = false;
                        });
                },
                hasParticipantDetails() {
                    return this.participants.some(p =>
                        p.first_name && p.last_name && p.email && p.phone && p.residency && p.base_price_id
                    );
                },
                formatDate(dateStr) {
                    const date = new Date(dateStr);
                    return date.toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric'
                    });
                },
                onResidencyChange(participant) {
                    if (participant.residency === 'india') {
                        participant.residency_currency = 'INR';
                        participant.currency = '';
                    } else {
                        if (!participant.currency) {
                            const firstKey = Object.keys(this.foreignCurrencies)[0];
                            const firstCurrency = this.foreignCurrencies[firstKey];
                            participant.currency = firstCurrency;
                        }
                        participant.residency_currency = participant.currency;
                    }
                    // Get base prices for the updated currency
                    const baseOptions = this.basePrices[participant.residency_currency] || [];

                    // Select the first base price by default (if available)
                    participant.base_price_id = baseOptions.length > 0 ? baseOptions[0].id : null;
                    //this.getBasePricesForParticipant(participant);
                },

                onCurrencyChange() {
                    if (this.participant.currency) {
                        this.participant.residency_currency = this.foreignCurrencies[this.participant.currency];
                    } else {
                        const firstKey = Object.keys(this.foreignCurrencies)[0];
                        const firstCurrency = this.foreignCurrencies[firstKey];
                        this.participant.currency = firstCurrency;
                        this.participant.residency_currency = firstCurrency;
                    }

                    this.getBasePricesForParticipant(this.participant);
                    // Update addon price IDs according to new currency
                    if (Array.isArray(this.participant.selected_addons) && this.participant.selected_addons.length) {
                        this.participant.selected_addons = this.participant.selected_addons.map(selAddon => {
                            const addon = this.addons.find(a => Number(a.id) === Number(selAddon.addon_id));
                            if (!addon) return selAddon; // keep unchanged if addon not found

                            const newPrice = addon.prices?.[this.participant.residency_currency];
                            if (!newPrice) return selAddon; // no matching price in this currency

                            return {
                                addon_id: addon.id,
                                price_id: newPrice.id
                            };
                        });
                    }
                }
            }
        }
    </script>

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

        .billingCurrency-element .billingCurrency-body .list-rate {
            padding-left: 0px !important;
        }

        .group-booking h2 {
            font-family: "Open Sans", sans-serif;
            font-size: 40px;
            font-weight: 700;
            color: #293148;
            text-decoration: underline;
        }

        .group-booking .content p {
            font-family: "Open Sans", sans-serif;
            font-size: 18px;
            font-weight: 600;
        }

        .time-info .italic {
            font-family: "Open Sans", sans-serif;
            font-style: italic;
            font-size: 20px;
            font-weight: 700;
            color: #000000;
        }

        .grp-booking-body {
            border-bottom: 1px solid #C45F44;
            padding: 40px 0px;
        }

        /* section1 */
        /* section2 */
        .select-branch h3 {
            font-family: "Open Sans", sans-serif;
            font-size: 32px;
            font-weight: 700;
            color: #151515;
        }

        .select-branch .select-date {
            font-family: "Open Sans", sans-serif;
            font-size: 20px;
            font-weight: 700;

        }

        .select-branch .select-date span {
            font-weight: 700;
        }

        .form-content-title {
            font-size: 14px;
            font-weight: 400;
            font-family: "Open Sans", sans-serif;
            color: #231F20;
        }

        .base-cost-label {
            display: flex;
        }

        .select-residence-label {
            margin-bottom: 20px;
        }

        .residence-options .select-residence-label:last-child {
            margin-bottom: 10px !important;
        }

        .terms-condition a {
            color: #0F43CA;
            text-decoration: underline;
        }

        .increment-decrement .leading-none {
            border-radius: 40px
        }

        .leading-none {
            font-weight: 700;
            font-size: 15px;
            text-align: left;
            text-decoration: underline;
        }

        .save-participant {
            text-transform: UpperCase;
            font-weight: 700;
            font-size: 16px;
            font-family: "Open Sans", sans-serif;
            border-radius: 20px
        }
.booking-successfull .title h2{
    font-size:20px;
    font-weight:600;
    color:#111827;
}
.booking-successfull .title p{
    font-size:14px;
    font-weight:400;
    color:#111827;
}
        @media only screen and (max-width: 700px) {
            .group-booking h2 {
                font-size: 18px;
            }

            .form-content-title {
                font-size: 14px;
                font-weight: 400;
                font-family: "Open Sans", sans-serif;
                color: #231F20;
            }

            .group-booking .content p {
                font-family: "Poppins", sans-serif;
                font-size: 10px;
                font-weight: 600;
                margin-bottom: 0px;
            }

            .select-branch h3 {
                font-family: "Poppins", sans-serif;
                padding: 20px 0px 0px;
                font-size: 14px;
                font-weight: 500;
            }

            .select-branch .select-date {
                font-size: 10px;
                padding: 10px 5px !important;
                color: #000000;
            }

            .select-branch .select-date:hover {
                font-size: 10px;
                background: #C45F44;
                color: #FFFFFF;
                padding: 10px 5px;
            }

            .grp-booking-body {
                border-bottom: 0px solid #C45F44;
                padding: 20px 0px 5px;
            }

            .grp-booking-body-content {
                border-bottom: 2px solid #C45F44;
                padding: 0px 0px 20px;
            }

            .time-info .italic {
                font-size: 12px;
            }

            .select-branch-grid-content {
                padding: 0px 0px 30px !important;
            }

            .Participants h1 {
                font-size: 18px;
                font-weight: 700;
            }

            .Participants .head .increment-decrement {
                width: 110px;
                background: #FDEDE0;
                border-radius: 12px;
                padding: 5px;
                margin: 0px;
                justify-content: space-between;
            }

            .Participants {
                background: #FFFFFF;
            }

            .select-residence-text {
                font-family: "Inter", sans-serif;
            }

            .Participants .head .increment-decrement span {
                margin: 0px 5px;
                line-height: 12px;
            }

            .Participants .head .increment-decrement #decreaseBtn,
            .Participants .head .increment-decrement #increaseBtn {
                height: 23px;
                width: 23px !important;
                margin: 0px;
                font-weight: 600;
                line-height: 12px;
                padding-bottom: 3px;
            }

            .Participants #accordionContainer .rounded-md {
                border-radius: 11px;
                padding: 10px;
                font-size: 15px;
                font-weight: 600;
            }

            .select-branch-grid-content .content {
                margin-top: 25px !important;
            }


            .form-content {
                justify-content: space-between;
            }

            .form-content input,
            .form-content select {
                border: 1px solid #6C6C6C !important;
                border-radius: 20px;
                background: #fff;
                padding: 13px 5px;
            }

            .form-content select {
                color: #00000033
            }

            .form-content .select-state {
                border: 1px solid #6C6C6C;
                border-radius: 20px;
                background: #fff;
                padding: 5px 10px;
            }

            .form-phone .code {
                border-top-left-radius: 20px;
                border-bottom-left-radius: 20px;
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
                border: 1px solid #6C6C6C;
            }

            .form-phone .phone {
                border-top-right-radius: 20px;
                border-bottom-right-radius: 20px;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
                border: 1px solid #6C6C6C;
            }

            .form-content .country {
                font-size: 16px;
                font-weight: 500;
                color: #344054;
                font-family: "Inter", sans-serif;
            }

            .head-sec-title {
                font-size: 18px;
                font-weight: 700;
                color: #6A9E74;
                text-transform: capitalize;
            }

            .cost-price {
                font-size: 18px;
                font-weight: 700;
                font-family: "Open Sans", sans-serif;

            }

            .cost-title {
                font-family: "Open Sans", sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #344054;
            }

            .item-left {
                font-family: "Open Sans", sans-serif;
                font-size: 12px;
                font-style: italic;
                font-weight: 600;
                color: #00AE4D;
            }

            .full-booked {
                font-family: "Open Sans", sans-serif;
                font-size: 12px;
                font-style: italic;
                font-weight: 600;
                color: #000000;
            }

            .billingCurrency-body label {
                font-size: 14px;
                font-weight: 600;
                font-family: "Open Sans", sans-serif;
                color: #000000;
            }

            .billingCurrency-body .relative select {
                font-size: 14px;
                font-weight: 700;
                font-family: "Open Sans", sans-serif;
                color: #000000;
            }

            .continue-sticky {
                position: fixed;
                bottom: 0px;
                width: 100%;
                z-index: 9;
                left: 0;
            }

            .exc-rate .title {
                font-style: italic;
                font-size: 12px;
                font-weight: 600;
                font-family: "Open Sans", sans-serif;
                text-decoration: underline;
            }

            .exc-rate .list-rate {
                font-style: italic;
                font-size: 12px;
                font-weight: 600;
                color: #000000;
            }

            .exc-rate .Disclaimer {
                color: #888888;
                font-size: 8px;
                font-weight: 600;
                font-style: italic;
            }

            .billingCurrency-element {
                border-radius: 6px;
            }

            .billed .title {
                font-size: 14px;
                font-weight: 400;
                font-family: "Open Sans", sans-serif;
                color: #231F20;
            }

            .billed .body select {
                font-size: 15px;
                color: #983419;
                font-weight: 600;
            }

            .participants-title {
                font-size: 18px;
                font-weight: 700;
                font-family: "Open Sans", sans-serif;
            }

            .privacy-policy p {
                color: #000000;
                font-size: 10px;
                font-family: "Poppins", sans-serif;
            }

            .privacy-policy a {
                font-weight: 500;
                color: #0F43CA;
                font-size: 10px;
                font-family: "Poppins", sans-serif;
            }

            .payment-summary button {
                font-size: 15px;
                font-weight: 700;
                color: #283148;
                font-family: "Open Sans", sans-serif;
            }

            #payment-summary-content h2 {
                font-size: 14px;
                color: #000000;
                font-weight: 700;
                font-family: "Open Sans", sans-serif;
            }

            .about-user {
                font-size: 12px;
                color: #727272;
                font-weight: 600;
                font-family: "Open Sans", sans-serif;
            }

            .cost-breakdown {
                font-style: italic;
                font-size: 14px;
                font-weight: 400;
                font-family: "Open Sans", sans-serif;
            }

            .cost-breakdown .text-red-600 {
                color: #62200E;
                font-weight: 600;
            }

            .cost-breakdown .discount-applied .text-red-600 {
                color: #FF0000;
                font-weight: 600;
            }

            .base-price {
                font-family: "Open Sans", sans-serif;
            }

            .cost-breakdown .new {
                font-weight: 700;
                font-family: "Open Sans", sans-serif;
            }

            .cost-breakdown .total .text-red-600 {
                font-weight: 700;
            }

            .discount button {
                color: #479456;
            }

            .total-before-tax span {
                font-weight: 600;
                font-size: 14px;
                font-family: "Open Sans", sans-serif;
                color: #000000;
            }

            .total-before-tax .value {
                font-weight: 700;
                font-size: 16px;
                font-family: "Open Sans", sans-serif;
                color: #000000;
            }

            .Applicable-tax .value {
                font-weight: 700;
                font-size: 12px;
                font-family: "Open Sans", sans-serif;
                color: #505050;
            }

            .save-participant {
                text-transform: UpperCase;
                font-weight: 700;
                font-size: 16px;
                font-family: "Open Sans", sans-serif;
                border-radius: 7px
            }
        }

        @media only screen and (max-width: 390px) {
            .Participants .head .increment-decrement span {
                margin: 0px 5px;
                line-height: 12px;
                font-size: 17px;
            }
        }
    </style>