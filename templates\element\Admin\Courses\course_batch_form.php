<div x-data="batchForm(<?= !empty($batchJson) ? h($batchJson) : '' ?>)" class="alpine-section mb-4">
    <?php $this->Form->setTemplates([
        'inputContainer' => '{{content}}', // removes <div class="input ...">
    ]); ?>
    <h5 class="mb-3">Course Batches</h5>
    <div class="form-group row">
        <label for="name" class="col-sm-2 col-form-label">Duration Details</label>
        <div class="col-sm-6 main-field">
            <?php echo $this->Form->control('duration_details', [
                'type' => 'text',
                'class' => 'form-control',
                'id' => 'duration_details',
                'placeholder' => __('Duration Details'),
                'label' => false,
                'required' => false
            ]); ?>
        </div>
    </div>
    <div class="accordion" id="batchAccordion">
        <template x-for="(batch, index) in batches" :key="index">
            <div class="accordion-item mb-3" :data-index="index">
                <h2 class="accordion-header" :id="`batchHeading_${index}`">
                    <button class="accordion-button" type="button"
                        :data-bs-toggle="'collapse'"
                        :data-bs-target="`#batchCollapse_${index}`"
                        :aria-expanded="index === 0 ? 'true' : 'false'"
                        :aria-controls="`batchCollapse_${index}`">
                        Batch <span x-text="index + 1"></span>
                    </button>
                </h2>
                <div :id="`batchCollapse_${index}`" class="accordion-collapse collapse show"
                    :aria-labelledby="`batchHeading_${index}`" data-bs-parent="#batchAccordion">
                    <div class="accordion-body position-relative">
                        <div class="d-flex justify-content-end mb-2">
                            <button type="button" class="btn btn-outline-danger btn-sm" @click="removeBatch(index)">
                                Remove
                            </button>
                        </div>

                        <div class="row mb-3">
                            <label class="col-sm-2 col-form-label">Name</label>
                            <div class="col-sm-6">
                                <?= $this->Form->text('batch_name[]', [
                                    'class' => 'form-control',
                                    'x-model' => 'batch.batch_name',
                                    'placeholder' => 'Enter Name',
                                    'label' => false
                                ]) ?>
                                <div class="text-danger text-sm" x-show="showErrors && !batch.batch_name">Batch Name is required</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3 align-items-center">
                            <label class="col-sm-2 col-form-label">Duration</label>

                            <!-- Start Date -->
                            <div class="col-sm-3">
                                <div class="d-flex align-items-center">
                                    <label class="mb-0 me-2" style="min-width: 40px;">Start</label>
                                    <?= $this->Form->control('start_date[]', [
                                        'type' => 'date',
                                        'class' => 'form-control',
                                        'x-model' => 'batch.start_date',
                                        'label' => false
                                    ]) ?>
                                </div>
                                <div class="text-danger text-sm mt-1" x-show="showErrors && !batch.start_date">
                                    Start Date is required
                                </div>
                            </div>

                            <!-- End Date -->
                            <div class="col-sm-3">
                                <div class="d-flex align-items-center">
                                    <label class="mb-0 me-2" style="min-width: 40px;">End</label>
                                    <?= $this->Form->control('end_date[]', [
                                        'type' => 'date',
                                        'class' => 'form-control',
                                        'x-model' => 'batch.end_date',
                                        'label' => false
                                    ]) ?>
                                </div>
                                <div class="text-danger text-sm mt-1" x-show="showErrors && (!batch.end_date || new Date(batch.end_date) < new Date(batch.start_date))">
                                    End date is required and must be after start date
                                </div>
                            </div>
                        </div>


                        <div class="row mb-3 align-items-center">
                            <label class="col-sm-2 col-form-label">Timing</label>

                            <!-- Start Time -->
                            <div class="col-sm-3">
                                <div class="d-flex align-items-center">
                                    <label class="mb-0 me-2" style="min-width: 40px;">Start</label>
                                    <input type="time"
                                        class="form-control timepicker1"
                                        x-model="batch.start_time"
                                        @change="batch.start_time = $event.target.value"
                                        placeholder="Select Start Time">
                                </div>
                            
                            </div>

                            <!-- End Time -->
                            <div class="col-sm-3">
                                <div class="d-flex align-items-center">
                                    <label class="mb-0 me-2" style="min-width: 40px;">End</label>
                                    <input type="time"
                                        class="form-control timepicker1"
                                        x-model="batch.end_time"
                                        @change="batch.end_time = $event.target.value"
                                        placeholder="Select End Time">
                                </div>
                                <div class="text-danger text-sm mt-1"
                                    x-show="showErrors && batch.end_time && batch.start_time && batch.end_time <= batch.start_time">
                                    End Time is required and must be after start time
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <label class="col-sm-2 col-form-label">Capacity</label>
                            <div class="col-sm-6">
                                <?= $this->Form->text('capacity[]', [
                                    'type' => 'number',
                                    'min' => 1,
                                    'class' => 'form-control',
                                    'x-model' => 'batch.capacity',
                                    'placeholder' => 'Enter capacity',
                                    'label' => false
                                ]) ?>
                                <div class="text-danger text-sm" x-show="showErrors && !batch.capacity">Capacity is required</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-sm-2 col-form-label">Status</label>
                            <div class="col-sm-6">
                                <select class="form-select" x-model="batch.status">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                                <div class="text-danger text-sm" x-show="showErrors && !batch.status">Status is required</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>

    <button type="button" class="btn btn-outline-primary mt-3" @click="addBatch()">+ Add Batch</button>

    <!-- Hidden field to send full JSON data -->
    <textarea name="course_batches_json" class="d-none" x-text="jsonBatches"></textarea>
</div>

<?php $this->append('script'); ?>
<script>
 $(document).ready(function(){
    $('.timepicker').timepicker({
        timeFormat: 'h:mm p',
        interval: 30,
        minTime: '6',
        maxTime: '11:30pm',
        defaultTime: '10',
        startTime: '6:00',
        dynamic: false,
        dropdown: true,
        scrollbar: true
    });
});

 function batchForm(initialBatches = []) {
    return {
        showErrors: false,
        batches: initialBatches.map(b => ({
            batch_name: b.batch_name || '',
            start_date: b.start_date || '',
            start_time: b.start_time || '',
            end_date: b.end_date || '',
            end_time: b.end_time || '',
            capacity: b.capacity || '',
            status: b.status || 'active'
        })),
        addBatch() {
            this.batches.push({
                batch_name: '',
                start_date: '',
                start_time: '',
                end_date: '',
                end_time: '',
                capacity: '',
                status: 'active'
            });
            this.initTimepickers(); // apply timepicker to new inputs
        },
        removeBatch(index) {
            this.batches.splice(index, 1);
        },
        validateBatchForm() {
            this.showErrors = true;

            // No batches at all
            if (this.batches.length === 0) {
                return false;
            }

            return this.batches.every(batch => {
                const allFieldsFilled = batch.batch_name && batch.start_date && batch.start_time &&
                    batch.end_date && batch.end_time && batch.capacity && batch.status;

                const start = new Date(batch.start_date);
                const end = new Date(batch.end_date);

                const datesValid = start <= end;

                return allFieldsFilled && datesValid;
            });
        },
        get jsonBatches() {
            return JSON.stringify(this.batches);
        },
         initTimepickers() {
            setTimeout(() => {
                $('.timepicker').timepicker({
                    timeFormat: 'HH:mm',          // 24-hour format (e.g., 13:30)
                    interval: 15,               // 15-minute steps
                    minTime: '3:00',            // Start at 03:00
                    maxTime: '23:45',           // End at 23:45
                    defaultTime: '',            // No default selected
                    dynamic: false,
                    dropdown: true,
                    scrollbar: true
                }).on('changeTime', function() {
                    // Force Alpine to update the x-model
                    this.dispatchEvent(new Event('change', { bubbles: true }));
                });
            }, 100); // slight delay to ensure DOM is ready
        },
        init() {
            this.initTimepickers(); // initialize on page load
        }
    };
}
</script>
<?php $this->end(); ?>
