<script>
    var baseUrl = '<?= \Cake\Routing\Router::url("/", true); ?>';
    var lang = '<?= $this->request->getParam('lang') ?>';
    var country = '<?= $this->request->getParam('country') ?>';
</script>
<nav class="nav-bar-menu flex items-center justify-between w-[100%] py-2"
    x-data="{ mobileOpen: false, dropdownOpen: false }">
    <div>
        <a href="<?= $this->Url->build(['controller' => 'Home', 'action' => 'index']) ?>"><img
                src="<?= $this->Url->webroot('img/yoga-logo.png') ?>" class="w-26 yogain-logo" alt="logo"></a>
    </div>
    <div class="menus">

        <ul class="flex items-center justify-between nav-gap">
            <li x-data="{ isOpen: false }" @mouseenter="isOpen = true" @mouseleave="isOpen = false">
                <div class="relative group" x-cloak>
                    <a href="#" class="text-black py-2 dropdown active:text-black focus:text-black">
                        Courses
                    </a>

                    <div class="absolute dropdown-submenu mt-2 z-1 w-48 bg-white shadow-lg rounded-lg" x-show="isOpen" x-transition:enter="transition ease-out duration-1000"
                        x-transition:enter-start="opacity-0"
                        x-transition:enter-end="opacity-100"
                        x-transition:leave="transition ease-in duration-200"
                        x-transition:leave-start="opacity-100"
                        x-transition:leave-end="opacity-0" 
                        @mouseenter="isOpen = true" @mouseleave="isOpen = false">
                        <a href="<?= $this->Url->build([
                                'lang' => $this->request->getParam('lang'),
                                'controller' => 'Courses',
                                'action' => 'index',
                                'country' => $this->request->getParam('country') ?? 'india'
                            ]) ?>"
                            class="block px-4 py-2"> All Courses</a>
                        <?php if (!empty($courseTypes)):
                            foreach ($courseTypes as $type):
                        ?>
                                <a href="<?= $this->Url->build([
                                        'lang'       => $this->request->getParam('lang'),
                                        'controller' => 'Courses',
                                        'action'     => 'index',
                                        'country'    =>  $this->request->getParam('country') ?? 'india',
                                        'type'       =>  urlencode($type->slug)
                                    ]) ?>"
                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-200"><?= $type->name ?></a>
                        <?php
                            endforeach;
                        endif; ?>
                    </div>
                </div>

                <!-- <a class="hover:text-gray-500" href="#">Courses & Classes</a> -->
            </li>
            <li x-data="{ isOpen: false }" @mouseenter="isOpen = true" @mouseleave="isOpen = false">
                <div class="relative group" x-cloak>
                    <a href="#" class="text-black py-2 dropdown active:text-black focus:text-black">
                        Classes
                    </a>
                    <div class="absolute dropdown-submenu mt-2 z-1 w-48 bg-white shadow-lg rounded-lg" x-show="isOpen" x-transition:enter="transition ease-out duration-1000"
                        x-transition:enter-start="opacity-0"
                        x-transition:enter-end="opacity-100"
                        x-transition:leave="transition ease-in duration-200"
                        x-transition:leave-start="opacity-100"
                        x-transition:leave-end="opacity-0" 
                        @mouseenter="isOpen = true" @mouseleave="isOpen = false">
                        <?php if (!empty($yogaStyles)):
                            foreach ($yogaStyles as $style):
                        ?>
                                <a href="<?= $this->Url->build(['controller' => 'Courses', 'action' => 'index']) ?>?type='<?= $style ?>'"
                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-200"><?= $style ?></a>
                        <?php
                            endforeach;
                        endif; ?>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Ahmedabad</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Bangalore</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Delhi</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Chennai</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Hyderabad</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Kolkata</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Mumbai</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Other Locations</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Online Classes</a>
                    </div>
                </div>

                <!-- <a class="hover:text-gray-500" href="#">Courses & Classes</a> -->
            </li>
            <li x-data="{ isOpen: false }" @mouseenter="isOpen = true" @mouseleave="isOpen = false">
                <div class="relative group" x-cloak>
                    <a href="#" class="text-black py-2 dropdown active:text-black focus:text-black">
                        Centers & Ashrams
                    </a>

                    <div class="absolute dropdown-submenu mt-2 z-1 w-48 bg-white shadow-lg rounded-lg" x-show="isOpen" x-transition:enter="transition ease-out duration-1000"
                        x-transition:enter-start="opacity-0"
                        x-transition:enter-end="opacity-100"
                        x-transition:leave="transition ease-in duration-200"
                        x-transition:leave-start="opacity-100"
                        x-transition:leave-end="opacity-0" 
                        @mouseenter="isOpen = true" @mouseleave="isOpen = false">
                        <a href="<?= $this->Url->build([
                                        'lang' => $this->request->getParam('lang'),
                                        'controller' => 'Partners',
                                        'action' => 'index',
                                        'country' => $this->request->getParam('country') ?? 'india'
                                    ]) ?>"
                            class="block px-4 py-2 text-gray-700 hover:bg-gray-200"> All Centers</a>
                        <a href="<?= $this->Url->build('/') . h($this->request->getParam('lang')) ?>/yoga-centers/india/south-india/goa"
                            class="block px-4 py-2 text-gray-700 hover:bg-gray-200"> Goa</a>
                        <a href="<?= $this->Url->build('/') . h($this->request->getParam('lang')) ?>/yoga-centers/india/south-india/karnataka/mysuru (mysore)"
                            class="block px-4 py-2 text-gray-700 hover:bg-gray-200"> Mysore</a>
                        <a href="<?= $this->Url->build('/') . h($this->request->getParam('lang')) ?>/yoga-centers/india/south-india/kerala"
                            class="block px-4 py-2 text-gray-700 hover:bg-gray-200"> Kerala</a>
                        <a href="<?= $this->Url->build('/') . h($this->request->getParam('lang')) ?>/yoga-centers/india/north-india/uttrakhand/rishikesh"
                            class="block px-4 py-2 text-gray-700 hover:bg-gray-200"> Rishikesh</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Other Locations</a>
                    </div>
                </div>
                <!-- <a class="hover:text-gray-500" href="#">Centers & Teachers</a> -->
            </li>
            <li x-data="{ isOpen: false }" @mouseenter="isOpen = true" @mouseleave="isOpen = false">
                <div class="relative group" x-cloak>
                    <a href="#" class="text-black py-2 dropdown active:text-black focus:text-black">
                        <!-- Discover -->Teachers
                    </a>

                    <div class="absolute dropdown-submenu mt-2 z-1 w-48 bg-white shadow-lg rounded-lg" x-show="isOpen" x-transition:enter="transition ease-out duration-1000"
                        x-transition:enter-start="opacity-0"
                        x-transition:enter-end="opacity-100"
                        x-transition:leave="transition ease-in duration-200"
                        x-transition:leave-start="opacity-100"
                        x-transition:leave-end="opacity-0" 
                        @mouseenter="isOpen = true" @mouseleave="isOpen = false">
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Ahmedabad</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Bangalore</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Delhi</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Chennai</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Hyderabad</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Kolkata</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Mumbai</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Other Locations</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Online Teachers</a>
                    </div>
                </div>
                <!-- <a class="hover:text-gray-500" href="#">Discover</a> -->
            </li>
            <li x-data="{ isOpen: false }" @mouseenter="isOpen = true" @mouseleave="isOpen = false">
                <div class="relative group" x-cloak>
                    <a href="#" class="text-black py-2 dropdown active:text-black focus:text-black">
                        <!-- Discover -->Yoga Info
                    </a>

                    <div class="absolute dropdown-submenu mt-2 z-1 w-48 bg-white shadow-lg rounded-lg" x-show="isOpen" x-transition:enter="transition ease-out duration-1000"
                        x-transition:enter-start="opacity-0"
                        x-transition:enter-end="opacity-100"
                        x-transition:leave="transition ease-in duration-200"
                        x-transition:leave-start="opacity-100"
                        x-transition:leave-end="opacity-0" 
                        @mouseenter="isOpen = true" @mouseleave="isOpen = false">
                        <a href="<?= h($configSettings['WP_ASANAS_LIBRARY']) ?>"
                            class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Asanas Library</a>
                        <a href="<?= h($configSettings['WP_DISCOVER_URL']) ?>" target="_blank"
                            class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Discover Yoga</a>
                        <a href="<?= h($configSettings['WP_DESTINATIONS']) ?>"
                            class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Destinations</a>
                        <a href="<?= h($configSettings['WP_NEWS_EVENTS']) ?>" target="_blank"
                            class="block px-4 py-2 text-gray-700 hover:bg-gray-200">News & Events</a>
                        <a href="<?= h($configSettings['WP_BLOGS_URL']) ?>" target="_blank"
                            class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Blog</a>
                    </div>
                </div>
                <!-- <a class="hover:text-gray-500" href="#">Discover</a> -->
            </li>
            <!-- <li class="partner-list">
                <?= $this->Html->link(
                    'Partners',
                    ['controller' => 'Partners', 'action' => 'partnerWithUs', 'prefix' => false],
                    ['class' => '  px-4 py-2 rounded-half', 'escape' => false]
                ) ?>
            </li> -->
            <li class="partner-list" x-data="{ isOpen: false }" @mouseenter="isOpen = true" @mouseleave="isOpen = false">
                <div class="relative group login" x-cloak>
                    <?php if ($customerData): ?>
                        <a class="px-2 py-2 rounded-half dropdown" type="button">
                            <span class="init-letter"><?= substr($customerData->first_name, 0, 1) ?></span>
                            <span class="customer-name" title="<?= h($customerData->first_name . ' ' . $customerData->last_name) ?>"><?= h($customerData->first_name . ' ' . $customerData->last_name) ?></span>
                        </a>
                    <?php else: ?>
                        <a class="px-2 py-2 rounded-half dropdown" type="button">My Account</a>
                    <?php endif; ?>

                    <!-- Dropdown menu -->
                    <div class="z-10 absolute bg-white dropdown-submenu mt-2 z-1 w-57 bg-white shadow-lg rounded-lg" x-show="isOpen" x-transition:enter="transition ease-out duration-1000"
                        x-transition:enter-start="opacity-0"
                        x-transition:enter-end="opacity-100"
                        x-transition:leave="transition ease-in duration-200"
                        x-transition:leave-start="opacity-100"
                        x-transition:leave-end="opacity-0" 
                        @mouseenter="isOpen = true" @mouseleave="isOpen = false">
                          <?php if ($customerData): ?>
                        <p class="account-title px-4 py-2">Student</p>
                        <ul class="text-sm text-gray-700 dark:text-gray-200 loginclick" aria-labelledby="dropdownDefaultButton">
                            <li>
                                <a href="<?= $this->Url->build('/')?>profile" class="block px-4 py-2">My Profile</a>
                            </li>
                            <li>
                                <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">My Bookings</a>
                            </li>
                            <li>
                                <a href="<?= $this->Url->build('/')?>profile/bookmarks" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">My Bookmarks</a>
                            </li>
                            <li>
                                <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">My Reviews</a>
                            </li>
                            <?php
                            if ($customerData): ?>
                                <li>
                                    <?= $this->Form->postLink('Logout', '/logout', ['class' => 'block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white logoutbtn']) ?>
                                    <!-- <a href="<?= $this->Url->build('/')?>admin/login" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white logoutbtn">Logout</a> -->
                                </li>
                            <?php endif; ?>

                        </ul>
                         <?php endif; ?>
                        <?php if (!$customerData): ?>
                        <p class="account-title px-4 py-2">Center & Teacher</p>
                        <ul class="text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                            <li>
                                <a href="<?= $this->Url->build('/')?>login/customer-login" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Login</a>
                            </li>
                            <li>
                                <a href="<?= $this->Url->build('/') . h($this->request->getParam('lang')) ?>/partners/partner-with-us" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Partner with us</a>
                            </li>
                        </ul>
                         <?php endif; ?>
                    </div>
                </div>
                <!-- <div class="relative group login" x-cloak>
                    <a class="px-2 py-2 rounded-half dropdown" type="button"><span class="init-letter">K</span> Karthikeyan</a>

                    
                    <div class="z-10 absolute bg-white dropdown-submenu mt-2 z-1 w-57 bg-white shadow-lg rounded-lg">
                        <ul class="text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                            <li>
                                <a href="#" class="block px-4 py-2">My Profile</a>
                            </li>
                            <li>
                                <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">My Bookings</a>
                            </li>
                            <li>
                                <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">My Bookmarks</a>
                            </li>
                            <li>
                                <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">My Reviews</a>
                            </li>
                            <li>
                                <button type="submit" class="block px-4 py-2 text-logout">Logout</button>
                            </li>
                        </ul>
                        <p class="account-title px-4 py-2">Center & Teacher Login</p>
                        <ul class="text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                            <li>
                                <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Login</a>
                            </li>
                            <li>
                                <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Partner with us</a>
                            </li>
                        </ul> 
                    </div>
                </div> -->
            </li>
        </ul>
    </div>
    <div x-data="{ 
        mobileOpen: false,
        init() {
            this.$watch('mobileOpen', value => {
                document.body.classList.toggle('overflow-hidden', value);
            });
        }
    }">
        <div class="mobile-menu" @click="mobileOpen = !mobileOpen">
            <span><i class="fas fa-bars"></i></span>
        </div>
        <!-- Overlay -->
        <div class="fixed overlay"
            x-show="mobileOpen"
            x-transition.opacity
            x-cloak
            @click="mobileOpen = false">
        </div>
        <!-- <div class="mobile-menus" x-show="mobileOpen" [x-cloak]> -->
        <div class="mobile-menus-container" x-data="{ openMenu: null }" x-show="mobileOpen"
            x-transition:enter="transition transform duration-300" x-transition:enter-start="-translate-x-full"
            x-transition:enter-end="translate-x-0" x-transition:leave="transition transform duration-300"
            x-transition:leave-start="translate-x-0" x-transition:leave-end="-translate-x-full" x-cloak
            @click.outside="openMenu = null">
            <div class="mobile-menus">

                <ul class="block gap-[2vw]" id="mobile-menu-id">
                    <li class="group">
                        <div class="relative" x-data="{ openMenu: null }" x-cloak>
                            <a @click="openMenu = (openMenu === 1 ? null : 1)"
                                class="text-black py-2 dropdown active:text-black focus:text-black">
                                Courses
                            </a>
                            <div x-show="openMenu === 1" class=" mt-2 z-1 w-auto sub-menu-items rounded-lg right-0 ">
                                <a href="<?=  $this->Url->build([
                                        'lang' => $this->request->getParam('lang'),
                                        'controller' => 'Courses',
                                        'action' => 'index',
                                        'country' => $this->request->getParam('country') ?? 'india'
                                ], ['fullBase' => true]) ?>"
                                    class="block px-4 py-2 hover:bg-gray-200"> All Courses</a>
                                <?php if (!empty($courseTypes)):
                                    foreach ($courseTypes as $type):
                                ?>
                                    <a href="<?= $this->Url->build([
                                        'lang'       => $this->request->getParam('lang'),
                                        'controller' => 'Courses',
                                        'action'     => 'index',
                                        'country'    => $this->request->getParam('country') ?? 'india',
                                        'type'       =>  urlencode($type->slug)
                                    ]) ?>" class="block px-4 py-2 hover:bg-gray-200"><?= $type->name ?></a>
                                <?php
                                    endforeach;
                                endif; ?>
                            </div>
                        </div>

                        <!-- <a class="hover:text-gray-500" href="#">Courses & Classes</a> -->
                    </li>
                    <li class="group">
                        <div class="relative" x-data="{ openMenu: null }" x-cloak>
                            <a @click="openMenu = (openMenu === 2 ? null : 2)"
                                class="text-black py-2 dropdown active:text-black focus:text-black">
                                Classes
                            </a>
                            <div x-show="openMenu === 2"
                                class=" mt-2 z-1 w-48 sub-menu-items rounded-lg right-0 dropdown-submenu">
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Ahmedabad</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Bangalore</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Delhi</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Chennai</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Hyderabad</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Kolkata</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Mumbai</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Other Locations</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Online Classes</a>
                            </div>
                        </div>

                        <!-- <a class="hover:text-gray-500" href="#">Courses & Classes</a> -->
                    </li>
                    <li class="group">
                        <div class="relative" x-data="{ openMenu: null }" x-cloak>
                            <a @click="openMenu = (openMenu === 3 ? null : 3)"
                                class="text-black py-2 dropdown active:text-black focus:text-black">
                                Centers & Ashrams
                            </a>

                            <div x-show="openMenu === 3"
                                class="relative mt-2 z-1 w-48 sub-menu-items rounded-lg right-0 dropdown-submenu">
                                <a href="<?= $this->Url->build([
                                                'lang' => $this->request->getParam('lang'),
                                                'controller' => 'Partners',
                                                'action' => 'index',
                                                'country' => $this->request->getParam('country') ?? 'india'
                                            ]) ?>"
                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-200"> All Centers</a>
                                <a href="<?= $this->Url->build('/') . h($this->request->getParam('lang')) ?>/yoga-centers/india/south-india/Goa"
                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-200"> Goa</a>
                                <a href="<?= $this->Url->build('/') . h($this->request->getParam('lang')) ?>/yoga-centers/india/south-india/Karnataka/Mysuru (Mysore)"
                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-200"> Mysore</a>
                                <a href="<?= $this->Url->build('/') . h($this->request->getParam('lang')) ?>/yoga-centers/india/south-india/Kerala"
                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-200"> Kerala</a>
                                <a href="<?= $this->Url->build('/') . h($this->request->getParam('lang')) ?>/yoga-centers/india/north-india/Uttrakhand/Rishikesh"
                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-200"> Rishikesh</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Other Locations</a>
                            </div>
                        </div>
                        <!-- <a class="hover:text-gray-500" href="#">Centers & Teachers</a> -->
                    </li>
                    <li class="group">
                        <div class="relative" x-data="{ openMenu: null }" x-cloak>
                            <a @click="openMenu = (openMenu === 4 ? null : 4)"
                                class="text-black py-2 dropdown active:text-black focus:text-black">
                                <!-- Discover -->Teachers
                            </a>

                            <div x-show="openMenu === 4"
                                class="relative mt-2 z-1 w-48 sub-menu-items rounded-lg right-0 dropdown-submenu">
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Ahmedabad</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Bangalore</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Delhi</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Chennai</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Hyderabad</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Kolkata</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Mumbai</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Other Locations</a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Online Teachers</a>
                            </div>
                        </div>
                        <!-- <a class="hover:text-gray-500" href="#">Discover</a> -->
                    </li>
                    <li class="group">
                        <div class="relative" x-data="{ openMenu: null }" x-cloak>
                            <a @click="openMenu = (openMenu === 5 ? null : 5)"
                                class="text-black py-2 dropdown active:text-black focus:text-black">
                                <!-- Discover -->Yoga Info
                            </a>
                            <div x-show="openMenu === 5"
                                class="relative mt-2 z-1 w-48 sub-menu-items rounded-lg right-0 dropdown-submenu">
                                <a href="<?= h($configSettings['WP_ASANAS_LIBRARY']) ?>"
                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Asanas Library</a>
                                <a href="<?= h($configSettings['WP_DISCOVER_URL']) ?>" target="_blank"
                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Discover Yoga</a>
                                <a href="<?= h($configSettings['WP_DESTINATIONS']) ?>"
                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Destinations</a>
                                <a href="<?= h($configSettings['WP_NEWS_EVENTS']) ?>" target="_blank"
                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-200">News & Events</a>
                                <a href="<?= h($configSettings['WP_BLOGS_URL']) ?>" target="_blank"
                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-200">Blog</a>
                            </div>
                        </div>
                        <!-- <a class="hover:text-gray-500" href="#">Discover</a> -->
                    </li>
                    <!-- <li class="partner-list mb-5">
                        <?= $this->Html->link(
                            'Partners',
                            ['controller' => 'Partners', 'action' => 'partnerWithUs', 'prefix' => false],
                            ['class' => 'bg-[#D87A61] text-white px-4 py-2 rounded-half', 'escape' => false]
                        ) ?>
                    </li> -->
                    <li class="partner-list mb-5">
                        <div class="relative" x-data="{ openMenu: null }" x-cloak>
                            <?php if ($customerData): ?>
                                <a @click="openMenu = (openMenu === 6 ? null : 6)" class="bg-[#D87A61] dropdown text-white px-4 py-2 rounded-half">
                                    <span class="init-letter"><?= substr($customerData->first_name, 0, 1) ?></span>
                                    <span class="customer-name" title="<?= h($customerData->first_name . ' ' . $customerData->last_name) ?>"><?= h($customerData->first_name . ' ' . $customerData->last_name) ?></span>
                                </a>
                                <?php else: ?>
                                    <a @click="openMenu = (openMenu === 6 ? null : 6)" class="bg-[#D87A61] dropdown text-white px-4 py-2 rounded-half">My Account</a>
                                <?php endif; ?>


                                <!-- Dropdown menu -->
                                <div x-show="openMenu === 6" class="relative mt-2 z-1 w-57 dropdown-submenu z-10 bg-white sub-menu-items">
                                    <p class="account-title px-4">Student</p>
                                    <ul class="py-2 text-sm text-gray-700 dark:text-gray-200 loginclick" aria-labelledby="dropdownDefaultButton">
                                        <li>
                                            <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">My Profile</a>
                                        </li>
                                        <li>
                                            <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">My Bookings</a>
                                        </li>
                                        <li>
                                            <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">My Bookmarks</a>
                                        </li>
                                        <li>
                                            <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">My Reviews</a>
                                        </li>
                                        <?php
                                        if ($customerData): ?>
                                            <li>
                                                <?= $this->Form->postLink('Logout', '/logout', ['class' => 'block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white logoutbtn']) ?>
                                                <!-- <button type="submit" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white logoutbtn">Logout</button> -->
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                     <?php if (!$customerData): ?>
                                    <p class="account-title px-4">Center & Teacher</p>
                                    <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                                        <li>
                                            <a href="<?= $this->Url->build('/')?>admin/login" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Login</a>
                                        </li>
                                        <li>
                                            <a href="<?= $this->Url->build('/') . h($this->request->getParam('lang')) ?>/partners/partner-with-us" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Partner with us</a>
                                        </li>
                                    </ul>
                                    <?php endif; ?>
                                </div>
                        </div>
                        <!-- <div class="relative login" x-data="{ openMenu: null }" x-cloak>
                            <a @click="openMenu = (openMenu === 6 ? null : 6)" class="bg-[#D87A61] dropdown text-white px-4 py-2 rounded-half"><span class="init-letter">K</span> Karthikeyan</a>
                            <div x-show="openMenu === 6" class="relative mt-2 z-1 w-48 dropdown-submenu z-10 bg-white sub-menu-items">
                                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                                    <li>
                                        <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">My Profile</a>
                                    </li>
                                    <li>
                                        <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">My Bookings</a>
                                    </li>
                                    <li>
                                        <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">My Bookmarks</a>
                                    </li>
                                    <li>
                                        <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">My Reviews</a>
                                    </li>
                                    <li>
                                        <button type="submit" class="block px-4 py-2 text-logout">Logout</button>
                                    </li>
                                </ul>
                            </div>
                        </div> -->
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <!-- <div class="relative bottom-[6px] partner">
        <button class="bg-[#D87A61] text-white px-4 py-2 rounded-half">Partners</button>
    </div> -->
</nav>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var isCustomerLoggedIn = <?= json_encode($isCustomerLoggedIn) ?>;
        const accountLinks = document.querySelectorAll('.loginclick a');
        accountLinks.forEach(link => {
            link.addEventListener('click', function(event) {
                if (!isCustomerLoggedIn) {
                    event.preventDefault(); // Prevent the default link action
                    window.location.href = '<?= $this->Url->build(['controller' => 'Login', 'action' => 'index']) ?>?redirect=' + '<?= $this->request->getRequestTarget() ?>'; // Redirect to login page
                }
            });
        });

         // Handle logout buttons
        const logoutButtons = document.querySelectorAll('.logoutbtn');
        logoutButtons.forEach(button => {
            button.addEventListener('click', function(event) {
                event.preventDefault();
                window.location.href = '<?= $this->Url->build(['controller' => 'Login', 'action' => 'logout']) ?>';
            });
        });
    });
</script>