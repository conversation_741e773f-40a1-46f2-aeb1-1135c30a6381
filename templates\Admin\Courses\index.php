<?php

/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Course> $courses
 */
?>
<?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')) ?>
<style>
    th.no-sort::after,
    th.no-sort::before {
        display: none !important;
    }

    /* Optional: prevent pointer cursor & sort highlight on hover */
    th.no-sort {
        cursor: default !important;
        background-image: none !important;
    }
    td a {
        color: black;
    }
</style>
<?php $this->append('style'); ?>
<?php $this->end(); ?>
<div class="main-content">
    <section class="section">
        <div class="section-body1">
            <div class="container-fluid">
                <?php
                $successMessage = $this->Flash->render('success');
                $errorMessage = $this->Flash->render('error');
                ?>
                <?php if (!empty($successMessage)): ?>
                    <?= $successMessage ?>
                <?php elseif (!empty($errorMessage)): ?>
                    <?= $errorMessage ?>
                <?php endif; ?>
            </div>
        </div>
        <div class="section-body" id="list">
            <div class="container-fluid">
                <div class="card card-primary">
                    <ul class="breadcrumb breadcrumb-style ">
                        <li class="breadcrumb-item">Partner Offerings</li>
                        <li class="breadcrumb-item">Partner Offerings List</li>
                    </ul>
                    <div class="card-header">
                        <div class="d-block d-sm-flex actions">
                            <div class="action-header">
                                <h4>Partner Offerings List</h4>
                            </div>
                            <div class="filter-options">
                                <!-- <div class="search-content">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="customSearchBox" placeholder="Search">
                                        <button class="btn btn-outline-secondary" type="button" id="customSearchBtn">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div> -->
                                <div class="action-button">
                                    <button onclick="window.location.href='<?= $this->Url->build(['controller' => 'Courses', 'action' => 'add']) ?>'">
                                        <i class="fas fa-plus"></i> <?= __("Add Offering") ?>
                                    </button>
                                </div>
                                <div class="filter-button">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-primary filter-toggle">
                                            <i class="fas fa-filter"></i> Filter
                                        </button>
                                    </div>
                                </div>
                                <div class="download-icon">
                                    <button class="btn btn-primary" id="downloadUsers"><i class="fas fa-file-download"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class='filter-action row'>
                        <div class='col-12 col-sm-3'>
                            <select id='filterStatus' class='form-control form-select'>
                                <option value=""><?= __("Filter By Status") ?></option>
                                <option value="A"><?= __("Active") ?></option>
                                <option value="I"><?= __("Inactive") ?></option>
                                <option value="D"><?= __("Deleted") ?></option>
                            </select>
                        </div>
                        <div class='col-12 col-sm-2 d-flex'>
                            <button type='submit' id='filter' class='btn btn-primary me-2'>
                                <i class='fas fa-filter'></i>
                            </button>
                            <button type="reset" class="btn btn-primary" onclick="resetFilters()">
                                <i class="fas fa-redo-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped dataTable no-footer display nowrap table-hover border" id="course-table">
                                <thead>
                                    <tr>
                                        <th class="no-sort"><?= __("Actions") ?></th>
                                        <th><?= __("Id") ?></th>
                                        <th><?= __("Name") ?></th>
                                        <th><?= __("Course Type") ?></th>
                                        <th><?= __("Offered By") ?></th>
                                        <th><?= __("Address") ?></th>
                                        <th id="status"><?= __("Status") ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                  
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Vertically Center Modal -->
    <div class="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">Delete Confirmation</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p class="icon"><i class="fas fa-exclamation-triangle danger"></i></p>
                    <p>Are you sure you want to delete this course?</p>
                </div>
                <div class="modal-footer br">
                    <button type="button" class="btn btn-primary confirm-delete" id="confirmDeleteBtn">Delete</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<script>
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    const viewBaseUrl = "<?= $this->Url->build(['controller' => 'Courses', 'action' => 'view', '_full' => false]) ?>";
    const editBaseUrl = "<?= $this->Url->build(['controller' => 'Courses', 'action' => 'edit', '_full' => false]) ?>";
    const approveBaseUrl = "<?= $this->Url->build(['controller' => 'Courses', 'action' => 'approve', '_full' => false]) ?>";
    
      // Restore saved page (if any)
    let savedPage = sessionStorage.getItem('courses_page');

    var table = $("#course-table").DataTable({
        processing: true,
        serverSide: true,
        order: [],
        ajax: {
            url: '<?= $this->Url->build(['controller' => 'courses', 'action' => 'index']) ?>', // Adjust the URL
            headers: {
                'X-CSRF-Token': $('meta[name="csrfToken"]').attr('content')
            },
            type: 'POST',
            data: function (d) {
                d.status = $('#filterStatus').val();
            }
        },
        displayStart: savedPage * 10,
        // initComplete: function () {
        //     if (savedPage !== null) {
        //         table.page(parseInt(savedPage)).draw('page'); // restore page
        //         sessionStorage.removeItem('courses_page');
        //     }
        // },
        columnDefs: [
            { orderable: false, targets: 0 }, // Make the first column non-sortable
        ],
        "columns": [
            {
               "data": "id",
                "render": function(data, type, row) {
                    const viewLink = `<a href="${viewBaseUrl}/${data}" title="View"><i class="far fa-eye m-r-10"></i></a>`;
                    const editLink = `<a href="${editBaseUrl}/${data}" title="Edit" class="btn-edit"><i class="fas fa-edit m-r-10"></i></a>`;
                    const deleteBtn = `<a href="javascript:void(0);" class="delete-button" data-id="${data}" title="Delete" data-bs-toggle="modal" data-bs-target="#exampleModalCenter"><i class="far fa-trash-alt"></i></a>`;
                    const approveLink = `<a href="${approveBaseUrl}/${data}" class="btn-sm approve approve ms-2" title="Toggle Status"><i class="fas fa-check"></i></a>`;
                    return viewLink + editLink + deleteBtn + approveLink;
                }
            },
            {
                data: "id"
            },
            {
                data: "name",
                "render": function(data, type, row) {
                    const shortText = data.length > 50 ? data.substring(0, 50) + '…' : data;
                    return `<div data-bs-toggle="tooltip" data-bs-placement="top" title="${data}">${shortText}</div>`;
                }
            },
            {
                data: "course_type"
            },
            {
                data: "offered_by",
            },
            {
                data: "address",
                "render": function(data, type, row) {
                    const shortText = data.length > 50 ? data.substring(0, 50) + '…' : data;
                    return `<div data-bs-toggle="tooltip" data-bs-placement="top" title="${data}">${shortText}</div>`;
                }
            },
            {
                data: "status",
                render: function (status) {
                    if (status === 'A') {
                        return '<span class="badge-outline col-green">Active</span>';
                    } else if (status === 'I') {
                        return '<span class="badge-outline col-red">Inactive</span>';
                    } else {
                        return '<span class="badge-outline col-red">Deleted</span>';
                    }
                }
            }
        ]
    });
sessionStorage.removeItem('courses_page');
    $(document).on('click', '.btn-edit', function (e) {
        e.preventDefault();
        // Save the current page to session storage
        let currentPage = table.page.info().page; // zero-based
       
        sessionStorage.setItem('courses_page', currentPage);
    
        let editURL = $(this).attr('href');
        window.location.href = editURL
    });

    function resetFilters() {
        // Clear all filter inputs
        $('#filterStatus').val('');
        $('#course-table').DataTable().ajax.reload();
    }

    $('#filter').on('click', function(event) {
        event.preventDefault();
        table.ajax.reload();
    });

    let deleteUserId = null;

    $(document).on('click', '.delete-button', function() {
        deleteUserId = $(this).data('id');
    });
    $('#confirmDeleteBtn').on('click', function () {
        if (deleteUserId) {
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'courses', 'action' => 'delete']) ?>/" + deleteUserId,

                type: 'POST',
                headers: {
                    'X-CSRF-Token': $('meta[name="csrfToken"]').attr('content') // CakePHP 4/5 CSRF token
                },
                success: function (res) {
                    if (res.success) {
                        $('#exampleModalCenter').modal('hide');
                        location.reload(); // Refresh to reflect changes
                    } else {
                        alert(res.message || 'Something went wrong');
                    }
                },
                error: function (xhr, status, error) {
                    console.log("AJAX Error:", xhr.responseText);
                }
            });
        }
    });
    // Download User CSV
    $(document).ready(function() {
        $("#downloadUsers").on("click", function() {
            var status = $("#filterStatus").val(); // Get selected filter status

            // Generate the download URL with status filter
            var downloadUrl = '<?= $this->Url->build(['controller' => 'Courses', 'action' => 'exportCourses']) ?>';
            if (status !== "") {
                downloadUrl += "?status=" + encodeURIComponent(status);
            }

            console.log("Downloading CSV with status:", status);
            window.location.href = downloadUrl; // Trigger file download
        });
    });
</script>
<?php $this->end(); ?>








