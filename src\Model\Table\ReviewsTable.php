<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\ORM\TableRegistry;

class ReviewsTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('reviews');
        $this->setPrimaryKey('id');
        $this->addBehavior('Timestamp');

        $this->hasMany('ReviewAnswers', [
            'foreignKey' => 'review_id',
            'dependent' => true,
            'cascadeCallbacks' => true
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('customer_id')
            ->requirePresence('customer_id', 'create')
            ->notEmptyString('customer_id');

        $validator
            ->inList('review_type', ['course', 'teacher', 'center']);

        $validator
            ->decimal('overall_rating')
            ->allowEmptyString('overall_rating');

        $validator
            ->allowEmptyString('comment');

        return $validator;
    }



  
    public static function getReviewTypeOptions(): array
    {
        return [
            'course' => 'Course Review',
            'teacher' => 'Teacher Review',
            'center' => 'Center Review'
        ];
    }

    /**
     * Get status options
     *
     * @return array
     */
    public static function getStatusOptions(): array
    {
        return [
            'Pending' => 'Pending',
            'Approved' => 'Approved',
            'Rejected' => 'Rejected'
        ];
    }

    /**
     * Get rating options
     *
     * @return array
     */
    public static function getRatingOptions(): array
    {
        return [
            1 => '1 Star',
            2 => '2 Stars',
            3 => '3 Stars',
            4 => '4 Stars',
            5 => '5 Stars'
        ];
    }
}
