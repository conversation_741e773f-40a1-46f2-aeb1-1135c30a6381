<?php

declare(strict_types=1);

namespace App;

use Cake\Core\Configure;
use Cake\Core\ContainerInterface;
use Cake\Datasource\FactoryLocator;
use Cake\Error\Middleware\ErrorHandlerMiddleware;
use Cake\Http\BaseApplication;
use Cake\Http\Middleware\BodyParserMiddleware;
use Cake\Http\Middleware\CsrfProtectionMiddleware;
use Cake\Http\MiddlewareQueue;
use Cake\ORM\Locator\TableLocator;
use Cake\Routing\Middleware\AssetMiddleware;
use Cake\Routing\Middleware\RoutingMiddleware;
use Authentication\AuthenticationService;
use Authentication\AuthenticationServiceInterface;
use Authentication\Middleware\AuthenticationMiddleware;
use Cake\Routing\Router;
use Psr\Http\Message\ServerRequestInterface;
use App\Middleware\ApiAuthenticationErrorMiddleware;
use Authentication\AuthenticationServiceProviderInterface;
use App\Middleware\LanguageRedirectMiddleware;

class Application extends BaseApplication implements AuthenticationServiceProviderInterface
{
    public function bootstrap(): void
    {
        // Call parent to load bootstrap from files.
        parent::bootstrap();

        // Load the Authentication plugin
        $this->addPlugin('Authentication');


        if (PHP_SAPI !== 'cli') {
            FactoryLocator::add(
                'Table',
                (new TableLocator())->allowFallbackClass(false)
            );
        }
        Configure::load('settings', 'default', true);
        Configure::load('constants', 'default', true);
    }
    public function middleware(MiddlewareQueue $middlewareQueue): MiddlewareQueue
    {

        $csrf = new CsrfProtectionMiddleware();
        // Token check will be skipped when callback returns `true`.
        $csrf->skipCheckCallback(function ($request) {
            return $request->is('get');
        });

        $middlewareQueue

            ->add(new ErrorHandlerMiddleware(Configure::read('Error'), $this))
            ->add(new AssetMiddleware([
                'cacheTime' => Configure::read('Asset.cacheTime'),
            ]))
            ->add(new RoutingMiddleware($this))
             ->add(new LanguageRedirectMiddleware())
            ->add(new AuthenticationMiddleware($this))
            ->add(new BodyParserMiddleware())
            ->add($csrf);

        return $middlewareQueue;
    }

    public function getAuthenticationService(ServerRequestInterface $request): AuthenticationServiceInterface
    {
        $lang = $this->extractLangFromPath($request);

        $authenticationService = new AuthenticationService([
            'unauthenticatedRedirect' => $this->getLoginRedirectPath($request, $lang),
            'queryParam' => 'redirect',
        ]);

        // Use the same identifier for both: just a different field (email or mobile handled in controller)
        $authenticationService->loadIdentifier('Authentication.Password', [
            'fields' => [
                'username' => 'email', // just a placeholder, we manually handle mobile/email in controller
                'password' => 'password',
            ]
        ]);

        // Load session + form authenticators
        $authenticationService->loadAuthenticator('Authentication.Session');
        $authenticationService->loadAuthenticator('Authentication.Form', [
            'fields' => [
                'username' => 'email',
                'password' => 'password',
            ],
            'loginUrl' => $this->getLoginRedirectPath($request, $lang),
        ]);

        return $authenticationService;
    }

    private function extractLangFromPath(ServerRequestInterface $request): string
    {
        $segments = explode('/', trim($request->getUri()->getPath(), '/'));
        $lang = $segments[0] ?? 'en';

        $supported = ['en', 'fr', 'de', 'es'];
        return in_array($lang, $supported) ? $lang : 'en';
    }

    private function getLoginRedirectPath(ServerRequestInterface $request, string $lang): string
    {
        $path = $request->getUri()->getPath();

        // Adjust based on actual routing patterns
        if (strpos($path, '/admin') === 0) {
            return Router::url(['prefix' => 'Admin', 'controller' => 'Users', 'action' => 'login']);
        }
    
        // For frontend/customer login
        return Router::url([
            // 'lang' => $lang,
            'controller' => 'Login',
            'action' => 'index']
        );
    }
}
