<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=homevilla', 'root', '');
    
    // Add social_provider column
    try {
        $pdo->exec('ALTER TABLE users ADD COLUMN social_provider VARCHAR(255) NULL');
        echo 'Added social_provider column' . PHP_EOL;
    } catch (Exception $e) {
        echo 'Column social_provider might already exist: ' . $e->getMessage() . PHP_EOL;
    }
    
    // Add socialID column
    try {
        $pdo->exec('ALTER TABLE users ADD COLUMN socialID VARCHAR(255) NULL');
        echo 'Added socialID column' . PHP_EOL;
    } catch (Exception $e) {
        echo 'Column socialID might already exist: ' . $e->getMessage() . PHP_EOL;
    }
    
    // Add profile_pic column
    try {
        $pdo->exec('ALTER TABLE users ADD COLUMN profile_pic VARCHAR(255) NULL');
        echo 'Added profile_pic column' . PHP_EOL;
    } catch (Exception $e) {
        echo 'Column profile_pic might already exist: ' . $e->getMessage() . PHP_EOL;
    }
    
    echo 'Done!' . PHP_EOL;
} catch (Exception $e) {
    echo 'Database connection error: ' . $e->getMessage() . PHP_EOL;
}
