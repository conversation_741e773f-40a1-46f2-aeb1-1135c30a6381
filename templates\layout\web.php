<?php
/**
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @since         0.10.0
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 * @var \App\View\AppView $this
 */

$cakeDescription = 'Homevilla-Yoga';
?>
<!DOCTYPE html>
<html>
<head>
    <?= $this->Html->charset() ?>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
   <meta name="csrf-token" content="<?= $this->request->getAttribute('csrfToken') ?>">
    <title>
        <?= $this->fetch('title') ?>
    </title>
    <link rel="icon" href="<?= $this->Url->build('/img/yoga-logo.png') ?>">
    <?= $this->fetch('meta') ?>
    
    <script src="https://cdn.tailwindcss.com"></script>
        <?= $this->fetch('meta') ?>
        <?= $this->fetch('css') ?>
        <?= $this->fetch('script') ?>
        <script>
            var baseUrl = '<?= \Cake\Routing\Router::url("/", true); ?>';
            var lang = '<?= $this->request->getParam('lang') ?>';
        </script>
        <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
        <!-- Swiper Styles -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
</head>
<body>
<header x-data="{ mobileOpen: false }" class="bg-white shadow-md px-4 md:px-10 lg:px-24 xl:px-40 2xl:px-60">
  <div class="flex flex-col items-center justify-center py-6">
    <nav class="w-full flex items-center justify-between relative" aria-label="Main Navigation">
      <!-- Logo -->
      <div class="flex items-center">
        <a href="<?= $this->Url->build(['controller' => 'Home', 'action' => 'index']) ?>" aria-label="Home">
          <img src="<?= $this->Url->webroot('img/yoga-logo.png') ?>" class="w-24 h-auto" alt="Yoga.in Logo">
        </a>
      </div>
      <!-- Desktop Navbar -->
      <ul class="hidden md:flex items-center space-x-6" id="mainNavLinks">
        <!-- Courses Dropdown -->
        <li x-data="{ open: false }" class="relative">
          <button @mouseenter="open=true" @mouseleave="open=false" @focus="open=true" @blur="open=false" class="text-black py-2 flex items-center gap-1 focus:outline-none">
            Courses
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
          </button>
          <div x-show="open" @mouseenter="open=true" @mouseleave="open=false" class="absolute left-0 mt-2 z-20 w-48 bg-white shadow-lg rounded-lg" x-transition>
            <a href="<?= $this->Url->build([
              'lang' => $this->request->getParam('lang'),
              'controller' => 'Courses',
              'action' => 'index',
              'country' => $this->request->getParam('country') ?? 'india'
            ]) ?>" class="block px-4 py-2 hover:bg-gray-100">All Courses</a>
            <?php if (!empty($courseTypes)) {
              foreach ($courseTypes as $type) { ?>
                <a href="<?= $this->Url->build([
                  'lang' => $this->request->getParam('lang'),
                  'controller' => 'Courses',
                  'action' => 'index',
                  'country' => $this->request->getParam('country') ?? 'india',
                  'type' => urlencode($type->slug)
                ]) ?>" class="block px-4 py-2 hover:bg-gray-100 text-gray-700"><?= $type->name ?></a>
            <?php } } ?>
          </div>
        </li>
        <!-- Classes Dropdown -->
        <li x-data="{ open: false }" class="relative">
          <button @mouseenter="open=true" @mouseleave="open=false" @focus="open=true" @blur="open=false" class="text-black py-2 flex items-center gap-1 focus:outline-none">
            Classes
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
          </button>
          <div x-show="open" @mouseenter="open=true" @mouseleave="open=false" class="absolute left-0 mt-2 z-20 w-48 bg-white shadow-lg rounded-lg" x-transition>
            <?php if (!empty($yogaStyles)) {
              foreach ($yogaStyles as $style) { ?>
                <a href="<?= $this->Url->build(['controller' => 'Courses', 'action' => 'index']) ?>?type=<?= urlencode($style) ?>" class="block px-4 py-2 hover:bg-gray-100 text-gray-700"><?= $style ?></a>
            <?php } } ?>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Ahmedabad</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Bangalore</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Delhi</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Chennai</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Hyderabad</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Kolkata</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Mumbai</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Other Locations</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Online Classes</a>
          </div>
        </li>
        <!-- Centers & Ashrams Dropdown -->
        <li x-data="{ open: false }" class="relative">
          <button @mouseenter="open=true" @mouseleave="open=false" @focus="open=true" @blur="open=false" class="text-black py-2 flex items-center gap-1 focus:outline-none">
            Centers & Ashrams
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
          </button>
          <div x-show="open" @mouseenter="open=true" @mouseleave="open=false" class="absolute left-0 mt-2 z-20 w-48 bg-white shadow-lg rounded-lg" x-transition>
            <a href="<?= $this->Url->build([
              'lang' => $this->request->getParam('lang'),
              'controller' => 'Partners',
              'action' => 'index',
              'country' => $this->request->getParam('country') ?? 'india'
            ]) ?>" class="block px-4 py-2 hover:bg-gray-100">All Centers</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Goa</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Mysore</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Kerala</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Rishikesh</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Other Locations</a>
          </div>
        </li>
        <!-- Teachers Dropdown -->
        <li x-data="{ open: false }" class="relative">
          <button @mouseenter="open=true" @mouseleave="open=false" @focus="open=true" @blur="open=false" class="text-black py-2 flex items-center gap-1 focus:outline-none">
            Teachers
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
          </button>
          <div x-show="open" @mouseenter="open=true" @mouseleave="open=false" class="absolute left-0 mt-2 z-20 w-48 bg-white shadow-lg rounded-lg" x-transition>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Ahmedabad</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Bangalore</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Delhi</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Chennai</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Hyderabad</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Kolkata</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Mumbai</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Other Locations</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Online Teachers</a>
          </div>
        </li>
        <!-- Yoga Info Dropdown -->
        <li x-data="{ open: false }" class="relative">
          <button @mouseenter="open=true" @mouseleave="open=false" @focus="open=true" @blur="open=false" class="text-black py-2 flex items-center gap-1 focus:outline-none">
            Yoga Info
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
          </button>
          <div x-show="open" @mouseenter="open=true" @mouseleave="open=false" class="absolute left-0 mt-2 z-20 w-48 bg-white shadow-lg rounded-lg" x-transition>
            <a href="<?= h($configSettings['WP_ASANAS_LIBRARY']) ?>" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Asanas Library</a>
            <a href="<?= h($configSettings['WP_DISCOVER_URL']) ?>" target="_blank" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Discover Yoga</a>
            <a href="<?= h($configSettings['WP_DESTINATIONS']) ?>" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Destinations</a>
            <a href="<?= h($configSettings['WP_NEWS_EVENTS']) ?>" target="_blank" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">News & Events</a>
            <a href="<?= h($configSettings['WP_BLOGS_URL']) ?>" target="_blank" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Blog</a>
          </div>
        </li>
        <!-- My Account Dropdown -->
        <li x-data="{ open: false }" class="relative">
          <button @mouseenter="open=true" @mouseleave="open=false" @focus="open=true" @blur="open=false" class="text-black py-2 flex items-center gap-1 focus:outline-none">
            My Account
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
          </button>
          <div x-show="open" @mouseenter="open=true" @mouseleave="open=false" class="absolute left-0 mt-2 z-20 w-56 bg-white shadow-lg rounded-lg" x-transition>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">My Profile</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">My Bookings</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">My Bookmarks</a>
            <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">My Reviews</a>
            <?php if ($isCustomerLoggedIn): ?>
              <a href="#" class="block px-4 py-2 hover:bg-gray-100 text-gray-700 logoutbtn">Logout</a>
            <?php else: ?>
              <div class="border-t border-gray-200 my-2"></div>
              <span class="block px-4 py-2 text-xs text-gray-500">Center & Teacher</span>
              <a href="<?= $this->Url->build('/')?>admin/login" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Login</a>
              <a href="<?= $this->Url->build('/') . h($this->request->getParam('lang')) ?>/partners/partner-with-us" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">Partner with us</a>
            <?php endif; ?>
          </div>
        </li>
        <!-- Language Dropdown -->
        <li class="relative">
          <div x-data="{ open: false }" class="inline-block">
            <button @click="open = !open" class="flex items-center px-3 py-2 bg-white border border-gray-300 rounded shadow hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              <img src="<?= $this->Url->webroot('img/flags/gb.svg') ?>" alt="English" class="w-5 h-5 mr-2"> EN
              <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
            </button>
            <div x-show="open" @click.away="open=false" class="absolute left-0 z-10 mt-2 w-40 bg-white border border-gray-200 rounded shadow-lg" x-transition>
              <button type="button" class="flex items-center w-full px-3 py-2 hover:bg-gray-100" @click="open=false"><img src="<?= $this->Url->webroot('img/flags/gb.svg') ?>" alt="English" class="w-5 h-5 mr-2"> English</button>
              <button type="button" class="flex items-center w-full px-3 py-2 hover:bg-gray-100" @click="open=false"><img src="<?= $this->Url->webroot('img/flags/in.svg') ?>" alt="Hindi" class="w-5 h-5 mr-2"> हिंदी</button>
              <button type="button" class="flex items-center w-full px-3 py-2 hover:bg-gray-100" @click="open=false"><img src="<?= $this->Url->webroot('img/flags/fr.svg') ?>" alt="French" class="w-5 h-5 mr-2"> Français</button>
              <button type="button" class="flex items-center w-full px-3 py-2 hover:bg-gray-100" @click="open=false"><img src="<?= $this->Url->webroot('img/flags/de.svg') ?>" alt="German" class="w-5 h-5 mr-2"> Deutsch</button>
              <button type="button" class="flex items-center w-full px-3 py-2 hover:bg-gray-100" @click="open=false"><img src="<?= $this->Url->webroot('img/flags/sp.svg') ?>" alt="Spanish" class="w-5 h-5 mr-2"> Español</button>
            </div>
          </div>
        </li>
      </ul>
      <!-- Mobile menu button -->
      <div class="md:hidden flex items-center ml-2">
        <button @click="mobileOpen = !mobileOpen" type="button" class="text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 p-2 rounded bg-white shadow-md" aria-label="Open menu">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" /></svg>
        </button>
      </div>
    </nav>
    <!-- Mobile menu -->
    <nav x-show="mobileOpen" x-transition class="md:hidden fixed inset-0 bg-black bg-opacity-40 z-50">
      <div class="absolute left-0 right-0 top-24 mx-auto w-[90vw] max-w-md rounded-lg shadow-2xl bg-white py-6 px-6">
        <ul class="flex flex-col space-y-2">
          <!-- Repeat nav items for mobile, each with AlpineJS dropdown logic -->
          <li x-data="{ open: false }" class="relative">
            <button @click="open = !open" class="w-full text-left text-black py-2 flex items-center gap-1 focus:outline-none">
              Courses
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
            </button>
            <div x-show="open" x-transition class="mt-2 w-full bg-white border border-gray-200 rounded shadow-lg">
              <a href="<?= $this->Url->build([
                'lang' => $this->request->getParam('lang'),
                'controller' => 'Courses',
                'action' => 'index',
                'country' => $this->request->getParam('country') ?? 'india'
              ]) ?>" class="block px-4 py-2 hover:bg-gray-100">All Courses</a>
              <?php if (!empty($courseTypes)):
                foreach ($courseTypes as $type): ?>
                  <a href="<?= $this->Url->build([
                    'lang' => $this->request->getParam('lang'),
                    'controller' => 'Courses',
                    'action' => 'index',
                    'country' => $this->request->getParam('country') ?? 'india',
                    'type' => urlencode($type->slug)
                  ]) ?>" class="block px-4 py-2 hover:bg-gray-100 text-gray-700"><?= $type->name ?></a>
              <?php endforeach; endif; ?>
            </div>
          </li>
          <!-- ...repeat for other nav items... -->
          <!-- Language Dropdown -->
          <li class="relative">
            <div x-data="{ open: false }" class="inline-block w-full">
              <button @click="open = !open" class="flex items-center w-full px-3 py-2 bg-white border border-gray-300 rounded shadow hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                <img src="<?= $this->Url->webroot('img/flags/gb.svg') ?>" alt="English" class="w-5 h-5 mr-2"> EN
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
              </button>
              <div x-show="open" @click.away="open=false" class="absolute left-0 z-10 mt-2 w-full bg-white border border-gray-200 rounded shadow-lg" x-transition>
                <button type="button" class="flex items-center w-full px-3 py-2 hover:bg-gray-100" @click="open=false"><img src="<?= $this->Url->webroot('img/flags/gb.svg') ?>" alt="English" class="w-5 h-5 mr-2"> English</button>
                <button type="button" class="flex items-center w-full px-3 py-2 hover:bg-gray-100" @click="open=false"><img src="<?= $this->Url->webroot('img/flags/in.svg') ?>" alt="Hindi" class="w-5 h-5 mr-2"> हिंदी</button>
                <button type="button" class="flex items-center w-full px-3 py-2 hover:bg-gray-100" @click="open=false"><img src="<?= $this->Url->webroot('img/flags/fr.svg') ?>" alt="French" class="w-5 h-5 mr-2"> Français</button>
                <button type="button" class="flex items-center w-full px-3 py-2 hover:bg-gray-100" @click="open=false"><img src="<?= $this->Url->webroot('img/flags/de.svg') ?>" alt="German" class="w-5 h-5 mr-2"> Deutsch</button>
                <button type="button" class="flex items-center w-full px-3 py-2 hover:bg-gray-100" @click="open=false"><img src="<?= $this->Url->webroot('img/flags/sp.svg') ?>" alt="Spanish" class="w-5 h-5 mr-2"> Español</button>
              </div>
            </div>
          </li>
        </ul>
        <!-- Floating Chat Button -->
        <button id="chatButton" aria-label="Chat" class="fixed bottom-6 right-6 z-50 bg-indigo-600 hover:bg-indigo-700 text-white rounded-full shadow-lg p-3 flex items-center justify-center transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500">
          <img src="<?= $this->Url->webroot('img/chat-icon.png') ?>" alt="Chat" class="w-7 h-7" />
        </button>
      </div>
    </nav>
  </div>
</header>

        <div class="header-div w-full flex flex-col lg:flex-row items-center justify-between gap-8">
            <div class="header-content flex flex-col items-center lg:items-start text-center lg:text-left">
                <img src="<?= $this->Url->webroot('img/leaf.png') ?>" alt="Leaf Image" class="yoga-leaf hidden lg:block mb-2" />
                <img src="<?= $this->Url->webroot('img/mobile-leaf.png') ?>" alt="Leaf Image" class="yoga-leaf block lg:hidden mb-2" />
                <h1 class="text-3xl md:text-4xl font-bold text-gray-800 mb-2">Find your Yoga in India</h1>
                <p class="text-lg text-gray-600">Yoga Courses, Classes, Centers, Teachers anywhere in India</p>
            </div>
            <div class="header-img flex justify-center items-center">
                <img src="<?= $this->Url->webroot('img/yoga.png') ?>" class="img w-48 md:w-64 lg:w-80" alt="yoga image">
            </div>
        </div>
    </header>
</div>
    <?= $this->fetch('content') ?>
    
<footer class="bg-gray-100 px-4 py-8 md:px-10 lg:px-24 xl:px-40 2xl:px-60 mt-12 border-t border-gray-200 relative">
    <div class="mx-auto max-w-7xl">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-16 2xl:gap-24">
            <nav aria-label="Footer Navigation" class="mb-6 md:mb-0">
                <h2 class="text-lg font-semibold mb-4 text-gray-700">Go To</h2>
                <ul class="space-y-2">
                    <li><a href="<?=  $this->Url->build([
                            'lang' => $this->request->getParam('lang'),
                            'controller' => 'Courses',
                            'action' => 'index',
                            'country' => $this->request->getParam('country') ?? 'india'
                        ]) ?>" class="text-gray-600 hover:text-indigo-600 transition">Courses</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-indigo-600 transition">Classes</a></li>
                    <li><a href="<?=  $this->Url->build([
                            'lang' => $this->request->getParam('lang'),
                            'controller' => 'Partners',
                            'action' => 'index',
                            'country' => $this->request->getParam('country') ?? 'india'
                        ]) ?>" class="text-gray-600 hover:text-indigo-600 transition">Centers & Ashrams</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-indigo-600 transition">Teachers</a></li>
                    <li><a href="<?= h($configSettings['WP_DISCOVER_URL']) ?>" target="_blank" class="text-gray-600 hover:text-indigo-600 transition">Discover</a></li>
                    <li><a href="<?= h($configSettings['WP_NEWS_EVENTS']) ?>" target="_blank" class="text-gray-600 hover:text-indigo-600 transition">News & Events</a></li>
                    <li><a href="<?= h($configSettings['WP_BLOGS_URL']) ?>" target="_blank" class="text-gray-600 hover:text-indigo-600 transition">Blog</a></li>
                </ul>
            </nav>
            <div class="mb-6 md:mb-0">
                <h2 class="text-lg font-semibold mb-4 text-gray-700">Quick Links</h2>
                <ul class="space-y-2">
                    <li><a href="#" class="text-gray-600 hover:text-indigo-600 transition">Yoga in Goa</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-indigo-600 transition">Yoga in Kerala</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-indigo-600 transition">Yoga in Mysore</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-indigo-600 transition">Yoga in Rishikesh</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-indigo-600 transition">Yoga teacher training in India</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-indigo-600 transition">Yoga retreats in India</a></li>
                </ul>
            </div>
            <div>
                <h2 class="text-lg font-semibold mb-4 text-gray-700">Contact Us</h2>
                <ul class="space-y-2">
                    <li><a href="<?= $this->Url->build(['controller' => 'Partners', 'action' => 'PartnerWithUs']) ?>" class="text-gray-600 hover:text-indigo-600 transition"><span>Partner with us</span></a></li>
                    <li><a href="tel:+91-8655-420-932" class="flex items-center text-gray-600 hover:text-indigo-600 transition"><img src="<?= $this->Url->webroot('img/phone.png') ?>" alt="phone-icon" class="mr-3 w-5 h-5"> <span>+91-8655-420-932</span></a></li>
                    <li><a href="mailto:<EMAIL>" class="flex items-center text-gray-600 hover:text-indigo-600 transition"><img src="<?= $this->Url->webroot('img/email.png') ?>" alt="email-icon" class="mr-3 w-5 h-5"> <span><EMAIL></span></a></li>
                    <li class="flex items-center space-x-3 mt-4">
                        <a href="<?= h($configSettings['FACEBOOK_URL_SOCIAL']) ?>" target="_blank" aria-label="Facebook"><img src="<?= $this->Url->webroot('img/square-facebook-brands.svg') ?>" alt="facebook-logo" class="w-6 h-6"/></a>
                        <a href="<?= h($configSettings['INSTAGRAM_URL_SOCIAL']) ?>" target="_blank" aria-label="Instagram"><img src="<?= $this->Url->webroot('img/square-instagram-brands.svg') ?>" alt="instagram-logo" class="w-6 h-6"/></a>
                        <a href="<?= h($configSettings['YOUTUBE_URL_SOCIAL']) ?>" target="_blank" aria-label="YouTube"><img src="<?= $this->Url->webroot('img/video.png') ?>" alt="youtube-logo" class="w-6 h-6"/></a>
                        <a href="<?= h($configSettings['TWITTER_URL_SOCIAL']) ?>" target="_blank" aria-label="Twitter"><img src="<?= $this->Url->webroot('img/twitter.png') ?>" alt="twitter-logo" class="w-6 h-6"/></a>
                        <a href="<?= h($configSettings['LINKEDIN_URL_SOCIAL']) ?>" target="_blank" aria-label="LinkedIn"><img src="<?= $this->Url->webroot('img/linkedin.png') ?>" alt="linkedin-logo" class="w-6 h-6"/></a>
                    </li>
                    <li class="mt-4">
                        <img src="<?= $this->Url->webroot('img/footer-logo.png') ?>" class="w-24 h-auto" alt="Yoga Logo">
                    </li>
                </ul>
            </div>
        </div>
        <div class="flex flex-col md:flex-row items-center justify-between gap-4 mt-8 border-t border-gray-200 pt-4">
            <div class="text-gray-500 text-sm flex flex-col md:flex-row items-center gap-2 md:gap-4">
                <span class="whitespace-nowrap">&copy; <?= date('Y') ?> Yoga.in - All rights reserved</span>
                <div class="flex flex-row items-center gap-2">
                    <a href="<?= h($configSettings['WP_PRIVACY_POLICY']) ?>" target="_blank" class="hover:underline transition-colors duration-200">Privacy Policy</a>
                    <span class="hidden md:inline-block">|</span>
                    <a href="<?= h($configSettings['WP_TERMS_CONDITIONS']) ?>" target="_blank" class="hover:underline transition-colors duration-200">Terms & Conditions</a>
                </div>
            </div>
        </div>
        <!-- Floating Chat Button -->
        <button id="chatButton" aria-label="Chat" class="fixed bottom-6 right-6 z-50 bg-indigo-600 hover:bg-indigo-700 text-white rounded-full shadow-lg p-3 flex items-center justify-center transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            <img src="<?= $this->Url->webroot('img/chat-icon.png') ?>" alt="Chat" class="w-7 h-7" />
        </button>

        <!-- Move Google Translate Widget to Footer -->
        <div class="flex justify-end mt-8">
            <div id="google_translate_element" class="bg-white rounded-lg shadow-lg px-3 py-2 flex items-center gap-2 border border-gray-200 hover:shadow-xl transition-all duration-300 w-auto">
                <span class="text-xs text-gray-600 font-medium mr-2">Translate:</span>
                <!-- Custom Language Dropdown (inside widget for better UX) -->
                <div class="relative inline-block text-left w-40 mb-0">
                    <button id="selectedLanguage" type="button" class="flex items-center w-full justify-between px-3 py-2 bg-white border border-gray-300 rounded shadow hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500" aria-haspopup="listbox" aria-expanded="false">
                        <span id="selectedLangLabel" class="flex items-center">
                            <img src="<?= $this->Url->webroot('img/flags/gb.svg') ?>" alt="English" class="w-5 h-5 mr-2"> EN
                        </span>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    <div id="languageMenu" class="absolute left-0 z-10 mt-2 w-full bg-white border border-gray-200 rounded shadow-lg hidden" role="listbox">
                        <div class="max-h-60 overflow-y-auto">
                            <button type="button" data-lang="en" class="flex items-center w-full px-3 py-2 hover:bg-gray-100 focus:bg-gray-100" role="option"><img src="<?= $this->Url->webroot('img/flags/gb.svg') ?>" alt="English" class="w-5 h-5 mr-2"> English</button>
                            <button type="button" data-lang="hi" class="flex items-center w-full px-3 py-2 hover:bg-gray-100 focus:bg-gray-100" role="option"><img src="<?= $this->Url->webroot('img/flags/in.svg') ?>" alt="Hindi" class="w-5 h-5 mr-2"> हिंदी</button>
                            <button type="button" data-lang="fr" class="flex items-center w-full px-3 py-2 hover:bg-gray-100 focus:bg-gray-100" role="option"><img src="<?= $this->Url->webroot('img/flags/fr.svg') ?>" alt="French" class="w-5 h-5 mr-2"> Français</button>
                            <button type="button" data-lang="de" class="flex items-center w-full px-3 py-2 hover:bg-gray-100 focus:bg-gray-100" role="option"><img src="<?= $this->Url->webroot('img/flags/de.svg') ?>" alt="German" class="w-5 h-5 mr-2"> Deutsch</button>
                            <button type="button" data-lang="es" class="flex items-center w-full px-3 py-2 hover:bg-gray-100 focus:bg-gray-100" role="option"><img src="<?= $this->Url->webroot('img/flags/sp.svg') ?>" alt="Spanish" class="w-5 h-5 mr-2"> Español</button>
                            <!-- Add more languages as needed -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
<script>
    // Responsive Navbar Toggle
    document.addEventListener('DOMContentLoaded', function() {
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileNavMenu = document.getElementById('mobileNavMenu');
        // Position menu below icon and add spacing
        function openMobileMenu() {
            mobileNavMenu.classList.remove('hidden');
            mobileMenuBtn.classList.add('z-50');
        }
        function closeMobileMenu() {
            mobileNavMenu.classList.add('hidden');
            mobileMenuBtn.classList.remove('z-50');
        }
        mobileMenuBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            if (mobileNavMenu.classList.contains('hidden')) {
                openMobileMenu();
            } else {
                closeMobileMenu();
            }
        });
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileMenuBtn.contains(e.target) && !mobileNavMenu.contains(e.target)) {
                closeMobileMenu();
            }
        });
        // Prevent closing when clicking inside menu
        mobileNavMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
        // Account links and logout
        var isCustomerLoggedIn = <?= json_encode($isCustomerLoggedIn) ?>;
        const accountLinks = document.querySelectorAll('.loginclick a');
        accountLinks.forEach(link => {
            link.addEventListener('click', function(event) {
                if (!isCustomerLoggedIn) {
                    event.preventDefault();
                    window.location.href = '<?= $this->Url->build(['controller' => 'Login', 'action' => 'index']) ?>?redirect=' + '<?= $this->request->getRequestTarget() ?>';
                }
            });
        });
        const logoutButtons = document.querySelectorAll('.logoutbtn');
        logoutButtons.forEach(button => {
            button.addEventListener('click', function(event) {
                event.preventDefault();
                window.location.href = '<?= $this->Url->build(['controller' => 'Login', 'action' => 'logout']) ?>';
            });
        });
    });
</script>

<script>
    const currentLang = '<?= $this->request->getParam('lang') ?>';

    const supportedLangs = ['af', 'sq', 'alz','am', 'ar', 'ban', 'bal', 'bn', 'bew', 'bho', 'bs', 'bg', 'bua', 'yue', 'ca', 'ca', 'ny', 'zh-CN', 'zh-TW', 'crh', 'hr', 'cs', 'nl', 'en', 'et', 'tl', 'fr','fr-CA', 'ka', 'de' , 'el', 'gu', 'iw', 'hi', 'hu', 'id', 'ja', 'kn', 'lb', 'ms', 'mt', 'mr', 'mwr', 'new', 'pt', 'pt-PT', 'pa', 'pa-Arab', 'es' ,'ta', 'te', 'tr'];
    
    // document.addEventListener("DOMContentLoaded", function () {
    //     // Set selected language label in dropdown on load
    //     const selected = document.querySelector(`.dropdown-menu div[data-lang="${currentLang}"]`);
    //     if (selected) {
    //         document.getElementById("selectedLanguage").innerHTML = selected.innerHTML;
    //     }
    //     changeLanguage(currentLang);
    // });


    // Language dropdown toggle
    document.getElementById("selectedLanguage").addEventListener("click", function(e) {
        e.stopPropagation();
        const menu = document.getElementById("languageMenu");
        menu.classList.toggle("hidden");
    });

    // Hide dropdown when clicking outside
    document.addEventListener("click", function(e) {
        const menu = document.getElementById("languageMenu");
        if (!document.getElementById("selectedLanguage").contains(e.target)) {
            menu.classList.add("hidden");
        }
    });

    // Language selection
    document.querySelectorAll("#languageMenu button").forEach(option => {
        option.addEventListener("click", function () {
            const selectedLang = this.dataset.lang;
            document.getElementById("selectedLangLabel").innerHTML = this.innerHTML;
            document.getElementById("languageMenu").classList.add("hidden");
            updateURLWithLang(selectedLang); // Reloads with new lang
        });
    });


    function triggerGoogleTranslate(lang) {
        // Wait for Google Translate widget to be available
        const interval = setInterval(() => {
            const selectField = document.querySelector('.goog-te-combo');
            if (selectField) {
                selectField.value = lang;
                selectField.dispatchEvent(new Event('change'));
                clearInterval(interval);
            }
        }, 500);
        // Stop after 10 seconds if not found
        setTimeout(() => clearInterval(interval), 10000);
    }

    function updateURLWithLang(lang) {
    
        const currentUrl = new URL(window.location.href);
        const pathname = currentUrl.pathname;

        const baseUrl = '<?= \Cake\Routing\Router::url("/", true); ?>';
        const basePath = new URL(baseUrl).pathname.replace(/\/$/, '');
        
        let relativePath = pathname.startsWith(basePath) ? pathname.slice(basePath.length) : pathname;

        let pathParts = relativePath.split('/').filter(p => p);

        // Replace the language code if present after the base path
        if (pathParts.length > 0 && supportedLangs.includes(pathParts[0])) {
            pathParts[0] = lang;
        } else {
            pathParts.unshift(lang);
        }

        const newPath = basePath + '/' + pathParts.join('/');
        const newUrl = currentUrl.origin + newPath + currentUrl.search + currentUrl.hash;

        window.location.href = newUrl;
    }

    // Wait for Google Translate to load and apply selected language

    window.addEventListener("load", () => {
        // Google Translate widget loads asynchronously, so wait for it
        setTimeout(() => {
            triggerGoogleTranslate(currentLang || 'en');
        }, 1200);
    });

</script>
<script type="text/javascript">
    function googleTranslateElementInit() {
        new google.translate.TranslateElement({pageLanguage: 'en', includedLanguages: 'en,hi,fr,de,es'}, 'google_translate_element');
    }
</script>
<script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>

<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
<script src="<?= $this->Url->webroot('js/ax-custom.js') ?>"></script>

</body>
</html>
