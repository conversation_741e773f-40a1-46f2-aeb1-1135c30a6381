<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * States Model
 *
 * @property \App\Model\Table\CountriesTable&\Cake\ORM\Association\BelongsTo $Countries
 * @property \App\Model\Table\CitiesTable&\Cake\ORM\Association\HasMany $Cities
 * @property \App\Model\Table\CoursesTable&\Cake\ORM\Association\HasMany $Courses
 * @property \App\Model\Table\CustomersTable&\Cake\ORM\Association\HasMany $Customers
 * @property \App\Model\Table\PartnersTable&\Cake\ORM\Association\HasMany $Partners
 *
 * @method \App\Model\Entity\State newEmptyEntity()
 * @method \App\Model\Entity\State newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\State> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\State get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\State findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\State patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\State> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\State|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\State saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\State>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\State>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\State>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\State> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\State>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\State>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\State>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\State> deleteManyOrFail(iterable $entities, array $options = [])
 */
class StatesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('states');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->belongsTo('Countries', [
            'foreignKey' => 'country_id',
            'joinType' => 'INNER',
        ]);
        $this->hasMany('Cities', [
            'foreignKey' => 'state_id',
        ]);
        $this->hasMany('Courses', [
            'foreignKey' => 'state_id',
        ]);
        $this->hasMany('Customers', [
            'foreignKey' => 'state_id',
        ]);
        $this->hasMany('Partners', [
            'foreignKey' => 'state_id',
        ]);
        $this->belongsTo('Regions', [
            'foreignKey' => 'region_id',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('country_id')
            ->notEmptyString('country_id');

        $validator
            ->scalar('name')
            ->maxLength('name', 100)
            ->requirePresence('name', 'create')
            ->notEmptyString('name');

        $validator
            ->scalar('status')
            ->allowEmptyString('status');

        $validator
            ->dateTime('created_at')
            ->notEmptyDateTime('created_at');

        $validator
            ->dateTime('modified_at')
            ->notEmptyDateTime('modified_at');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['country_id'], 'Countries'), ['errorField' => 'country_id']);

        return $rules;
    }
    
    /**
     * Get state id by name and country_id, creating if not exists.
     * Ensures uniqueness for (country_id, name) combination.
     * @param string $stateName
     * @param int $countryId
     * @return int State ID
     */
    public function getOrCreateStateId(string $stateName, int $countryId): int
    {
        $state = $this->find()
            ->where(['name' => $stateName, 'country_id' => $countryId])
            ->first();

        if ($state) {
            return $state->id;
        }

        $entity = $this->newEntity([
            'name' => $stateName,
            'country_id' => $countryId
        ]);
        $this->save($entity);
        return $entity->id;
    }

    public function getListByCountry($countryId){
        $res = $this->find()
            ->select(['id', 'name'])
            ->where([
                'country_id' => $countryId,
                'status' => 'A'
            ])
            ->orderAsc('name')
            ->all()
            ->toArray();

        return $res;
    }

    public function getList(){
        $res = $this->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A'])->toArray();

        return $res;
    }
}
