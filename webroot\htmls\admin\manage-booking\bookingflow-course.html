<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>

<body>
    <section class="group-booking grp-booking-body-content">
        <div class="container mx-auto px-4">
            <div class="flex bg-[#D9D9D9] py-1 mt-1 pr-1 rounded-full justify-between items-center">
                <div class="text-[#000000] text-sm font-medium py-1 px-3 order-id">
                    <p>ORDER NO: 407-9914751-562757</p>
                </div>
                <div class="text-[#FFFFFF] text-sm  font-medium bg-[#00AE4D] py-2 px-3 rounded-full online">
                    <p>ONLINE</p>
                </div>
            </div>
            <div class="flex items-center space-x-4 pt-6 pb-4 bg-[] grp-booking-body">
                <!-- Image -->
                <img src="https://www.shutterstock.com/shutterstock/videos/1074813608/thumb/1.jpg" alt="Yoga Group"
                    class="w-24 h-24 rounded-lg object-cover" />

                <!-- Text Content -->
                <div class="content">
                    <!-- Organizer -->
                    <p class="text-sm text-[#D87A61] font-medium">@The Art of Living</p>

                    <!-- Title -->
                    <h2 class="text-xl md:text-2xl font-bold text-gray-800">
                        200-Hour Yoga Teacher Training in India
                    </h2>

                    <!-- Info Row -->
                    <div class="flex items-center mt-2 space-x-6 text-sm text-gray-700 time-info hidden sm:block">
                        <!-- Time -->
                        <div class="flex items-center space-x-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                                fill="none">
                                <path
                                    d="M14.6673 7.99967C14.6673 11.6797 11.6807 14.6663 8.00065 14.6663C4.32065 14.6663 1.33398 11.6797 1.33398 7.99967C1.33398 4.31967 4.32065 1.33301 8.00065 1.33301C11.6807 1.33301 14.6673 4.31967 14.6673 7.99967Z"
                                    stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                                <path opacity="0.4"
                                    d="M10.4739 10.1202L8.40724 8.88684C8.04724 8.6735 7.75391 8.16017 7.75391 7.74017V5.00684"
                                    stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <span><span class="italic font-semibold">Mon - Fri : 10:00 AM to 6:00 PM</span></span>
                        </div>

                        <!-- Language -->
                        <div class="flex items-center space-x-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                                fill="none">
                                <path
                                    d="M14.6673 7.99967C14.6673 11.6797 11.6807 14.6663 8.00065 14.6663C4.32065 14.6663 1.33398 11.6797 1.33398 7.99967C1.33398 4.31967 4.32065 1.33301 8.00065 1.33301C11.6807 1.33301 14.6673 4.31967 14.6673 7.99967Z"
                                    stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                                <path opacity="0.4"
                                    d="M10.4739 10.1202L8.40724 8.88684C8.04724 8.6735 7.75391 8.16017 7.75391 7.74017V5.00684"
                                    stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <span><span class="italic font-semibold">Mon - Fri : 10:00 AM to 6:00 PM</span></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content block sm:hidden ">
                <!-- Info Row -->
                <div class="flex items-center text-sm text-gray-600 mt-1 date-content justify-between py-2">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M5.33398 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10"
                                stroke-linecap="round" stroke-linejoin="round" />
                            <path d="M10.666 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10"
                                stroke-linecap="round" stroke-linejoin="round" />
                            <path opacity="0.4" d="M2.33398 6.05957H13.6673" stroke="#C45F44" stroke-miterlimit="10"
                                stroke-linecap="round" stroke-linejoin="round" />
                            <path
                                d="M14.6673 12.6667C14.6673 13.1667 14.5273 13.64 14.2807 14.04C13.8207 14.8133 12.974 15.3333 12.0007 15.3333C11.3273 15.3333 10.714 15.0867 10.2473 14.6667C10.0407 14.4933 9.86065 14.28 9.72065 14.04C9.47399 13.64 9.33398 13.1667 9.33398 12.6667C9.33398 11.1933 10.5273 10 12.0007 10C12.8007 10 13.514 10.3533 14.0007 10.9067C14.414 11.38 14.6673 11.9933 14.6673 12.6667Z"
                                stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path d="M10.9609 12.667L11.6209 13.327L13.0409 12.0137" stroke="#C45F44"
                                stroke-linecap="round" stroke-linejoin="round" />
                            <path
                                d="M14 5.66634V10.9063C13.5133 10.353 12.8 9.99967 12 9.99967C10.5267 9.99967 9.33333 11.193 9.33333 12.6663C9.33333 13.1663 9.47333 13.6397 9.72 14.0397C9.86 14.2797 10.04 14.493 10.2467 14.6663H5.33333C3 14.6663 2 13.333 2 11.333V5.66634C2 3.66634 3 2.33301 5.33333 2.33301H10.6667C13 2.33301 14 3.66634 14 5.66634Z"
                                stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path opacity="0.4" d="M7.99764 9.13314H8.00363" stroke="#C45F44" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path opacity="0.4" d="M5.52889 9.13314H5.53488" stroke="#C45F44" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path opacity="0.4" d="M5.5293 11.1338H5.53528" stroke="#C45F44" stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                        <span class="date pl-2 italic font-bold text-[#000000]">06 Mar 2025 - 06 Apr 2025</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path
                                d="M8.00065 14.6663C11.6825 14.6663 14.6673 11.6816 14.6673 7.99967C14.6673 4.31778 11.6825 1.33301 8.00065 1.33301C4.31875 1.33301 1.33398 4.31778 1.33398 7.99967C1.33398 11.6816 4.31875 14.6663 8.00065 14.6663Z"
                                stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                            <g opacity="0.4">
                                <path d="M5.33372 2H6.00039C4.70039 5.89333 4.70039 10.1067 6.00039 14H5.33372"
                                    stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M10 2C11.3 5.89333 11.3 10.1067 10 14" stroke="#C45F44" stroke-linecap="round"
                                    stroke-linejoin="round" />
                                <path d="M2 10.6667V10C5.89333 11.3 10.1067 11.3 14 10V10.6667" stroke="#C45F44"
                                    stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M2 6.00039C5.89333 4.70039 10.1067 4.70039 14 6.00039" stroke="#C45F44"
                                    stroke-linecap="round" stroke-linejoin="round" />
                            </g>
                        </svg>
                        <span class="date pl-2 italic font-bold text-[#000000]">English</span>
                    </div>
                </div>
                <div class="flex items-center text-sm text-gray-600  date-content justify-between py-2">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path
                                d="M14.6673 7.99967C14.6673 11.6797 11.6807 14.6663 8.00065 14.6663C4.32065 14.6663 1.33398 11.6797 1.33398 7.99967C1.33398 4.31967 4.32065 1.33301 8.00065 1.33301C11.6807 1.33301 14.6673 4.31967 14.6673 7.99967Z"
                                stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                            <path opacity="0.4"
                                d="M10.4739 10.1202L8.40724 8.88684C8.04724 8.6735 7.75391 8.16017 7.75391 7.74017V5.00684"
                                stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <span class="date pl-2 italic font-bold text-[#000000]">Mon - Fri : 10 :00 AM to 6:00 PM</span>
                    </div>
                    <div class="flex items-center">

                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="select-branch bg-[] select-classes book-course-content">
        <div class="container mx-auto  px-4">
            <div class="flex items-center text-sm text-gray-600 mt-1 date-content flex-start pt-4 py-2">
                <div class="flex items-center">

                    <span class="date font-bold text-[#000000]">Participant 1</span>
                </div>
                <span class="ml-4 flex items-center date">
                    <span class="px-2 py-1 text-[#00AE4D] text-[10px] border border-[#00AE4D] rounded-full font-bold">
                        Tamil Nadu | Indian</span>
                </span>
            </div>
            <div class=" mx-auto content border-b-2 border-[#983419] pt-2 pb-5">

                <!-- Header -->
                <h2 class="text-xl font-semibold text-[#983419]">
                    Ms. Gisha Jatin Shah
                </h2>
                <p class="text-gray-500 italic text-sm mb-4">
                    India Resident | 24 yrs | +91 79043 30323 | Veg
                </p>

                <!-- Cost Breakdown -->
                <div class="space-y-2 text-sm italic">
                    <div class="flex justify-between">
                        <span>Base : Online</span>
                        <div class="text-right">
                            <span class="text-[#983419] font-semibold mr-4">27,500 INR</span>
                            <span class="text-[#983419] font-semibold">330 USD</span>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <span>Course Material</span>
                        <div class="text-right">
                            <span class="text-[#983419] font-semibold mr-4">2000 INR</span>
                            <span class="text-[#983419] font-semibold">24 USD</span>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <span>Certification</span>
                        <div class="text-right">
                            <span class="text-[#983419] font-semibold mr-4">200 INR</span>
                            <span class="text-[#983419] font-semibold">2.64 USD</span>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <span>
                            Discount Applied:<br>
                            <span class="font-bold italic text-black">NEW10</span>
                        </span>
                        <div class="text-right">
                            <span class="text-red-600 font-semibold mr-4">-200 INR</span>
                            <span class="text-red-600 font-semibold">-2.64 USD</span>
                        </div>
                    </div>
                    <hr class="border-t border-gray-300 mt-4">
                    <div class="flex justify-between pt-2 text-base font-semibold">
                        <span>Sub-Total</span>
                        <div class="text-right">
                            <span class="text-[#983419]  font-bold mr-4">29920 INR</span>
                            <span class="text-[#983419] font-bold">359.04 USD</span>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </section>
    <section class="select-branch bg-[] select-classes">
        <div class="w-full  overflow-hidden border border-[#f3c9b7] ">
            <!-- Dropdown -->
            <div class="max-w-md mx-auto border border-gray-300 rounded overflow-hidden text-sm payment-summary">
                <!-- Accordion Header -->
                <!-- <button
                        onclick="document.getElementById('payment-summary-content').classList.toggle('hidden'); this.querySelector('svg').classList.toggle('rotate-180')"
                        class="w-full bg-[#ffede6] px-4 py-2 flex items-center justify-between text-gray-700 font-semibold border-b">
                        <span>Payment Summary</span>
                        <svg class="w-4 h-4 transition-transform duration-300" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button> -->
                <!-- Accordion Content -->
                <div id="payment-summary-content" class="bg-[#fff4f4] px-4 py-4 space-y-4">
                    <!-- Title -->

                    <div class=" p-2  space-y-4 text-sm">
                        <!-- Total Discounts -->
                        <div class="flex justify-between items-center">
                            <span class="italic">Total Discounts</span>
                            <span class="text-red-600 font-semibold italic">-200 USD</span>
                        </div>

                        <!-- Total Before Tax -->
                        <div class="flex justify-between items-center total-before-tax font-bold">
                            <span>Total Before Tax (USD)</span>
                            <span class="italic font-bold text-lg value">8611.54 USD</span>
                        </div>

                        <!-- Applicable Tax -->
                        <div class="flex justify-between items-center text-sm Applicable-tax">
                            <span class="italic">Applicable Tax (Indian GST : 18 %)</span>
                            <span class="italic font-semibold text-[#505050] value">1,550.08 USD</span>
                        </div>
                    </div>

                </div>
            </div>


            <!-- Bottom Section -->
            <div class="bg-[#1f2941] text-white px-4 py-3 flex items-center justify-between">
                <div>
                    <div class="text-lg font-bold">27,500 INR</div>
                    <div class="text-xs text-gray-300">For 3 Participants</div>
                </div>
                <button
                    class="bg-[#00AE4D] hover:bg-[#00AE4D] text-[white] text-[16px] font-semibold px-4 py-2 rounded-lg">
                    Download Invoice
                </button>
            </div>


        </div>
    </section>

</body>


<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

    .book-course-content .content.border-b-2:last-child {
        border: 0 !important;
    }

    .group-booking h2 {
        font-family: "Open Sans", sans-serif;
        font-size: 18px;
        font-weight: 700;
        color: #293148;
        text-decoration: underline;
    }

    .group-booking .content p {
        font-family: "Poppins", sans-serif;
        font-size: 10px;
        font-weight: 600;
    }

    .time-info .italic {
        font-family: "Open Sans", sans-serif;
        font-style: italic;
        font-size: 11px;
        font-weight: 600;
    }

    .grp-booking-body-content {
        border-bottom: 2px solid #C45F44;
        padding: 0px 0px 20px;
    }
    .grp-booking-body-content .order-id{
        font-weight: 600;
        font-size: 10px;
    }
    .grp-booking-body-content .online{
           font-weight: 600;
        font-size: 8px;
    }
</style>

</html>