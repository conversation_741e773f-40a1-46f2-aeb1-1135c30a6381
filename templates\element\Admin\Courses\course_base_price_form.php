<h5 class="mb-4">Base Prices</h5>

<div x-data="basePriceForm(<?= !empty($basePricesJson) ? h($basePricesJson) : ''  ?>)" class="alpine-section row g-3">
    <template x-for="(price, index) in prices" :key="index">
        <div class="card p-3 mb-3">
            <div class="row g-3 align-items-end">
                <!-- Name -->
                <div class="col-md-2">
                    <label class="form-label d-block">Name</label>
                    <div><input type="text"
                           class="form-control"
                           :name="'course_base_prices[' + index + '][name]'"
                           x-model="price.name"
                           :class="{ 'is-invalid': showErrors && !price.name }">
                        <div class="text-danger text-sm h-5" x-show="showErrors && !price.name">Name is required</div>
                    </div>
                </div>

                <!-- Currency -->
                <div class="col-md-2">
                    <label class="form-label d-block">Currency</label>
                    <div><select class="form-select"
                            :name="'course_base_prices[' + index + '][currency_id]'"
                            x-model="price.currency_id"
                            :class="{ 'is-invalid': showErrors && !price.currency_id }">
                        <option value="">Select</option>
                        <?php if (!empty($currencies)): ?>
                            <?php foreach ($currencies as $key => $currency): ?>
                                <option value="<?= h($key) ?>"><?= h($currency) ?></option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                    <div class="text-danger text-sm h-5" x-show="showErrors && !price.currency_id">Currency is required</div></div>
                </div>

                <!-- Price -->
                <div class="col-md-2">
                    <label class="form-label d-block">Price</label>
                    <div><input type="number"
                           class="form-control"
                           :name="'course_base_prices[' + index + '][price]'"
                           x-model="price.price"
                           :class="{ 'is-invalid': showErrors && !price.price }">
                    <div class="text-danger text-sm h-5" x-show="showErrors && !price.price">Price is required</div>
                    </div>
                </div>

                <!-- Total Count -->
                <!-- <div class="col-md-2">
                    <label class="form-label d-block">Total Count</label>
                    <div><input type="number"
                           class="form-control"
                           :name="'course_base_prices[' + index + '][total_count]'"
                           x-model="price.total_count"
                           :class="{ 'is-invalid': showErrors && !price.total_count }">
                    <div class="text-danger text-sm h-5" x-show="showErrors && !price.total_count">Max count is required</div></div>
                </div> -->

                <!-- Sort Order -->
                <div class="col-md-2">
                    <label class="form-label d-block">Sort Order</label>
                    <div><input type="number" min="1"
                           class="form-control"
                           :name="'course_base_prices[' + index + '][sort_order]'"
                           x-model="price.sort_order">
                    <div class="text-danger text-sm h-5" x-show="showErrors && !price.sort_order">Sort Order is required</div></div>
                </div>

                <!-- Status -->
                <div class="col-md-2">
                    <label class="form-label d-block">Status</label>
                    <div><select class="form-select"
                            :name="'course_base_prices[' + index + '][status]'"
                            x-model="price.status"
                            :class="{ 'is-invalid': showErrors && !price.status }">
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                    <div class="text-danger text-sm h-5" x-show="showErrors && !price.status">Status is required</div></div>
                </div>

                <!-- Remove Button -->
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger mt-4" @click="removePrice(index)">
                        Remove
                    </button>
                </div>
            </div>
        </div>
    </template>

    <!-- Add Button -->
    <div class="col-12">
        <button type="button" class="btn btn-primary" @click="addPrice()">
            + Add Base Price
        </button>
    </div>

    <!-- Hidden JSON Field for backend -->
    <!-- <textarea class="d-none" name="course_base_prices_json" x-text="JSON.stringify(prices)"></textarea> -->
</div>

<script>
function basePriceForm(initialPrices = []) {
    return {
        showErrors: false,
        prices: initialPrices.length ? initialPrices.map((p, i) => (
            {
                name: p.name || '',
                currency_id: p.currency_id || '',
                price: p.price || '',
                // total_count: p.total_count || '',
                sort_order: p.sort_order || i + 1,
                status: 'active'
            }
        )) : [
            {
                name: '',
                currency_id: '',
                price: '',
                // total_count: '',
                sort_order: 1,
                status: 'active'
            }
        ],
        addPrice() {
            const nextOrder = this.prices.length + 1;
            this.prices.push({
                name: '',
                currency_id: '',
                price: '',
                // total_count: '',
                sort_order: nextOrder,
                status: 'active'
            });
        },
        removePrice(index) {
            this.prices.splice(index, 1);
            // Recalculate sort_order after removal
            this.prices.forEach((price, i) => {
                price.sort_order = i + 1;
            });
        },
        validateBasePriceForm() {
            this.showErrors = true;
            return this.prices.every(p =>
                p.name && p.currency_id && p.price && p.status && p.sort_order
            );
        }
    }
}
</script>
