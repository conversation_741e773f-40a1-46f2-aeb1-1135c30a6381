<?php
declare(strict_types=1);

namespace App\Controller\Admin;

use Cake\Event\EventInterface;

/**
 * Bookings Controller
 *
 * @property \App\Model\Table\BookingsTable $Bookings
 */
class BookingsController extends AppController
{
    protected $Customers;
    protected $Courses;
    protected $Partners;
    protected $BookingAddons;
    // protected $BookingDetails;
    protected $BookingItems;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('CustomPaginator');

        $this->Customers = $this->fetchTable('Customers');
        $this->Courses = $this->fetchTable('Courses');
        $this->Partners = $this->fetchTable('Partners');
        $this->BookingAddons = $this->fetchTable('BookingAddons');
        // $this->BookingDetails = $this->fetchTable('BookingDetails');
        $this->BookingItems = $this->fetchTable('BookingItems');
    }

    public function beforeFilter(EventInterface $event)
    {
        parent::beforeFilter($event);
    }

    public function index()
    {
        $status = $this->request->getQuery('status');
        $paymentStatus = $this->request->getQuery('payment_status');
        $dateFrom = $this->request->getQuery('date_from');
        $dateTo = $this->request->getQuery('date_to');
        $search = $this->request->getQuery('search');

        $page = (int)$this->request->getQuery('page', 1);

        $query = $this->Bookings->find()->contain([
            'Customers' => ['Users'],
            'Courses',
            'Partners',
            'BookingAddons',
            // 'BookingDetails',
            'BookingItems'
        ]);

        // Add filters
        if (!empty($status)) {
            $query->where(['Bookings.booking_status' => $status]);
        }

        if (!empty($paymentStatus)) {
            $query->where(['Bookings.payment_status' => $paymentStatus]);
        }

        if (!empty($dateFrom)) {
            $query->where(['Bookings.booking_date >=' => $dateFrom]);
        }

        if (!empty($dateTo)) {
            $query->where(['Bookings.booking_date <=' => $dateTo]);
        }

        // Add search functionality
        if (!empty($search)) {
            $searchConditions = ['OR' => []];

            // Only search by ID if the search term is numeric
            if (is_numeric($search)) {
                $searchConditions['OR']['Bookings.id'] = (int)$search;
            }

            // Search in customer names (Users table through Customers association)
            $searchConditions['OR']['Users.first_name LIKE'] = '%' . $search . '%';
            $searchConditions['OR']['Users.last_name LIKE'] = '%' . $search . '%';
            $searchConditions['OR']['Users.email LIKE'] = '%' . $search . '%';

            // Uncomment these when needed
            // $searchConditions['OR']['Courses.title LIKE'] = '%' . $search . '%';
            // $searchConditions['OR']['Partners.business_name LIKE'] = '%' . $search . '%';

            $query->where($searchConditions);
        }

        $query->orderBy(['Bookings.id' => 'DESC']);

        // Use CustomPaginator for consistent pagination
        $result = $this->CustomPaginator->paginate($query, [
            'limit' => 10,
            'page' => $page
        ]);

        $bookings = $result['items'];
        $pagination = $result['pagination'];

        // Calculate summary statistics
        $totalBookings = $this->Bookings->find()->count();
        $confirmedBookings = $this->Bookings->find()->where(['booking_status' => 'confirmed'])->count();
        $pendingBookings = $this->Bookings->find()->where(['booking_status' => 'pending'])->count();
        $cancelledBookings = $this->Bookings->find()->where(['booking_status' => 'cancelled'])->count();

        $totalRevenue = $this->Bookings->find()
            ->where(['payment_status' => 'paid'])
            ->select(['total' => $this->Bookings->find()->func()->sum('grand_total')])
            ->first();

        $stats = [
            'total' => $totalBookings,
            'confirmed' => $confirmedBookings,
            'pending' => $pendingBookings,
            'cancelled' => $cancelledBookings,
            'revenue' => $totalRevenue->total ?? 0
        ];

        // AJAX request for pagination/filter
        if ($this->request->is('ajax') || $this->request->getQuery('ajax')) {
            $this->set(compact('bookings', 'pagination'));
            $this->viewBuilder()->setLayout('ajax');
            $this->render('/element/Admin/Bookings/table_content');
            return $this->response;
        }

        $title = 'Bookings Management';

        $this->set(compact('bookings', 'pagination', 'stats', 'title', 'status', 'paymentStatus', 'dateFrom', 'dateTo', 'search'));
    }

    /**
     * View method - View booking details
     *
     * @param string|null $id Booking id.
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function view($id = null)
    {
        $booking = $this->Bookings->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'Courses' => [
                    'Partners',
                    'CourseTypes',
                    'CourseBatches'
                ],
                'Partners',
                'BookingAddons',
                // 'BookingDetails' => [
                //     'CourseBatches'
                // ],
                'BookingItems'
            ]
            ]);
          
    
        

        // Calculate addon totals
        $addonTotal = 0;
        foreach ($booking->booking_addons as $addon) {
            $addonTotal += (float)$addon->total_price;
        }

        // Get booking timeline/history
        $timeline = [];
        
        // Add booking creation
        $timeline[] = [
            'date' => $booking->created_at,
            'event' => 'Booking Created',
            'description' => 'Booking was created with status: ' . $booking->booking_status,
            'icon' => 'fas fa-plus-circle',
            'color' => 'primary'
        ];

        // Add payments
        // foreach ($booking->payments as $payment) {
        //     $timeline[] = [
        //         'date' => $payment->created_at,
        //         'event' => 'Payment ' . ucfirst($payment->status),
        //         'description' => 'Payment of ' . $payment->currency . ' ' . $payment->amount,
        //         'icon' => 'fas fa-credit-card',
        //         'color' => $payment->status === 'completed' ? 'success' : 'warning'
        //     ];
        // }

        // // Add cancellations
        // foreach ($booking->cancellations as $cancellation) {
        //     $timeline[] = [
        //         'date' => $cancellation->created_at,
        //         'event' => 'Booking Cancelled',
        //         'description' => 'Reason: ' . ($cancellation->reason ?? 'Not specified'),
        //         'icon' => 'fas fa-times-circle',
        //         'color' => 'danger'
        //     ];
        // }

        // Sort timeline by date
        usort($timeline, function($a, $b) {
            return $b['date'] <=> $a['date'];
        });

        $title = 'Booking Details - #' . $booking->id;
        $this->set(compact('booking', 'addonTotal', 'timeline', 'title'));
    }

    /**
     * Update booking status
     */
    public function updateStatus($id = null)
    {
        $this->request->allowMethod(['post', 'put']);
        
        $booking = $this->Bookings->get($id);
        $newStatus = $this->request->getData('status');
        
        if (in_array($newStatus, ['pending', 'confirmed', 'cancelled'])) {
            $booking->booking_status = $newStatus;
            
            if ($this->Bookings->save($booking)) {
                $this->Flash->success(__('Booking status updated successfully.'));
            } else {
                $this->Flash->error(__('Unable to update booking status.'));
            }
        } else {
            $this->Flash->error(__('Invalid status provided.'));
        }
        
        return $this->redirect(['action' => 'view', $id]);
    }

    /**
     * Update payment status
     */
    public function updatePaymentStatus($id = null)
    {
        $this->request->allowMethod(['post', 'put']);
        
        $booking = $this->Bookings->get($id);
        $newStatus = $this->request->getData('payment_status');
        
        if (in_array($newStatus, ['pending', 'paid', 'cancelled', 'refunded'])) {
            $booking->payment_status = $newStatus;
            
            if ($this->Bookings->save($booking)) {
                $this->Flash->success(__('Payment status updated successfully.'));
            } else {
                $this->Flash->error(__('Unable to update payment status.'));
            }
        } else {
            $this->Flash->error(__('Invalid payment status provided.'));
        }
        
        return $this->redirect(['action' => 'view', $id]);
    }

    /**
     * View booking item (participant) details
     *
     * @param string|null $id BookingItem id.
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function viewParticipant($id = null)
    {
        $bookingItem = $this->BookingItems->get($id, [
            'contain' => [
                'Bookings' => [
                    'Customers' => ['Users'],
                    'Courses',
                    'Partners'
                ]
            ]
        ]);

        // Get addons for this specific participant
        $participantAddons = $this->BookingAddons->find()
            ->where(['booking_item_id' => $id])
            ->toArray();

        $title = 'Participant Details';
        $this->set(compact('bookingItem', 'participantAddons', 'title'));
    }

    /**
     * Generate Invoice for booking
     */
    public function invoice($id = null)
    {
        $booking = $this->Bookings->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'Courses' => [
                    'Partners',
                    'CourseTypes'
                ],
                // 'Classes',
                // 'ClassTimeSlots',
                'Partners',
                //'Discounts',
                'BookingAddons',
                // 'BookingDetails' => [
                //     'CourseBatches'
                // ],
               // 'Payments'
            ]
        ]);

        // Calculate totals
        $subtotal = (float)$booking->total_price;
        $addonTotal = 0;
        foreach ($booking->booking_addons as $addon) {
            $addonTotal += (float)$addon->total_price;
        }

        $discountAmount = (float)($booking->discount_value ?? 0);
        $taxAmount = (float)($booking->tax_amount ?? 0);
        $grandTotal = (float)($booking->grand_total ?? ($subtotal + $addonTotal + $taxAmount - $discountAmount));

        // Generate invoice number
       // $invoiceNumber = 'INV-' . str_pad($booking->id, 6, '0', STR_PAD_LEFT);
            $invoiceNumber = 'INV-' . $booking->id;
        // Get company/partner details
        $companyInfo = [
            'name' => $booking->partner->name ?? '',
            'address' => $booking->partner->address ?? '',
            'phone' => $booking->partner->phone ?? '',
            'email' => $booking->partner->email ?? '',
            
        ];

        $title = 'Invoice - ' . $invoiceNumber;
        $this->set(compact(
            'booking',
            'subtotal',
            'addonTotal',
            'discountAmount',
            'taxAmount',
            'grandTotal',
            'invoiceNumber',
            'companyInfo',
            'title'
        ));

        // Set layout for invoice
        $this->viewBuilder()->setLayout('invoice');
    }

    /**
     * Modern Invoice Design
     */
    public function invoiceModern($id = null)
    {
        $booking = $this->Bookings->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'Courses' => [
                    'Partners',
                    'CourseTypes'
                ],
                'Partners',
                'BookingAddons',
                // 'BookingDetails' => [
                //     'CourseBatches'
                // ]
            ]
        ]);

        // Calculate totals
        $subtotal = (float)$booking->total_price;
        $addonTotal = 0;
        foreach ($booking->booking_addons as $addon) {
            $addonTotal += (float)($addon->total_price ?? ($addon->addon_price * ($addon->quantity ?? 1)));
        }

        $discountAmount = (float)($booking->discount_value ?? 0);
        $taxAmount = (float)($booking->tax_amount ?? 0);
        $grandTotal = (float)($booking->grand_total ?? ($subtotal + $addonTotal + $taxAmount - $discountAmount));

        // Generate invoice number
        $invoiceNumber = 'INV-' . $booking->id;

        // Get company/partner details
        $companyInfo = [
            'name' => $booking->partner->name ?? 'Wellness Studio',
            'address' => $booking->partner->address ?? 'Professional Address',
            'phone' => $booking->partner->phone ?? '+****************',
            'email' => $booking->partner->email ?? '<EMAIL>',
        ];

        $title = 'Modern Invoice - ' . $invoiceNumber;
        $this->set(compact(
            'booking',
            'subtotal',
            'addonTotal',
            'discountAmount',
            'taxAmount',
            'grandTotal',
            'invoiceNumber',
            'companyInfo',
            'title'
        ));

        // Set layout and template for modern invoice
        $this->viewBuilder()->setLayout('invoice_modern');
        $this->viewBuilder()->setTemplate('invoice_modern');
    }

    /**
     * Corporate Invoice Design
     */
    public function invoiceCorporate($id = null)
    {
        $booking = $this->Bookings->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'Courses' => [
                    'Partners',
                    'CourseTypes'
                ],
                'Partners',
                'BookingAddons',
                // 'BookingDetails' => [
                //     'CourseBatches'
                // ]
            ]
        ]);

        // Calculate totals
        $subtotal = (float)$booking->total_price;
        $addonTotal = 0;
        foreach ($booking->booking_addons as $addon) {
            $addonTotal += (float)($addon->total_price ?? ($addon->addon_price * ($addon->quantity ?? 1)));
        }

        $discountAmount = (float)($booking->discount_value ?? 0);
        $taxAmount = (float)($booking->tax_amount ?? 0);
        $grandTotal = (float)($booking->grand_total ?? ($subtotal + $addonTotal + $taxAmount - $discountAmount));

        // Generate invoice number
        $invoiceNumber = 'INV-' . $booking->id;

        // Get company/partner details
        $companyInfo = [
            'name' => $booking->partner->name ?? 'Corporate Wellness Solutions',
            'address' => $booking->partner->address ?? 'Corporate Headquarters',
            'phone' => $booking->partner->phone ?? '+****************',
            'email' => $booking->partner->email ?? '<EMAIL>',
        ];

        $title = 'Corporate Invoice - ' . $invoiceNumber;
        $this->set(compact(
            'booking',
            'subtotal',
            'addonTotal',
            'discountAmount',
            'taxAmount',
            'grandTotal',
            'invoiceNumber',
            'companyInfo',
            'title'
        ));

        // Set layout and template for corporate invoice
        $this->viewBuilder()->setLayout('invoice_corporate');
        $this->viewBuilder()->setTemplate('invoice_corporate');
    }




    /**
     * Export bookings to CSV
     */
    public function export()
    {
        $query = $this->Bookings->find()->contain([
            'Customers' => ['Users'],
            'Courses',
            'Partners'
        ]);

        // Apply same filters as index
        $status = $this->request->getQuery('status');
        $paymentStatus = $this->request->getQuery('payment_status');
        $dateFrom = $this->request->getQuery('date_from');
        $dateTo = $this->request->getQuery('date_to');

        if (!empty($status)) {
            $query->where(['Bookings.booking_status' => $status]);
        }
        if (!empty($paymentStatus)) {
            $query->where(['Bookings.payment_status' => $paymentStatus]);
        }
        if (!empty($dateFrom)) {
            $query->where(['Bookings.booking_date >=' => $dateFrom]);
        }
        if (!empty($dateTo)) {
            $query->where(['Bookings.booking_date <=' => $dateTo]);
        }

        $bookings = $query->toArray();

        $this->response = $this->response->withType('csv');
        $this->response = $this->response->withDownload('bookings_' . date('Y-m-d') . '.csv');
        
        $this->set(compact('bookings'));
        $this->viewBuilder()->setLayout('csv');
        $this->render('csv/export');
    }
}
