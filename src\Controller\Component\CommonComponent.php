<?php
declare(strict_types=1);

namespace App\Controller\Component;

use <PERSON>ake\Controller\Component;
use <PERSON>ake\Controller\ComponentRegistry;
use Cake\Core\Configure;

/**
 * Common component
 */
class CommonComponent extends Component
{
    /**
     * Default configuration.
     *
     * @var array<string, mixed>
     */
    protected array $_defaultConfig = [];

    public function paginationMeta($page, $limit, $total){
        // Calculate pagination metadata
        $pageCount = $total > 0 ? (int) ceil($total / $limit) : 0;
        $pagination = [
            'page'     => $page,
            'limit'    => $limit,
            'total'    => $total,
            'pageCount'=> $pageCount,
            'prevPage' => $page > 1,
            'nextPage' => $page < $pageCount
        ];
        return $pagination;
    }

    public function formatCourseModes($course, $request){
        if (!empty($course->modalities) && is_array($course->modalities)) {
            $names = array_column($course->modalities, 'name');
            $modeNames = [];

            // Online types
            if (in_array('Online Live', $names)) {
                $modeNames[] = 'Online - Live';
            }
            if (in_array('Online VOD', $names)) {
                $modeNames[] = 'Online - VOD';
            }
            if (in_array('Hybrid', $names)) {
                $modeNames[] = 'Hybrid';
            }

            // On-Site
            $onSite = in_array('On Site', $names);
            if ($onSite) {
                $location = $course->state->name ?? '';
                $modeNames[] = !empty($location) ? h($location) : 'On-Site';
            }

            $count = count($modeNames);

            if ($count > 1) {
                $icon = "<img src=".$request->getAttribute('webroot').'img/home_work.png' ." class='w-5 h-5'>";
            } elseif ($onSite) {
                $icon = "<i class='fas fa-map-marker-alt'></i>";
            } else {
                $icon = "<i class='fas fa-laptop-code'></i>";
            }

            if (!empty($modeNames)) {
                $modeparts = $icon . ' ' . implode(' | ', $modeNames);
            }

            return $modeparts;
        }
    }

    public function getExchangeRate($currency){
        $exchangeRates = Configure::read('Constants.EXCHANGERATES');
        return $exchangeRates[$currency] ?? 1;
    }

    public function convertCurrency($amount, $fromCurrency, $toCurrency)
    {
        // If currencies are the same, no conversion needed
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        $rateFrom = $this->getExchangeRate($fromCurrency) ?: 1;
        $rateTo   = $this->getExchangeRate($toCurrency) ?: 1;

        // Convert using formula: (amount / rateFrom) * rateTo
        $converted = ($amount / $rateFrom) * $rateTo;

        // Return rounded value (2 decimal places)
        return round($converted, 2);
    }

}
