<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\ORM\TableRegistry;

class ReviewQuestionsTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('review_questions');
        $this->setPrimaryKey('id');
        $this->addBehavior('Timestamp');

        $this->hasMany('ReviewAnswers', [
            'foreignKey' => 'question_id',
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('question')
            ->maxLength('question', 255)
            ->notEmptyString('question');

        $validator
            ->inList('review_type', ['course', 'teacher', 'center']);

        return $validator;
    }
}
