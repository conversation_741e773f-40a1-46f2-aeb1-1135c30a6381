<?php

namespace App\Middleware;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Cake\Http\Response;
use Cake\Core\Configure;

class LanguageRedirectMiddleware implements MiddlewareInterface
{
    protected array $supportedLanguages = ['af', 'sq', 'alz', 'am', 'ar', 'ban', 'bal', 'bn', 'bew', 'bho', 'bs', 'bg', 'bua', 'yue', 'ca', 'ca', 'ny', 'zh-CN', 'zh-TW', 'crh', 'hr', 'cs', 'nl', 'en', 'et', 'tl', 'fr', 'fr-CA', 'ka', 'de', 'el', 'gu', 'iw', 'hi', 'hu', 'id', 'ja', 'kn', 'lb', 'ms', 'mt', 'mr', 'mwr', 'new', 'pt', 'pt-PT', 'pa', 'pa-Arab', 'es', 'ta', 'te', 'tr'];

    public function process(ServerRequestInterface $request, \Psr\Http\Server\RequestHandlerInterface $handler): ResponseInterface
    {
        $path = $request->getUri()->getPath();
        $segments = explode('/', ltrim($path, '/'));
        $firstSegment  = $segments[0] ?? null;

        

        $excludedPaths = [
            'partners/get-states',
            'partners/get-cities',
            'partners/checkPartnerDuplicateEmail',
            'partners/checkPartnerDuplicatePhone',
            'partners/generate-captcha',
            'partners/verify-captcha',
            'partners/submit',
            'subscribe-newsletter',
            'unsubscribe-newsletter',
            'login',
            'signup',
            'logout',
            'signup/register',
            'signup/sendOtp',
            'signup/verifyOtp',
            'login/verifyOtp',
            'login/sendOtp',
            'login/social-login',
            'login/customer-login',
            'login/social',
            'login/social/google/callback',
            'login/social/facebook/callback',
            'sitemap.xml',
            'sitemap-index.xml',
            'sitemap/detail-courses.xml',
            'sitemap/list-partners.xml',
            'sitemap/detail-partners.xml',
            'sitemap/list-courses.xml',
            'users/forgot-password',
            'forgot-password',
            'reset-password',
            'users/reset-password',
            'verify-otp',
            'profile/update-profile',
            'profile/toggle',
            'profile/check',
            'profile/bookmarks',
            'bookings/add',
            'profile/deleteBookmark',


        ];
  
        // Skip language redirection for admin URLs or excluded paths
        if ($firstSegment === 'admin' || preg_match('#^/?uploads/#', $path) || in_array(ltrim($path, '/'), $excludedPaths) || in_array(ltrim($firstSegment, '/'), $excludedPaths)) {
            return $handler->handle($request);
        }


        // If no valid lang prefix in URL
        if (!in_array($firstSegment, $this->supportedLanguages)) {

            $preferredLang = $this->detectBrowserLanguage($request);
            // enable later #187//
            // $ip = $_SERVER['REMOTE_ADDR']; // or a specific IP for testing

            // // Replace with your actual token
            // $token = Configure::read('IPINFO_ACCESS_TOKEN');
            // $url = "https://ipinfo.io/{$ip}?token={$token}";

            // $response = @file_get_contents($url);
        
            // if ($response !== false) {
            //     $data = json_decode($response, true);
                
            //     $country = $data['country'] ?? 'US'; // e.g., 'IN', 'US'
            //     $region = $data['region'] ?? '';
            //     $city = $data['city'] ?? '';
            //     $languageMap  = Configure::read('Constants.LANGUAGE_MAP');
                
            //     if(!empty($languageMap) && $languageMap[$country]){
            //         $preferredLang = $languageMap[$country] ?? 'en';
            //     }
              
            // }

            $preferredLang = 'en'; // for dev only //

            $base = $request->getAttribute('base');
            $trimmedPath = ltrim($path, '/');
            $newPath = $base . '/' . $preferredLang . ($trimmedPath ? '/' . $trimmedPath : '');

            return (new Response())
                ->withStatus(302)
                ->withHeader('Location', $newPath);
        }

        return $handler->handle($request);
    }

    protected function detectBrowserLanguage(ServerRequestInterface $request): string
    {
        $acceptLang = $request->getHeaderLine('Accept-Language');
        if ($acceptLang) {
            $languages = explode(',', $acceptLang);
            foreach ($languages as $langEntry) {
                $code = substr($langEntry, 0, 2);
                if (in_array($code, $this->supportedLanguages)) {
                    return $code;
                }
            }
        }
        return 'en';
    }
}
