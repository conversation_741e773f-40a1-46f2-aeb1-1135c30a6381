<?php
// src/View/Helper/CardHelper.php
namespace App\View\Helper;

use Cake\View\Helper;

class CardHelper extends Helper
{
    protected array $helpers = ['Html'];

    /**
     * Render a single course card.
     * Card data may contain:
     *  - image (string URL)
     *  - title (string)
     *  - center (string) center/organization name
     *  - rating (float) course rating
     *  - description (string) course description
     *  - date (string) course date
     *  - mode (string) delivery mode (Hybrid, Online, etc.)
     *  - language (string) course language
     *  - link (string) optional
     *  - alt (string) optional
     *
     * @param array|object $cardData Card data
     * @param array $options Card options: cardClass, imageClass, wrapperClass
     * @return string HTML markup for the card
     */
    public function render($cardData, array $options = []): string
    {
        $config = $this->normalizeOptions($options);
        $data = $this->normalizeCardData($cardData);
        
        return $this->buildSingleCard($data, $config);
    }

    /**
     * Render multiple cards in a grid layout.
     *
     * @param array $cardsData Array of card data
     * @param array $options Grid and card options
     * @return string HTML markup for the cards grid
     */
    public function renderGrid(array $cardsData, array $options = []): string
    {
        $config = $this->normalizeGridOptions($options);
        
        if (empty($cardsData)) {
            return '<div class="' . h($config['emptyClass']) . '">No items found</div>';
        }

        $html = '<div class="' . h($config['gridClass']) . '">';
        foreach ($cardsData as $cardData) {
            $data = $this->normalizeCardData($cardData);
            $html .= '<div class="' . h($config['itemClass']) . '">';
            $html .= $this->buildSingleCard($data, $config);
            $html .= '</div>';
        }
        $html .= '</div>';

        // Add description toggle script
        $html .= $this->buildDescriptionToggleScript();

        return $html;
    }

    /**
     * Render cards with custom container and header.
     *
     * @param array $cardsData Array of card data
     * @param array $options Section options including title, description, container classes
     * @return string HTML markup for the complete section
     */
    public function renderSection(array $cardsData, array $options = []): string
    {
        $config = $this->normalizeSectionOptions($options);
        
        $headerHtml = $this->buildSectionHeader($config);
        $cardsHtml = $this->renderGrid($cardsData, $config);

        return '<section class="' . h($config['sectionClass']) . '">'
            . '<div class="' . h($config['containerClass']) . '">'
            . $headerHtml
            . $cardsHtml
            . '</div>'
            . '</section>';
    }

    private function normalizeOptions(array $options): array
    {
        $defaults = [
            'cardClass' => 'group h-full flex flex-col bg-white rounded-2xl shadow-sm ring-1 ring-slate-200 hover:shadow-lg transition duration-200 overflow-hidden',
            'imageClass' => 'w-full h-48 object-cover',
            'wrapperClass' => '',
        ];
        return $options + $defaults;
    }

    private function normalizeGridOptions(array $options): array
    {
        $defaults = [
            'gridClass' => 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
            'itemClass' => 'flex',
            'emptyClass' => 'flex items-center justify-center h-40 text-sm text-gray-500',
            'cardClass' => 'group h-full flex flex-col bg-white rounded-2xl shadow-sm ring-1 ring-slate-200 hover:shadow-lg transition duration-200 overflow-hidden',
            'imageClass' => 'w-full h-48 object-cover',
        ];
        return $options + $defaults;
    }

    private function normalizeSectionOptions(array $options): array
    {
        $defaults = [
            'sectionClass' => 'w-full py-8',
            'containerClass' => 'max-w-7xl mx-auto px-4 md:px-8 lg:px-12',
            'gridClass' => 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
            'itemClass' => 'flex',
            'emptyClass' => 'flex items-center justify-center h-40 text-sm text-gray-500',
            'cardClass' => 'group h-full flex flex-col bg-white rounded-2xl shadow-sm ring-1 ring-slate-200 hover:shadow-lg transition duration-200 overflow-hidden',
            'imageClass' => 'w-full h-48 object-cover',
            'title' => '',
            'description' => '',
        ];
        return $options + $defaults;
    }

    private function normalizeCardData($cardData): array
    {
        return [
            'image' => is_array($cardData) ? ($cardData['image'] ?? '') : ($cardData->image ?? ''),
            'title' => is_array($cardData) ? ($cardData['title'] ?? '') : ($cardData->title ?? ''),
            'center' => is_array($cardData) ? ($cardData['center'] ?? '') : ($cardData->center ?? ''),
            'rating' => is_array($cardData) ? ($cardData['rating'] ?? 4.5) : ($cardData->rating ?? 4.5),
            'description' => is_array($cardData) ? ($cardData['description'] ?? '') : ($cardData->description ?? ''),
            'date' => is_array($cardData) ? ($cardData['date'] ?? '') : ($cardData->date ?? ''),
            'mode' => is_array($cardData) ? ($cardData['mode'] ?? '') : ($cardData->mode ?? ''),
            'language' => is_array($cardData) ? ($cardData['language'] ?? 'English') : ($cardData->language ?? 'English'),
            'link' => is_array($cardData) ? ($cardData['link'] ?? null) : ($cardData->link ?? null),
            'alt' => is_array($cardData) ? ($cardData['alt'] ?? '') : ($cardData->alt ?? ''),
        ];
    }

    private function buildSingleCard(array $cardData, array $config): string
    {
        $alt = $cardData['alt'] ?: $cardData['title'];
        
        $imageHtml = $cardData['image'] ?
            '<div class="relative">' . $this->Html->image($cardData['image'], ['alt' => h($alt), 'class' => $config['imageClass']]) . '</div>' : '';

        $centerHtml = $cardData['center'] ?
            '<div class="text-sm font-semibold text-rose-700 mb-1">@ ' . h($cardData['center']) . '</div>' : '';

        $titleHtml = $cardData['title'] ?
            '<h3 class="text-xl font-semibold text-slate-800 leading-snug mb-2">' . h($cardData['title']) . '</h3>' : '';

        $ratingHtml = '<div class="flex items-center gap-1 mb-3">' .
            '<span class="inline-flex items-center text-xs font-medium bg-rose-200 text-rose-800 rounded px-1.5 py-0.5">' . h($cardData['rating']) . '</span>' .
            '<svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-amber-500" fill="currentColor" viewBox="0 0 20 20"><path d="M10 15.27L16.18 19l-1.64-7.03L20 7.24l-7.19-.61L10 0 7.19 6.63 0 7.24l5.46 4.73L3.82 19z"/></svg>' .
            '</div>';

        $descriptionHtml = $cardData['description'] ?
            '<div class="mb-5">' .
            '<p class="text-sm text-slate-600 description-text" data-full="' . h($cardData['description']) . '">' .
            '<span class="line-clamp-2">' . h($cardData['description']) . '</span>' .
            '</p>' .
            '<button class="text-xs text-rose-700 hover:text-rose-800 font-medium mt-1 view-toggle" onclick="toggleDescription(this, event)">View more</button>' .
            '</div>' : '';
        
        $metaHtml = '<div class="mt-auto space-y-4 text-sm">';
        if ($cardData['date']) {
            $metaHtml .= '<div class="flex items-start gap-3"><span class="text-rose-700">📅</span><span>' . h($cardData['date']) . '</span></div>';
        }
        if ($cardData['mode']) {
            $metaHtml .= '<div class="flex items-start gap-3"><span class="text-rose-700">🖥️</span><span>' . h($cardData['mode']) . '</span></div>';
        }
        if ($cardData['language']) {
            $metaHtml .= '<div class="flex items-start gap-3"><span class="text-rose-700">🌐</span><span>' . h($cardData['language']) . '</span></div>';
        }
        $metaHtml .= '</div>';
        
        $contentHtml = '<div class="flex flex-col flex-1 px-6 pt-5 pb-4">' .
            $centerHtml . $titleHtml . $ratingHtml . $descriptionHtml . $metaHtml .
            '</div>';
        
        $cardContent = $imageHtml . $contentHtml;
        
        if ($cardData['link']) {
            $cardContent = '<a href="' . h($cardData['link']) . '" class="h-full flex flex-col">' . $cardContent . '</a>';
        }
        
        $wrapperClass = !empty($config['wrapperClass']) ? $config['wrapperClass'] : '';
        $cardHtml = '<div class="' . h($config['cardClass']) . '">' . $cardContent . '</div>';
        
        return !empty($wrapperClass) ? '<div class="' . h($wrapperClass) . '">' . $cardHtml . '</div>' : $cardHtml;
    }

    private function buildSectionHeader(array $config): string
    {
        if (empty($config['title']) && empty($config['description'])) {
            return '';
        }

        $titleHtml = '';
        $descriptionHtml = '';

        if (!empty($config['title'])) {
            $titleHtml = '<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">' . h($config['title']) . '</h2>';
        }

        if (!empty($config['description'])) {
            $descriptionHtml = '<p class="text-lg text-gray-600 mb-8">' . h($config['description']) . '</p>';
        }

        return '<div class="text-center mb-12">' . $titleHtml . $descriptionHtml . '</div>';
    }

    private function buildDescriptionToggleScript(): string
    {
        static $scriptAdded = false;
        if ($scriptAdded) {
            return '';
        }
        $scriptAdded = true;
        
        return '<script>
            function toggleDescription(button, event) {
                // Prevent event bubbling to parent link
                if (event) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                
                const descText = button.previousElementSibling;
                const span = descText.querySelector("span");
                const fullText = descText.getAttribute("data-full");
                
                if (span.classList.contains("line-clamp-2")) {
                    span.classList.remove("line-clamp-2");
                    span.innerHTML = fullText;
                    button.textContent = "View less";
                    descText.style.maxHeight = "none";
                    descText.style.overflow = "visible";
                } else {
                    span.classList.add("line-clamp-2");
                    span.innerHTML = fullText;
                    button.textContent = "View more";
                    descText.style.maxHeight = "";
                    descText.style.overflow = "";
                }
            }
        </script>';
    }
}
