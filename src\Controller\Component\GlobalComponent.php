<?php

namespace App\Controller\Component;

use Aws\Exception\AwsException;
use Aws\Sns\SnsClient;
use Cake\Controller\Component;
use Cake\Mailer\Mailer;
use Cake\Core\Configure;
use Cake\Log\Log;
use Cake\Utility\Security;
use Cake\I18n\FrozenTime;
use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\Routing\Router;
use Cake\ORM\Table;

class GlobalComponent extends Component
{

    public function send_email($to = null, $from = null, $subject = null, $template = null, $viewVars = null, $attachments = null, $cc = null, $layout = null)
    {
        $view_vars = $viewVars ?? [];
        $cc = $cc ?? [];

        if ($to && $subject) {
            $from = $from ?? Configure::read('Settings.ADMIN_EMAIL');

            $Email = new Mailer('default');
            
            
            $Email->setSubject($subject)
                ->setEmailFormat('html')
                ->setTo($to)
                ->setCc($cc)
                ->setFrom($from)
                ->setViewVars($view_vars);

            if ($attachments !== null) {
                $Email->setAttachments($attachments);
            }
            
            $Email->viewBuilder()->setHelpers(['Html', 'Url', 'Text']);

            if ($layout !== null) {
                $Email->viewBuilder()->setTemplate($template)
                    ->setLayout($layout);
            } else {
                $Email->viewBuilder()->setTemplate($template);
            }

            try {
                // $result = $Email->send();
                if($Email->send()){
                    return true;
                } else {
                    return false;
                }
               
            } catch (\Exception $e) {
                \Cake\Log\Log::error('Email sending failed: ' . $e->getMessage());
                return false;
            }
        }
        
        return false;
    }

    function sendOtp($phoneNumber, $otp) {
        $snsClient = new SnsClient([
            'region' => Configure::read ('Settings.SNS_CREGION'),
            'version' => 'latest',
            'credentials' => [
                'key'    => Configure::read ('Settings.SNS_KEY'),
                'secret' => Configure::read ('Settings.SNS_SECRET'),
            ]
        ]);
    
        try {
            $message = "Your OTP code for Homevilla-Yoga login: " . $otp;
    
            $result = $snsClient->publish([
                'Message' => $message,
                'PhoneNumber' => $phoneNumber,
                'MessageAttributes' => [
                    'AWS.SNS.SMS.SenderID' => [
                        'DataType' => 'String',
                        'StringValue' => 'MyAppName'
                    ]
                ],
            ]);
            
            return [
                'success' => true,
                'messageId' => $result['MessageId'],
                'message' => 'OTP sent successfully.'
            ];
    
        } catch (AwsException $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'The OTP could not be sent.'
            ];
        }
    }
    /**
     * <AUTHOR> Jain
     * @Property create random password
     *
     **/
    public function randomPassword()
    {
        $password = "";
        $charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        for ($i = 0; $i < 8; $i++) {
            $random_int = mt_rand();
            $password .= $charset[$random_int % strlen($charset)];
        }
        return $password;
    }

    public function refer_code($limit)
    {
        return substr(base_convert(sha1(uniqid(mt_rand())), 16, 36), 0, $limit);
    }

    public function formatValidationErrors($errors){
        $firstErrorMessage = 'Something went wrong.';
        if (!empty($errors)) {
            foreach ($errors as $field => $msgs) {
                $firstErrorMessage = is_array($msgs) ? reset($msgs) : $msgs;
                break;
            }
        }

        return $firstErrorMessage;
    }

    public function forgotPassword($user, $userTable){
        $adminUser = true;
        if($user->user_type == 'Customer'){
            $adminUser = false;
        }

        $token = Security::hash(Security::randomBytes(25));
        $user->reset_token = $token;
        $user->reset_token_expiry = date('Y-m-d H:i:s');

        if ($userTable->save($user)) {
            $resetLink = Router::url(['controller' => 'Login', 'action' => 'resetPassword', $token, 'lang' => false ], true,);

            if($adminUser){
                $resetLink = Router::url(['prefix' => 'Admin','controller' => 'Users', 'action' => 'resetPassword', $token, 'lang' => false ], true);
            }
    
            $to = trim($user->email);
            $from = Configure::read('Settings.FROM_EMAIL');
            $subject = "Reset your Password for Homevilla-Yoga";
            $template = "forgot_password";

            $viewVars = [
                'token' => $token, 
                'userId' => $user->id, 
                'username' => $user->first_name . ' ' . $user->last_name, 
                'datetime' => date('d-m-Y H:i:s'), 
                'resetLink' => $resetLink
            ];

            try {
                // Add debug logging
                Log::debug('Attempting to send password reset email to: ' . $to);
                Log::debug('Reset link: ' . $resetLink);
                
                $send_email = $this->send_email($to, $from, $subject, $template, $viewVars);
                
                if ($send_email) {
                    Log::info('Password reset email sent successfully to: ' . $to);
                    $message = 'Password reset email sent successfully.';
                    $messageKey = 'success';
                    
                } else {
                    Log::error('Failed to send password reset email');
                    // Try fallback method
                    $fallbackResult = $this->_sendPasswordResetEmailFallback($to, $from, $subject, $viewVars, $resetLink);
                    
                    if ($fallbackResult) {
                        $message = 'Please check your email to reset your password.';
                        $messageKey = 'error';
                    } else {
                        $message = 'Unable to send the password reset email. Please try again.';
                        $messageKey = 'error';
                        
                    }
                }
            } catch (\Exception $e) {
                $message = 'Unable to send the password reset email. Please try again.';
                $messageKey = 'error';
                Log::error('Exception sending password reset email: ' . $e->getMessage());
                
            }
        } else {
            $message = 'Unable to process your request. Please try again.';
            $messageKey = 'error';
        }
    
        $result = [
            'message' => $message ?? '',
            'messageKey' => $messageKey ?? ''
        ];
        
        return  $result;
        
    }

    /**
     * Fallback method to send password reset email directly using Mailer
     */
    protected function _sendPasswordResetEmailFallback($to, $from, $subject, $viewVars, $resetLink)
    {
        try {
            Log::debug('Using fallback method to send password reset email');

            $mailer = new \Cake\Mailer\Mailer('default');
            $mailer->setFrom([$from => 'Yoga.in Password Reset'])
                ->setTo($to)
                ->setSubject($subject)
                ->setViewVars([
                    'token' => $viewVars['token'],
                    'userId' => $viewVars['userId'],
                    'username' => $viewVars['username'],
                    'datetime' => $viewVars['datetime'],
                    'resetLink' => $resetLink
                ]);

            // Set email format
            $mailer->setEmailFormat('both');

            // Set template using viewBuilder
            $mailer->viewBuilder()
                ->setTemplate('forgot_password')
                ->setLayout('default');

            $result = $mailer->deliver();

            if ($result) {
                Log::info('Password reset email sent successfully via fallback method');
                return true;
            } else {
                Log::error('Failed to send password reset email via fallback method');
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Exception in fallback password reset email method: ' . $e->getMessage());
            return false;
        }
    }

    // Reset Password
    public function resetPassword($token, $request, $userTable)
    {
        if (!$token) {
            $message = 'Invalid token. Please try again.';
            $messageKey = 'error';
            $redirectAction = 'forgotPassword';
        }

        $user = $userTable->find('all', [
            'conditions' => [
                'reset_token' => $token,
                'reset_token_expiry >' => FrozenTime::now()->modify('-1 hours')
            ]
        ])->first();

        if (!$user) {
            $message = 'Invalid or expired token. Please try again.';
            $messageKey = 'error';
            $redirectAction = 'forgotPassword';

        } else {
             $adminUser = true;
            if($user->user_type == 'Customer'){
                $adminUser = false;
            }

            if ($request->is(['post', 'put'])) {
                $password = $request->getData('password');
                $confirmPassword = $request->getData('confirm_password');

                if ($password !== $confirmPassword) {
                    $message = 'Passwords do not match. Please try again.';
                    $messageKey = 'error';
                
                } else {
                    $user->password = $password;
                    $user->reset_token = null;
                    $user->reset_token_expiry = null;

                    if ($userTable->save($user)) {
                        $message = 'Your password has been updated successfully.';
                        $messageKey = 'success';
                        $redirectAction = 'login';
                    } else {
                        $message = 'Unable to update your password. Please try again.';
                        $messageKey = 'error';
                    }
                }
            }
        }

        $result = [
            'message' => $message ?? '',
            'messageKey'  => $messageKey ?? '',
            'redirect'  => $redirectAction ?? '',
            'adminUser' => $adminUser ?? ''
        ];
        return  $result;
        
    }
}

?>
