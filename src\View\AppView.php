<?php
declare(strict_types=1);

/**
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link      https://cakephp.org CakePHP(tm) Project
 * @since     3.0.0
 * @license   https://opensource.org/licenses/mit-license.php MIT License
 */
namespace App\View;

use Cake\View\View;

/**
 * Application View
 *
 * Your application's default view class
 *
 * @link https://book.cakephp.org/5/en/views.html#the-app-view
 */
class AppView extends View
{
    /**
     * Initialization hook method.
     *
     * Use this method to add common initialization code like adding helpers.
     *
     * e.g. `$this->addHelper('Html');`
     *
     * @return void
     */
    public function initialize(): void
    {
        $this->loadHelper('Authentication.Identity');
        $this->loadHelper('Paginator', [
            'templates' => [
                'nextActive' => '<li class="paginate_button page-item next" id="table-1_next">
                            <a href="{{url}}" aria-controls="table-1" class="page-link">
                           <!--  <i class="bi bi-chevron-right"></i> -->
                            <i class="fa fa-chevron-right"></i>
                            </a>
                        </li>',
                'nextDisabled' => '<li class="paginate_button page-item next" id="table-1_next">
                            <a href="{{url}}" aria-controls="table-1" class="page-link">
                           <!--  <i class="bi bi-chevron-right"></i> -->
                            <i class="fa fa-chevron-right"></i>
                            </a>
                        </li>',
                'prevActive' => '<li class="paginate_button page-item previous" id="table-1_previous">
                            <a href="{{url}}" aria-controls="table-1" class="page-link">
                          <!-- <i class="bi bi-chevron-left"></i> -->
                            <i class="fa fa-chevron-left"></i>
                            </a>
                        </li>',
                'prevDisabled' => '<li class="paginate_button page-item previous disabled" id="table-1_previous">
                            <a href="{{url}}" aria-controls="table-1" class="page-link">
                           <!-- <i class="bi bi-chevron-left"></i> -->
                            <i class="fa fa-chevron-left"></i>
                            </a>
                        </li>',
                'number' => '<li class="paginate_button page-item">
                            <a href="{{url}}" aria-controls="table-1" class="page-link">{{text}}</a>
                        </li>',
                'current' => '<li class="paginate_button page-item active">
                            <a href="{{url}}" aria-controls="table-1" class="page-link">{{text}}</a>
                        </li>',
                // 'first' => '<li class="page-item"><a class="page-link" href="{{url}}">First</a></li>',
                // 'last' => '<li class="page-item"><a class="page-link" href="{{url}}">Last</a></li>',
            ]
        ]);
        $this->loadHelper('Breadcrumbs');
        $this->loadHelper('Slider');
        $this->loadHelper('Card');
    }
}
