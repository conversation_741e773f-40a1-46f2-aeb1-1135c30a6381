<?php
use Cake\Core\Configure;

$cakeDescription = 'Homevilla-Yoga';
?>
<!DOCTYPE html>
<html>

<head>
<title>HomeVilla Yoga</title>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
<meta name="keyword" content="">
<meta name="description" content="">
<script src="https://unpkg.com/@tailwindcss/browser@4"></script>
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.14.8/dist/cdn.min.js"></script>

<link rel="icon" href=" <?= $this->Url->webroot('img/yoga-logo.png'); ?>">
<link href='https://fonts.googleapis.com/css?family=Open Sans' rel='stylesheet'>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Afacad:ital,wght@0,400..700;1,400..700&family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=DM+Serif+Text:ital@0;1&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Outfit:wght@100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/lipis/flag-icons@7.2.3/css/flag-icons.min.css"/>
</head>

<body>
    <?= $this->fetch('content') ?>
    <script src="<?= $this->Url->webroot('js/app.min.js'); ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js'); ?>"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <?= $this->fetch('script') ?>
</body>
</html>
