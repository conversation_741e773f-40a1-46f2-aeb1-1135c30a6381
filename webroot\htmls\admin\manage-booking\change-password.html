<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>

<body>


    <section class="select-branch bg-[] select-classes book-course-content">
        <div class="container mx-auto  px-4">
            <div class="w-full  p-6">
                <h2 class="text-center text-[24px] font-semibold text-gray-800 mb-6 title-change-pass">Change Password
                </h2>

                <!-- Create Password -->
                <div class="mb-4 relative body">
                    <label class="block text-gray-700 text-sm mb-1 crt-pass">Create Password</label>
                    <input type="password" placeholder=""
                        class="w-full border border-gray-300 rounded-full px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <button type="button" class="absolute right-3 top-10 text-gray-500">
                        <!-- Eye Icon -->
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 
            4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                    </button>
                </div>

                <!-- Re-enter Password -->
                <div class="mb-6 relative body">
                    <label class="block text-gray-700 text-sm mb-1 crt-pass">Re-enter Password</label>
                    <input type="password" placeholder=""
                        class="w-full border border-gray-300 rounded-full px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <button type="button" class="absolute right-3 top-10 text-gray-500">
                        <!-- Eye Slash Icon -->
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.477 
            0-8.268-2.943-9.542-7a9.967 9.967 0 012.223-3.592M6.18 
            6.18A9.953 9.953 0 0112 5c4.477 0 8.268 2.943 9.542 
            7a9.965 9.965 0 01-4.073 5.093M15 12a3 3 0 11-6 
            0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3l18 18" />
                        </svg>
                    </button>
                </div>

                <!-- Button -->
                <button
                    class="w-full bg-[#D87A61] text-white font-[700] py-2 rounded-md hover:bg-[#c46d54] transition">
                    GENERATE OTP
                </button>
            </div>
            <div class="w-full bg-white p-6 body">
                <!-- Title -->
                <label class="block text-gray-800 crt-pass text-sm font-medium mb-3">
                    Enter Verification Code
                </label>

                <!-- OTP Inputs -->
                <div class="flex gap-2 mb-4 items-center justify-between">
                    <input type="text" maxlength="1"
                        class="w-16 h-12 border border-[#6C6C6C] rounded-xl text-center text-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <input type="text" maxlength="1"
                        class="w-16 h-12 border border-[#6C6C6C] rounded-xl text-center text-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <input type="text" maxlength="1"
                        class="w-16 h-12 border border-[#6C6C6C] rounded-xl text-center text-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <input type="text" maxlength="1"
                        class="w-16 h-12 border border-[#6C6C6C] rounded-xl text-center text-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <!-- Timer -->
                <div class="text-center text-[#BE1F2E] font-bold text-lg mb-2">
                    05:00
                </div>

                <!-- Resend -->
                <div class="text-center text-[16px] mb-1">
                    Didn’t receive a code?
                    <button class="text-[#D87A61] font-medium hover:underline">Resend</button>
                </div>

                <!-- Info -->
                <div class="text-center text-[12px] text-[#313131] mb-5">
                    Verification Code Sent to <span class="font-bold">+91 7904330323</span>
                </div>

                <!-- Button -->
                <button
                    class="w-full bg-[#D87A61] text-white font-[700] py-2 rounded-md hover:bg-[#c46d54] transition">
                    CHANGE PASSWORD
                </button>
            </div>
        </div>
    </section>


</body>


<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

    .title-change-pass {
        color: #293148;
        font-weight: 700;
        font-size: 24px;
        font-family: "Open Sans", sans-serif;
    }

    .select-branch .body .crt-pass {
        font-family: "Open Sans", sans-serif;
        color: #293148;
        font-weight: 600;
        font-size: 16px;
    }
</style>

</html>